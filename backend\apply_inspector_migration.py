#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to apply inspector migration manually
"""
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.core.management import execute_from_command_line

if __name__ == '__main__':
    try:
        print("Applying inspector migration...")
        execute_from_command_line(['manage.py', 'migrate', 'inventory', '0049_add_inspector_fields'])
        print("Migration applied successfully!")
    except Exception as e:
        print(f"Error applying migration: {e}")
        
    try:
        print("Creating inspector group...")
        execute_from_command_line(['manage.py', 'create_inspector_group'])
        print("Inspector group created successfully!")
    except Exception as e:
        print(f"Error creating inspector group: {e}")
