@echo off
echo Django Migration Application Script
echo ===================================

echo.
echo Step 1: Checking current migration status...
python manage.py showmigrations inventory

echo.
echo Step 2: Applying inspector fields migration (0049)...
python manage.py migrate inventory 0049

echo.
echo Step 3: Applying tags migration (0050)...
python manage.py migrate inventory 0050

echo.
echo Step 4: Applying audit trail migration (0051)...
python manage.py migrate inventory 0051

echo.
echo Step 5: Applying all remaining migrations...
python manage.py migrate

echo.
echo Step 6: Final migration status check...
python manage.py showmigrations inventory

echo.
echo ===================================
echo Migration process completed!
echo.
echo Next steps:
echo 1. Uncomment the tags field in inventory/models/items.py
echo 2. Uncomment the tags admin configuration
echo 3. Restart the Django server
echo ===================================
pause
