#!/usr/bin/env python
"""
Simple script to apply migrations step by step
"""

import os
import sys
import subprocess

def run_command(command, description):
    """Run a command and return success status"""
    print(f"\n{'='*50}")
    print(f"Running: {description}")
    print(f"Command: {command}")
    print('='*50)
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.stdout:
            print("STDOUT:")
            print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"✓ {description} completed successfully")
            return True
        else:
            print(f"✗ {description} failed with return code {result.returncode}")
            return False
            
    except Exception as e:
        print(f"✗ Error running {description}: {e}")
        return False

def main():
    """Main migration process"""
    print("Django Migration Application Script")
    print("This will apply migrations step by step")
    
    # Change to backend directory
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    # Step 1: Check current migration status
    if not run_command("python manage.py showmigrations inventory", "Check migration status"):
        print("Warning: Could not check migration status, continuing anyway...")
    
    # Step 2: Apply inspector fields migration
    if not run_command("python manage.py migrate inventory 0049", "Apply inspector fields migration"):
        print("Inspector fields migration failed, but continuing...")
    
    # Step 3: Apply tags migration
    if not run_command("python manage.py migrate inventory 0050", "Apply tags migration"):
        print("Tags migration failed, but continuing...")
    
    # Step 4: Apply audit trail migration
    if not run_command("python manage.py migrate inventory 0051", "Apply audit trail migration"):
        print("Audit trail migration failed, but continuing...")
    
    # Step 5: Apply all remaining migrations
    if not run_command("python manage.py migrate", "Apply all remaining migrations"):
        print("Some migrations may have failed")
    
    # Step 6: Check final status
    run_command("python manage.py showmigrations inventory", "Check final migration status")
    
    print(f"\n{'='*50}")
    print("Migration process completed!")
    print("Next steps:")
    print("1. Uncomment the tags field in inventory/models/items.py")
    print("2. Uncomment the tags admin configuration")
    print("3. Restart the Django server")
    print('='*50)

if __name__ == '__main__':
    main()
