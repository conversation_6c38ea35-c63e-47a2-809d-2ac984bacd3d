@echo off
echo 🔧 Applying Django Migrations
echo ==================================================

echo.
echo 📋 Checking current migration status...
python manage.py showmigrations inventory

echo.
echo 🚀 Applying all pending migrations...
python manage.py migrate

echo.
echo ✅ Checking final migration status...
python manage.py showmigrations inventory

echo.
echo ==================================================
echo ✅ MIGRATIONS APPLIED SUCCESSFULLY!
echo ✅ The 500 error should now be fixed.
echo ✅ You can restart the Django server.
echo ==================================================

echo.
echo Press any key to exit...
pause > nul
