#!/usr/bin/env python
"""
Script to check and apply migrations safely
"""

import os
import sys
import django
from django.conf import settings
from django.core.management import execute_from_command_line

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.core.management import call_command
from django.db import connection
from django.apps import apps

def check_migrations():
    """Check migration status"""
    print("=== Checking Migration Status ===")
    
    try:
        # Check if migrations are applied
        call_command('showmigrations', 'inventory', verbosity=1)
        print("\n=== Migration Status Check Complete ===")
        return True
    except Exception as e:
        print(f"Error checking migrations: {e}")
        return False

def apply_migrations():
    """Apply pending migrations"""
    print("\n=== Applying Migrations ===")
    
    try:
        # Apply migrations
        call_command('migrate', 'inventory', verbosity=2)
        print("\n=== Migrations Applied Successfully ===")
        return True
    except Exception as e:
        print(f"Error applying migrations: {e}")
        return False

def check_models():
    """Check if models are properly loaded"""
    print("\n=== Checking Models ===")
    
    try:
        from inventory.models import ItemTag, ItemMaster, AuditTrail
        
        # Check if ItemTag model exists
        print(f"✓ ItemTag model loaded: {ItemTag}")
        
        # Check if ItemMaster has tags field
        if hasattr(ItemMaster, 'tags'):
            print(f"✓ ItemMaster.tags field exists")
        else:
            print("✗ ItemMaster.tags field missing")
            
        # Check if AuditTrail model exists
        print(f"✓ AuditTrail model loaded: {AuditTrail}")
        
        return True
    except ImportError as e:
        print(f"✗ Error importing models: {e}")
        return False
    except Exception as e:
        print(f"✗ Error checking models: {e}")
        return False

def main():
    """Main function"""
    print("Django Migration Checker")
    print("=" * 50)
    
    # Check current status
    if not check_migrations():
        print("Failed to check migration status")
        return False
    
    # Apply migrations
    if not apply_migrations():
        print("Failed to apply migrations")
        return False
    
    # Check models
    if not check_models():
        print("Model check failed")
        return False
    
    print("\n" + "=" * 50)
    print("✓ All checks passed! System is ready.")
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
