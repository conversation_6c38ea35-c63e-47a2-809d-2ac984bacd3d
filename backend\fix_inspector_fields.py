#!/usr/bin/env python
"""
Script to add inspector fields to the database manually
"""
import os
import sys
import django
import sqlite3

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.db import connection

def add_inspector_fields():
    """Add inspector fields to ItemEntryRequestItem table"""
    try:
        with connection.cursor() as cursor:
            # Check if the fields already exist
            cursor.execute("PRAGMA table_info(inventory_itementryrequestitem)")
            columns = [row[1] for row in cursor.fetchall()]
            
            print(f"Current columns: {columns}")
            
            # Add assigned_inspector field if it doesn't exist
            if 'assigned_inspector_id' not in columns:
                print("Adding assigned_inspector_id field...")
                cursor.execute("""
                    ALTER TABLE inventory_itementryrequestitem 
                    ADD COLUMN assigned_inspector_id INTEGER NULL 
                    REFERENCES auth_user(id)
                """)
                print("assigned_inspector_id field added successfully!")
            else:
                print("assigned_inspector_id field already exists")
            
            # Add inspection_status field if it doesn't exist
            if 'inspection_status' not in columns:
                print("Adding inspection_status field...")
                cursor.execute("""
                    ALTER TABLE inventory_itementryrequestitem 
                    ADD COLUMN inspection_status VARCHAR(20) DEFAULT 'not_required'
                """)
                print("inspection_status field added successfully!")
            else:
                print("inspection_status field already exists")
            
            # Add inspection_notes field if it doesn't exist
            if 'inspection_notes' not in columns:
                print("Adding inspection_notes field...")
                cursor.execute("""
                    ALTER TABLE inventory_itementryrequestitem 
                    ADD COLUMN inspection_notes TEXT DEFAULT ''
                """)
                print("inspection_notes field added successfully!")
            else:
                print("inspection_notes field already exists")
            
            # Add inspection_date field if it doesn't exist
            if 'inspection_date' not in columns:
                print("Adding inspection_date field...")
                cursor.execute("""
                    ALTER TABLE inventory_itementryrequestitem 
                    ADD COLUMN inspection_date DATETIME NULL
                """)
                print("inspection_date field added successfully!")
            else:
                print("inspection_date field already exists")
                
            print("All inspector fields have been added successfully!")
            
    except Exception as e:
        print(f"Error adding inspector fields: {e}")

def create_inspector_group():
    """Create Inspector group"""
    try:
        from django.contrib.auth.models import Group, Permission
        from django.contrib.contenttypes.models import ContentType
        
        # Create Inspector group
        inspector_group, created = Group.objects.get_or_create(name='Inspector')
        
        if created:
            print("Inspector group created successfully!")
        else:
            print("Inspector group already exists")
            
        # Try to add some basic permissions
        try:
            from inventory.models.inspection import InspectionRequest
            inspection_ct = ContentType.objects.get_for_model(InspectionRequest)
            
            # Add view permission
            view_perm, _ = Permission.objects.get_or_create(
                codename='view_inspectionrequest',
                name='Can view inspection request',
                content_type=inspection_ct
            )
            inspector_group.permissions.add(view_perm)
            print("Added inspection permissions to Inspector group")
            
        except Exception as perm_error:
            print(f"Could not add permissions: {perm_error}")
            
    except Exception as e:
        print(f"Error creating inspector group: {e}")

if __name__ == '__main__':
    print("Starting inspector fields setup...")
    add_inspector_fields()
    create_inspector_group()
    print("Inspector setup completed!")
