from django.contrib import admin
from django.utils import timezone
from .models import (
    # Organization
    OrganizationType, Organization, Office,
    # Classification
    MainClassification, SubClassification, EntryMode,
    # Specifications
    ItemType, ItemCategory, ItemBrand, ItemManufacturer,
    ItemQuality, ItemShape, ItemSize, UnitOfMeasure,
    # Status
    ItemStatus, PropertyStatus, ApprovalStatus,
    # Storage
    StoreType, Store, Shelf,
    # Suppliers
    Supplier,
    # Gate Pass
    GatePass,
    # Items
    ItemMaster, Batch, Item,
    # Reports
    DiscrepancyType, DamageReport, ReportItem,
    # Requisitions
    RequisitionStatus, ItemRequisition, RequisitionItem, RequisitionAuditLog,
    # Inspection
    InspectionCommittee, InspectionRequest, InspectionResult, InspectionItem, InspectionEvidence,
    # Entry Request
    ItemEntryRequest, ItemEntryRequestAttachment, ItemEntryRequestItem,
    # Receiving
    Model19Receipt, Model19Item,
    # Tags
    ItemTag,
    # Audit
    AuditTrail, WorkflowAuditTrail, SystemMetrics
)

# Organization Admin
@admin.register(OrganizationType)
class OrganizationTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'created_at', 'is_active')
    search_fields = ('name',)
    list_filter = ('is_active',)

@admin.register(Organization)
class OrganizationAdmin(admin.ModelAdmin):
    list_display = ('name', 'shortcode', 'org_type', 'phone', 'email', 'is_active')
    search_fields = ('name', 'shortcode', 'email')
    list_filter = ('org_type', 'is_active')
    raw_id_fields = ('parent',)

@admin.register(Office)
class OfficeAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'organization', 'parent_office', 'is_active')
    search_fields = ('name', 'code')
    list_filter = ('organization', 'is_active')
    raw_id_fields = ('organization', 'parent_office')

# Classification Admin
@admin.register(MainClassification)
class MainClassificationAdmin(admin.ModelAdmin):
    list_display = ('code', 'name', 'is_active')
    search_fields = ('code', 'name')
    list_filter = ('is_active',)

@admin.register(SubClassification)
class SubClassificationAdmin(admin.ModelAdmin):
    list_display = ('code', 'name', 'main_class', 'is_active')
    search_fields = ('code', 'name')
    list_filter = ('main_class', 'is_active')

@admin.register(EntryMode)
class EntryModeAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_active')
    search_fields = ('name',)
    list_filter = ('is_active',)

# Specifications Admin
@admin.register(ItemType)
class ItemTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_active')
    search_fields = ('name',)
    list_filter = ('is_active',)

@admin.register(ItemCategory)
class ItemCategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_active')
    search_fields = ('name',)
    list_filter = ('is_active',)

@admin.register(ItemBrand)
class ItemBrandAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_active')
    search_fields = ('name',)
    list_filter = ('is_active',)

@admin.register(ItemManufacturer)
class ItemManufacturerAdmin(admin.ModelAdmin):
    list_display = ('name', 'country', 'website', 'is_active')
    search_fields = ('name', 'country')
    list_filter = ('country', 'is_active')

@admin.register(ItemQuality)
class ItemQualityAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_active')
    search_fields = ('name',)
    list_filter = ('is_active',)

@admin.register(ItemShape)
class ItemShapeAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_active')
    search_fields = ('name',)
    list_filter = ('is_active',)

@admin.register(ItemSize)
class ItemSizeAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_active')
    search_fields = ('name',)
    list_filter = ('is_active',)

@admin.register(UnitOfMeasure)
class UnitOfMeasureAdmin(admin.ModelAdmin):
    list_display = ('name', 'symbol', 'is_active')
    search_fields = ('name', 'symbol')
    list_filter = ('is_active',)

# Status Admin
@admin.register(ItemStatus)
class ItemStatusAdmin(admin.ModelAdmin):
    list_display = ('code', 'name', 'color_code', 'is_active')
    search_fields = ('code', 'name')
    list_filter = ('is_active',)

@admin.register(PropertyStatus)
class PropertyStatusAdmin(admin.ModelAdmin):
    list_display = ('name', 'color_code', 'is_active')
    search_fields = ('name',)
    list_filter = ('is_active',)

@admin.register(ApprovalStatus)
class ApprovalStatusAdmin(admin.ModelAdmin):
    list_display = ('name', 'color_code', 'is_active')
    search_fields = ('name',)
    list_filter = ('is_active',)

# Storage Admin
@admin.register(StoreType)
class StoreTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_active')
    search_fields = ('name',)
    list_filter = ('is_active',)

@admin.register(Store)
class StoreAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'store_type', 'organization', 'location', 'is_active')
    search_fields = ('name', 'code', 'location')
    list_filter = ('store_type', 'organization', 'is_active')
    raw_id_fields = ('organization',)

@admin.register(Shelf)
class ShelfAdmin(admin.ModelAdmin):
    list_display = ('__str__', 'code', 'row', 'column', 'store', 'is_active')
    search_fields = ('code', 'store__name')
    list_filter = ('store', 'is_active')
    raw_id_fields = ('store',)

# Supplier Admin
@admin.register(Supplier)
class SupplierAdmin(admin.ModelAdmin):
    list_display = ('company_name', 'contact_person', 'email', 'phone', 'country', 'is_active')
    search_fields = ('company_name', 'contact_person', 'email', 'phone')
    list_filter = ('country', 'is_active')

# Gate Pass Admin
@admin.register(GatePass)
class GatePassAdmin(admin.ModelAdmin):
    list_display = ('gate_code', 'gate_name', 'issued_to', 'issue_date', 'is_returned')
    search_fields = ('gate_code', 'gate_name', 'issued_to')
    list_filter = ('is_returned', 'issue_date')
    raw_id_fields = ('item', 'authorized_by', 'issued_by', 'return_verified_by')

# Items Admin
@admin.register(ItemMaster)
class ItemMasterAdmin(admin.ModelAdmin):
    list_display = ('stock_classification', 'name', 'sub_class', 'item_type', 'brand', 'current_stock', 'is_active')
    search_fields = ('stock_classification', 'name', 'description', 'model')
    list_filter = ('sub_class', 'item_type', 'item_category', 'brand', 'is_active')
    raw_id_fields = ('sub_class', 'item_type', 'item_category', 'brand', 'manufacturer')
    readonly_fields = ('item_code', 'stock_classification', 'current_stock')
    filter_horizontal = ('tags',)
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description', 'model', 'is_active')
        }),
        ('Classifications', {
            'fields': ('sub_class', 'item_type', 'item_category', 'entry_mode')
        }),
        ('Specifications', {
            'fields': ('brand', 'manufacturer', 'size', 'shape')
        }),
        ('Units and Pricing', {
            'fields': ('unit_of_measure', 'unit_price', 'depreciation_rate')
        }),
        ('Stock Control', {
            'fields': ('min_stock', 'max_stock', 'abc_class')
        }),
        ('Tags', {
            'fields': ('tags',)
        }),
        ('System Fields', {
            'fields': ('item_code', 'stock_classification'),
            'classes': ('collapse',)
        }),
    )

@admin.register(Batch)
class BatchAdmin(admin.ModelAdmin):
    list_display = ('master_item', 'supplier', 'quantity', 'remaining_quantity', 'invoice_no', 'series', 'received_date')
    search_fields = ('invoice_no', 'master_item__name', 'supplier__company_name', 'series')
    list_filter = ('received_date', 'supplier', 'approval_status')
    raw_id_fields = ('master_item', 'supplier', 'received_by')
    readonly_fields = ('remaining_quantity',)
    fieldsets = (
        ('Basic Information', {
            'fields': ('master_item', 'supplier', 'invoice_no', 'series')
        }),
        ('Quantity and Price', {
            'fields': ('quantity', 'purchase_price')
        }),
        ('Dates', {
            'fields': ('received_date', 'expiry_date', 'warranty_months')
        }),
        ('Model 22 Form Fields', {
            'fields': ('page_from', 'page_to')
        }),
        ('Status', {
            'fields': ('approval_status', 'received_by')
        }),
    )

@admin.register(Item)
class ItemAdmin(admin.ModelAdmin):
    list_display = ('registry_no', 'master', 'serial_number', 'series', 'status', 'store', 'received_date')
    search_fields = ('registry_no', 'serial_number', 'barcode', 'series', 'master__name')
    list_filter = ('status', 'property_status', 'approval_status', 'store')
    raw_id_fields = ('master', 'batch', 'store', 'shelf')
    readonly_fields = ('registry_no', 'barcode')
    fieldsets = (
        ('Item Information', {
            'fields': ('master', 'batch', 'serial_number', 'series')
        }),
        ('Status', {
            'fields': ('status', 'property_status', 'approval_status')
        }),
        ('Location', {
            'fields': ('store', 'shelf', 'page_from', 'page_to')
        }),
        ('Lifecycle', {
            'fields': ('received_date', 'expiry_date')
        }),
        ('System Fields', {
            'fields': ('registry_no', 'barcode'),
            'classes': ('collapse',)
        }),
    )

# Tags Admin - Temporarily commented out until migration is applied
# @admin.register(ItemTag)
# class ItemTagAdmin(admin.ModelAdmin):
#     list_display = ('name', 'tag_type', 'color', 'priority', 'is_active')
#     search_fields = ('name', 'description')
#     list_filter = ('tag_type', 'color', 'is_active')
#     readonly_fields = ('slug',)
#     actions = ['create_predefined_tags', 'activate_tags', 'deactivate_tags']
#
#     fieldsets = (
#         ('Basic Information', {
#             'fields': ('name', 'slug', 'tag_type', 'description', 'is_active')
#         }),
#         ('Display Settings', {
#             'fields': ('color', 'icon', 'priority')
#         }),
#     )
#
#     def create_predefined_tags(self, request, queryset):
#         """Create predefined tags"""
#         ItemTag.create_predefined_tags()
#         self.message_user(request, "Predefined tags have been created.")
#     create_predefined_tags.short_description = "Create predefined tags"
#
#     def activate_tags(self, request, queryset):
#         updated = queryset.update(is_active=True)
#         self.message_user(request, f"{updated} tags activated.")
#     activate_tags.short_description = "Activate selected tags"
#
#     def deactivate_tags(self, request, queryset):
#         updated = queryset.update(is_active=False)
#         self.message_user(request, f"{updated} tags deactivated.")
#     deactivate_tags.short_description = "Deactivate selected tags"

# Reports Admin
@admin.register(DiscrepancyType)
class DiscrepancyTypeAdmin(admin.ModelAdmin):
    list_display = ('code', 'name', 'requires_amount', 'is_active')
    search_fields = ('code', 'name')
    list_filter = ('requires_amount', 'is_active')

class ReportItemInline(admin.TabularInline):
    model = ReportItem
    extra = 1

@admin.register(DamageReport)
class DamageReportAdmin(admin.ModelAdmin):
    list_display = ('reference_number', 'title', 'supplier', 'status', 'report_date', 'reported_by')
    search_fields = ('reference_number', 'title', 'description', 'purchase_order')
    list_filter = ('status', 'report_date', 'supplier', 'discrepancy_type')
    readonly_fields = ('reference_number', 'report_date', 'date_reported')
    raw_id_fields = ('supplier', 'discrepancy_type', 'inspection_result', 'entry_request',
                    'reported_by', 'prepared_by', 'verified_by', 'approved_by')
    inlines = [ReportItemInline]
    actions = ['mark_as_submitted', 'mark_as_verified', 'mark_as_approved']

    fieldsets = (
        ('Basic Information', {
            'fields': ('reference_number', 'title', 'description', 'recipient', 'report_date', 'date_reported')
        }),
        ('Supplier and Purchase Information', {
            'fields': ('supplier', 'purchase_order', 'discrepancy_type')
        }),
        ('Related Records', {
            'fields': ('inspection_result', 'entry_request'),
            'classes': ('collapse',),
        }),
        ('Store Information', {
            'fields': ('store_keeper',)
        }),
        ('Workflow', {
            'fields': ('status', 'reported_by', 'prepared_by', 'verified_by', 'approved_by', 'remarks')
        }),
    )

    def mark_as_submitted(self, request, queryset):
        for report in queryset.filter(status='draft'):
            report.submit(request.user)
        self.message_user(request, f"{queryset.filter(status='draft').count()} reports marked as submitted.")
    mark_as_submitted.short_description = "Mark selected reports as submitted"

    def mark_as_verified(self, request, queryset):
        for report in queryset.filter(status='submitted'):
            report.verify(request.user)
        self.message_user(request, f"{queryset.filter(status='submitted').count()} reports marked as verified.")
    mark_as_verified.short_description = "Mark selected reports as verified"

    def mark_as_approved(self, request, queryset):
        for report in queryset.filter(status='verified'):
            report.approve(request.user)
        self.message_user(request, f"{queryset.filter(status='verified').count()} reports marked as approved.")
    mark_as_approved.short_description = "Mark selected reports as approved"

@admin.register(ReportItem)
class ReportItemAdmin(admin.ModelAdmin):
    list_display = ('report', 'item_number', 'description', 'discrepancy_type', 'quantity_advised', 'discrepancy_amount')
    search_fields = ('description',)
    list_filter = ('discrepancy_type',)
    raw_id_fields = ('report', 'discrepancy_type')

# Requisitions Admin
@admin.register(RequisitionStatus)
class RequisitionStatusAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_active')
    search_fields = ('name',)
    list_filter = ('is_active',)

class RequisitionItemInline(admin.TabularInline):
    model = RequisitionItem
    extra = 1

class RequisitionAuditLogInline(admin.TabularInline):
    model = RequisitionAuditLog
    extra = 0
    readonly_fields = ('user', 'action', 'previous_status', 'new_status', 'comments', 'created_at')
    can_delete = False
    max_num = 0

@admin.register(ItemRequisition)
class ItemRequisitionAdmin(admin.ModelAdmin):
    list_display = ('receipt_number', 'request_date', 'requesting_office', 'requesting_manager', 'status', 'handover_date')
    search_fields = ('receipt_number', 'requesting_manager', 'designated_recipient')
    list_filter = ('status', 'request_date', 'handover_date', 'requesting_office')
    raw_id_fields = ('requesting_office', 'property_admin', 'handover_confirmed_by')
    readonly_fields = ('receipt_number',)
    inlines = [RequisitionItemInline, RequisitionAuditLogInline]
    fieldsets = (
        ('Request Information', {
            'fields': ('receipt_number', 'request_date', 'requesting_office', 'requesting_manager', 'designated_recipient')
        }),
        ('Status and Review', {
            'fields': ('status', 'property_admin', 'admin_comments')
        }),
        ('Handover', {
            'fields': ('handover_date', 'handover_confirmed_by')
        }),
    )

@admin.register(RequisitionItem)
class RequisitionItemAdmin(admin.ModelAdmin):
    list_display = ('requisition', 'description', 'quantity_requested', 'quantity_approved', 'quantity_handed_over')
    search_fields = ('description', 'model')
    list_filter = ('requisition__status',)
    raw_id_fields = ('requisition',)

@admin.register(RequisitionAuditLog)
class RequisitionAuditLogAdmin(admin.ModelAdmin):
    list_display = ('requisition', 'action', 'user', 'created_at', 'previous_status', 'new_status')
    search_fields = ('requisition__receipt_number', 'user__username', 'action', 'comments')
    list_filter = ('action', 'created_at')
    raw_id_fields = ('requisition', 'user', 'previous_status', 'new_status')
    readonly_fields = ('created_at',)

# Inspection Admin
@admin.register(InspectionCommittee)
class InspectionCommitteeAdmin(admin.ModelAdmin):
    list_display = ('title', 'get_classifications', 'get_user_count', 'is_active')
    search_fields = ('title', 'description')
    list_filter = ('main_classifications', 'is_active')
    filter_horizontal = ('users', 'main_classifications')

    def get_user_count(self, obj):
        return obj.users.count()
    get_user_count.short_description = 'Members'

    def get_classifications(self, obj):
        return ", ".join([f"{c.code}" for c in obj.main_classifications.all()])
    get_classifications.short_description = 'Classifications'



    def assign_to_committee(self, request, queryset):
        """Assign selected inspection requests to a committee"""
        # This would typically be implemented with a custom admin action form
        # For now, we'll just update the status
        updated = queryset.filter(status='pending').update(status='in_progress')
        self.message_user(request, f"{updated} inspection requests marked as in progress.")
    assign_to_committee.short_description = "Assign to committee"

    def mark_as_completed(self, request, queryset):
        """Mark selected inspection requests as completed"""
        updated = queryset.filter(status='in_progress').update(
            status='completed',
            completed_date=timezone.now()
        )
        self.message_user(request, f"{updated} inspection requests marked as completed.")
    mark_as_completed.short_description = "Mark as completed"

class InspectionItemInline(admin.TabularInline):
    model = InspectionItem
    extra = 0
    fields = ('item_code', 'item_description', 'item_master', 'quantity_ordered', 'quantity_received',
              'condition', 'spec_match', 'status', 'discrepancy_type', 'quarantine_required', 'lab_test_required')
    raw_id_fields = ('item_master', 'discrepancy_type')

class InspectionEvidenceInline(admin.TabularInline):
    model = InspectionEvidence
    extra = 0
    fields = ('title', 'file', 'description', 'inspection_item')
    raw_id_fields = ('inspection_item',)

@admin.register(InspectionRequest)
class InspectionRequestAdmin(admin.ModelAdmin):
    list_display = ('form_number', 'title', 'po_number', 'supplier', 'committee', 'store', 'requested_by', 'status', 'created_at')
    search_fields = ('form_number', 'title', 'po_number', 'description')
    list_filter = ('status', 'committee', 'supplier', 'store', 'external_packaging_condition',
                  'technical_inspection_required', 'requires_followup')
    raw_id_fields = ('supplier', 'committee', 'requested_by', 'approved_by', 'batch', 'store', 'entry_request')
    readonly_fields = ('created_at', 'completed_date')
    actions = ['assign_to_committee', 'mark_as_completed']
    inlines = [InspectionItemInline, InspectionEvidenceInline]
    fieldsets = (
        ('Form Information', {
            'fields': ('form_number', 'title', 'description', 'status')
        }),
        ('Delivery Information', {
            'fields': ('po_number', 'delivery_note_number', 'delivery_date', 'supplier', 'bid_documents', 'delivery_note')
        }),
        ('External Packaging', {
            'fields': ('external_packaging_verified', 'external_packaging_condition', 'packaging_condition_matches')
        }),
        ('Technical Inspection', {
            'fields': ('technical_inspection_required', 'technical_inspector', 'technical_test_results')
        }),
        ('Follow-up Inspection', {
            'fields': ('requires_followup', 'followup_date', 'followup_notes')
        }),
        ('Store Information', {
            'fields': ('store',)
        }),
        ('User Information', {
            'fields': ('requested_by', 'inspected_by', 'approved_by', 'approval_date', 'approval_comments')
        }),
        ('Related Records', {
            'fields': ('entry_request', 'batch')
        }),
        ('Scheduling', {
            'fields': ('scheduled_date', 'completed_date')
        }),
    )

@admin.register(InspectionResult)
class InspectionResultAdmin(admin.ModelAdmin):
    list_display = ('inspection_request', 'result', 'inspection_date', 'has_evidence_photos')
    search_fields = ('inspection_request__title', 'comments', 'findings')
    list_filter = ('result', 'inspection_date', 'has_evidence_photos')
    raw_id_fields = ('inspection_request',)
    filter_horizontal = ('inspected_by',)
    readonly_fields = ('inspection_date',)
    fieldsets = (
        ('Basic Information', {
            'fields': ('inspection_request', 'result', 'findings', 'comments')
        }),
        ('Evidence', {
            'fields': ('has_evidence_photos',)
        }),
        ('Inspection Committee', {
            'fields': ('inspected_by', 'inspection_date')
        }),
        ('Signatures', {
            'fields': ('inspector_signature', 'inspector_signature_date',
                      'approver_signature', 'approver_signature_date')
        }),
    )

@admin.register(InspectionItem)
class InspectionItemAdmin(admin.ModelAdmin):
    list_display = ('item_code', 'item_description', 'inspection_request', 'quantity_ordered',
                   'quantity_received', 'condition', 'spec_match', 'status')
    search_fields = ('item_code', 'item_description', 'inspection_request__title')
    list_filter = ('status', 'condition', 'spec_match', 'quarantine_required', 'lab_test_required')
    raw_id_fields = ('inspection_request', 'item_master', 'discrepancy_type')
    fieldsets = (
        ('Basic Information', {
            'fields': ('inspection_request', 'item_code', 'item_description', 'item_master')
        }),
        ('Quantities', {
            'fields': ('quantity_ordered', 'quantity_received')
        }),
        ('Inspection Details', {
            'fields': ('condition', 'spec_match', 'status')
        }),
        ('Discrepancy Information', {
            'fields': ('discrepancy_type', 'discrepancy_notes')
        }),
        ('Special Handling', {
            'fields': ('quarantine_required', 'lab_test_required', 'technical_inspection_notes')
        }),
    )
    inlines = [InspectionEvidenceInline]

@admin.register(InspectionEvidence)
class InspectionEvidenceAdmin(admin.ModelAdmin):
    list_display = ('title', 'inspection_request', 'inspection_item', 'created_at')
    search_fields = ('title', 'description', 'inspection_request__title')
    list_filter = ('created_at',)
    raw_id_fields = ('inspection_request', 'inspection_item')

# Entry Request Admin
class ItemEntryRequestItemInline(admin.TabularInline):
    model = ItemEntryRequestItem
    extra = 1
    fields = ('item_description', 'specifications', 'quantity', 'unit_price', 'main_classification',
              'is_received', 'received_quantity', 'received_date')
    raw_id_fields = ('main_classification',)

class ItemEntryRequestAttachmentInline(admin.TabularInline):
    model = ItemEntryRequestAttachment
    extra = 1
    fields = ('file_name', 'file_path', 'file_type', 'file_size', 'description',
              'attachment_type', 'is_confidential', 'uploaded_by')
    raw_id_fields = ('uploaded_by',)

@admin.register(ItemEntryRequest)
class ItemEntryRequestAdmin(admin.ModelAdmin):
    list_display = ('request_code', 'title', 'po_number', 'supplier', 'workflow_status', 'assigned_store',
                   'is_urgent', 'is_complete', 'requested_by', 'created_at')
    search_fields = ('request_code', 'title', 'po_number', 'description')
    list_filter = ('workflow_status', 'status', 'supplier', 'assigned_store', 'requested_by',
                  'approved_by', 'is_urgent', 'is_complete', 'inspection_requested', 'inspection_failed',
                  'model19_generated', 'dsr_generated')
    raw_id_fields = ('supplier', 'status', 'requested_by', 'approved_by', 'main_classification',
                    'target_store', 'assigned_store', 'assigned_by')
    readonly_fields = ('request_code', 'created_at', 'updated_at', 'workflow_status',
                      'assigned_date', 'inspection_request_date')
    actions = ['mark_as_urgent', 'mark_as_complete', 'assign_to_store']
    inlines = [ItemEntryRequestItemInline, ItemEntryRequestAttachmentInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('request_code', 'title', 'description', 'po_number', 'po_date', 'supplier')
        }),
        ('Classification and Storage', {
            'fields': ('main_classification', 'target_store'),
            'classes': ('collapse',),
        }),
        ('Notes', {
            'fields': ('delivery_note', 'additional_notes'),
            'classes': ('collapse',),
        }),
        ('Status and Workflow', {
            'fields': ('status', 'workflow_status', 'requested_by', 'approved_by', 'approval_date', 'approval_comments',
                      'is_urgent', 'is_complete')
        }),
        ('Store Assignment', {
            'fields': ('assigned_store', 'assigned_by', 'assigned_date'),
        }),
        ('Inspection and Model 19', {
            'fields': ('inspection_requested', 'inspection_request_date', 'inspection_failed',
                      'model19_generated', 'model19_reference', 'dsr_generated', 'dsr_reference'),
        }),
        ('Dates', {
            'fields': ('expected_delivery_date', 'actual_delivery_date', 'created_at', 'updated_at')
        }),
    )

    def mark_as_urgent(self, request, queryset):
        updated = queryset.update(is_urgent=True)
        self.message_user(request, f"{updated} entry requests marked as urgent.")
    mark_as_urgent.short_description = "Mark selected requests as urgent"

    def mark_as_complete(self, request, queryset):
        updated = queryset.update(is_complete=True, actual_delivery_date=timezone.now().date())
        self.message_user(request, f"{updated} entry requests marked as complete.")
    mark_as_complete.short_description = "Mark selected requests as complete"

    def assign_to_store(self, request, queryset):
        """Assign selected entry requests to a store"""
        # This would typically be implemented with a custom admin action form
        # For now, we'll just update the first approved request as an example
        from django.contrib.admin.helpers import ActionForm
        from django import forms

        class AssignToStoreForm(ActionForm):
            store = forms.ModelChoiceField(
                queryset=Store.objects.filter(is_active=True),
                required=True,
                label="Select Store"
            )

        self.action_form = AssignToStoreForm

        # For demonstration, we'll just update the workflow status
        for entry_request in queryset.filter(workflow_status='approved'):
            try:
                entry_request.assign_to_store(
                    store=request.POST.get('store'),
                    user=request.user
                )
            except Exception as e:
                self.message_user(request, f"Error assigning {entry_request.request_code}: {str(e)}", level='error')

        self.message_user(request, f"{queryset.filter(workflow_status='approved').count()} entry requests assigned to store.")
    assign_to_store.short_description = "Assign selected requests to store"

@admin.register(ItemEntryRequestAttachment)
class ItemEntryRequestAttachmentAdmin(admin.ModelAdmin):
    list_display = ('file_name', 'entry_request', 'attachment_type', 'file_type',
                   'file_size', 'is_confidential', 'uploaded_by', 'created_at')
    search_fields = ('file_name', 'description', 'entry_request__request_code')
    list_filter = ('file_type', 'attachment_type', 'is_confidential', 'uploaded_by', 'created_at')
    raw_id_fields = ('entry_request', 'uploaded_by')
    readonly_fields = ('created_at', 'updated_at')

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        # Only show confidential attachments to users with permission
        if not request.user.has_perm('inventory.can_view_confidential_attachments'):
            qs = qs.filter(is_confidential=False)
        return qs

@admin.register(ItemEntryRequestItem)
class ItemEntryRequestItemAdmin(admin.ModelAdmin):
    list_display = ('item_description', 'entry_request', 'quantity', 'unit_price',
                   'is_received', 'received_quantity', 'received_date')
    search_fields = ('item_description', 'specifications', 'entry_request__request_code')
    list_filter = ('is_received', 'received_date', 'entry_request__status')
    raw_id_fields = ('entry_request', 'main_classification')
    readonly_fields = ('created_at', 'updated_at')
    actions = ['mark_as_received']

    def mark_as_received(self, request, queryset):
        for item in queryset:
            item.mark_as_received()
        self.message_user(request, f"{queryset.count()} items marked as received.")
    mark_as_received.short_description = "Mark selected items as received"

# Model 19 Item Inline
class Model19ItemInline(admin.TabularInline):
    model = Model19Item
    extra = 1
    fields = ('item_master', 'description', 'quantity', 'unit_price', 'serie', 'page_from', 'page_to',
             'inspection_status', 'inspection_notes')
    raw_id_fields = ('item_master',)

# Model 19 Receipt Admin
@admin.register(Model19Receipt)
class Model19ReceiptAdmin(admin.ModelAdmin):
    list_display = ('receipt_number', 'source_type', 'supplier', 'store', 'receipt_date', 'received_by', 'is_finalized')
    search_fields = ('receipt_number', 'purchase_order', 'delivery_note', 'notes')
    list_filter = ('source_type', 'receipt_date', 'store', 'is_finalized')
    raw_id_fields = ('supplier', 'store', 'shelf', 'entry_request', 'received_by', 'inspected_by', 'approved_by')
    readonly_fields = ('receipt_number', 'created_at', 'updated_at')
    inlines = [Model19ItemInline]
    actions = ['finalize_receipts']

    fieldsets = (
        ('Receipt Information', {
            'fields': ('receipt_number', 'receipt_date', 'source_type', 'is_finalized')
        }),
        ('Source', {
            'fields': ('supplier', 'department', 'purchase_order', 'delivery_note', 'entry_request')
        }),
        ('Storage Location', {
            'fields': ('store', 'shelf')
        }),
        ('Personnel', {
            'fields': ('received_by', 'inspected_by', 'approved_by')
        }),
        ('Notes', {
            'fields': ('notes',)
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',),
        }),
    )

    def finalize_receipts(self, request, queryset):
        """Finalize selected Model 19 receipts"""
        finalized = 0
        for receipt in queryset.filter(is_finalized=False):
            try:
                receipt.finalize(request.user)
                finalized += 1
            except Exception as e:
                self.message_user(request, f"Error finalizing receipt {receipt.receipt_number}: {str(e)}", level='error')

        if finalized:
            self.message_user(request, f"{finalized} receipts have been finalized successfully.")
    finalize_receipts.short_description = "Finalize selected receipts"


# Tags Admin
@admin.register(ItemTag)
class ItemTagAdmin(admin.ModelAdmin):
    list_display = ('name', 'color', 'description', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name', 'description')
    prepopulated_fields = {'slug': ('name',)}


# Audit Trail Admin
@admin.register(AuditTrail)
class AuditTrailAdmin(admin.ModelAdmin):
    list_display = ('timestamp', 'user', 'action_type', 'action_description', 'success', 'severity')
    list_filter = ('action_type', 'success', 'severity', 'timestamp')
    search_fields = ('action_description', 'user__username', 'user__email')
    readonly_fields = ('timestamp', 'user', 'action_type', 'action_description', 'content_type',
                      'object_id', 'old_values', 'new_values', 'metadata', 'ip_address',
                      'user_agent', 'session_key', 'success', 'error_message')
    date_hierarchy = 'timestamp'
    ordering = ('-timestamp',)

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return request.user.is_superuser


@admin.register(WorkflowAuditTrail)
class WorkflowAuditTrailAdmin(admin.ModelAdmin):
    list_display = ('timestamp', 'user', 'from_state', 'to_state', 'action', 'content_object')
    list_filter = ('from_state', 'to_state', 'action', 'timestamp')
    search_fields = ('user__username', 'comments')
    readonly_fields = ('timestamp', 'user', 'content_type', 'object_id', 'from_state',
                      'to_state', 'action', 'comments', 'metadata', 'duration_in_state')
    date_hierarchy = 'timestamp'
    ordering = ('-timestamp',)

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return request.user.is_superuser


@admin.register(SystemMetrics)
class SystemMetricsAdmin(admin.ModelAdmin):
    list_display = ('created_at', 'metric_type', 'metric_name', 'metric_value', 'metric_unit')
    list_filter = ('metric_type', 'created_at')
    search_fields = ('metric_name',)
    readonly_fields = ('created_at', 'updated_at')
    date_hierarchy = 'created_at'
    ordering = ('-created_at',)
