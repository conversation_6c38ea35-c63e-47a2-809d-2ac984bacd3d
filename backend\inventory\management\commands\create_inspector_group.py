from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from inventory.models.inspection import InspectionRequest, InspectionResult, InspectionItem


class Command(BaseCommand):
    help = 'Create Inspector group with appropriate permissions'

    def handle(self, *args, **options):
        # Create Inspector group
        inspector_group, created = Group.objects.get_or_create(name='Inspector')
        
        if created:
            self.stdout.write(
                self.style.SUCCESS('Successfully created Inspector group')
            )
        else:
            self.stdout.write(
                self.style.WARNING('Inspector group already exists')
            )

        # Get content types for inspection models
        inspection_request_ct = ContentType.objects.get_for_model(InspectionRequest)
        inspection_result_ct = ContentType.objects.get_for_model(InspectionResult)
        inspection_item_ct = ContentType.objects.get_for_model(InspectionItem)

        # Define permissions for Inspector group
        inspector_permissions = [
            # InspectionRequest permissions
            f'{inspection_request_ct.app_label}.view_inspectionrequest',
            f'{inspection_request_ct.app_label}.add_inspectionrequest',
            f'{inspection_request_ct.app_label}.change_inspectionrequest',
            
            # InspectionResult permissions
            f'{inspection_result_ct.app_label}.view_inspectionresult',
            f'{inspection_result_ct.app_label}.add_inspectionresult',
            f'{inspection_result_ct.app_label}.change_inspectionresult',
            
            # InspectionItem permissions
            f'{inspection_item_ct.app_label}.view_inspectionitem',
            f'{inspection_item_ct.app_label}.add_inspectionitem',
            f'{inspection_item_ct.app_label}.change_inspectionitem',
        ]

        # Add permissions to the group
        permissions_added = 0
        for perm_codename in inspector_permissions:
            try:
                app_label, codename = perm_codename.split('.', 1)
                permission = Permission.objects.get(
                    content_type__app_label=app_label,
                    codename=codename
                )
                inspector_group.permissions.add(permission)
                permissions_added += 1
            except Permission.DoesNotExist:
                self.stdout.write(
                    self.style.WARNING(f'Permission {perm_codename} does not exist')
                )

        self.stdout.write(
            self.style.SUCCESS(
                f'Added {permissions_added} permissions to Inspector group'
            )
        )

        # Display group information
        self.stdout.write(
            self.style.SUCCESS(
                f'Inspector group now has {inspector_group.permissions.count()} permissions'
            )
        )
