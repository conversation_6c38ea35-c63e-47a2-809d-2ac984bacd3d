from django.core.management.base import BaseCommand
from inventory.models.tags import ItemTag


class Command(BaseCommand):
    help = 'Create predefined item tags'

    def handle(self, *args, **options):
        self.stdout.write('Creating predefined item tags...')
        
        try:
            ItemTag.create_predefined_tags()
            
            # Count created tags
            total_tags = ItemTag.objects.count()
            active_tags = ItemTag.objects.filter(is_active=True).count()
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully created predefined tags!\n'
                    f'Total tags: {total_tags}\n'
                    f'Active tags: {active_tags}'
                )
            )
            
            # Display created tags by type
            self.stdout.write('\nCreated tags by type:')
            for tag_type_code, tag_type_name in ItemTag.TAG_TYPES:
                tags = ItemTag.objects.filter(tag_type=tag_type_code)
                if tags.exists():
                    self.stdout.write(f'\n{tag_type_name}:')
                    for tag in tags:
                        status = '✓' if tag.is_active else '✗'
                        self.stdout.write(f'  {status} {tag.name} ({tag.color})')
                        
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error creating predefined tags: {str(e)}')
            )
