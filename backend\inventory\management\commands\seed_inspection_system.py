from django.core.management.base import BaseCommand
from django.contrib.auth.models import User, Group
from django.db import transaction
from inventory.models import InspectionCommittee, MainClassification


class Command(BaseCommand):
    help = 'Seed the database with sample inspection users and committees'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Reset existing data before seeding',
        )

    def handle(self, *args, **options):
        if options['reset']:
            self.stdout.write(self.style.WARNING('Resetting inspection data...'))
            self.reset_data()

        self.stdout.write(self.style.SUCCESS('🌱 Starting Inspection System Seeding...'))
        
        with transaction.atomic():
            inspectors = self.create_inspection_users()
            classifications = self.create_main_classifications()
            committees = self.create_inspection_committees(inspectors, classifications)
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'✅ Successfully created {len(inspectors)} inspectors, '
                    f'{len(classifications)} classifications, and {len(committees)} committees'
                )
            )

    def reset_data(self):
        """Reset inspection-related data"""
        InspectionCommittee.objects.all().delete()
        User.objects.filter(username__startswith='inspector_').delete()
        self.stdout.write(self.style.WARNING('Reset completed'))

    def create_inspection_users(self):
        """Create sample inspection users"""
        inspector_group, _ = Group.objects.get_or_create(name='Inspector')
        
        inspectors_data = [
            {
                'username': 'inspector_medical',
                'first_name': 'Dr. Sarah',
                'last_name': 'Johnson',
                'email': '<EMAIL>'
            },
            {
                'username': 'inspector_technical',
                'first_name': 'Eng. Michael',
                'last_name': 'Chen',
                'email': '<EMAIL>'
            },
            {
                'username': 'inspector_office',
                'first_name': 'Ms. Emily',
                'last_name': 'Davis',
                'email': '<EMAIL>'
            },
            {
                'username': 'inspector_safety',
                'first_name': 'Mr. James',
                'last_name': 'Wilson',
                'email': '<EMAIL>'
            },
            {
                'username': 'inspector_general',
                'first_name': 'Ms. Lisa',
                'last_name': 'Anderson',
                'email': '<EMAIL>'
            },
            {
                'username': 'inspector_senior',
                'first_name': 'Dr. Robert',
                'last_name': 'Martinez',
                'email': '<EMAIL>'
            }
        ]
        
        created_users = []
        for user_data in inspectors_data:
            user, created = User.objects.get_or_create(
                username=user_data['username'],
                defaults={
                    'first_name': user_data['first_name'],
                    'last_name': user_data['last_name'],
                    'email': user_data['email'],
                    'is_active': True,
                    'is_staff': True
                }
            )
            
            if created:
                user.set_password('inspector123')
                user.save()
                self.stdout.write(f"✓ Created inspector: {user.first_name} {user.last_name}")
            
            user.groups.add(inspector_group)
            created_users.append(user)
        
        return created_users

    def create_main_classifications(self):
        """Create sample main classifications"""
        classifications_data = [
            {'name': 'Medical Equipment', 'code': 'MED'},
            {'name': 'Office Supplies', 'code': 'OFF'},
            {'name': 'Technical Equipment', 'code': 'TECH'},
            {'name': 'Safety Equipment', 'code': 'SAFE'},
            {'name': 'Furniture', 'code': 'FURN'},
            {'name': 'Vehicles', 'code': 'VEH'}
        ]
        
        created_classifications = []
        for class_data in classifications_data:
            classification, created = MainClassification.objects.get_or_create(
                name=class_data['name'],
                defaults={'code': class_data['code']}
            )
            created_classifications.append(classification)
        
        return created_classifications

    def create_inspection_committees(self, inspectors, classifications):
        """Create sample inspection committees"""
        committees_data = [
            {
                'title': 'Medical Equipment Inspection Committee',
                'description': 'Specialized committee for medical devices and healthcare equipment',
                'members': ['inspector_medical', 'inspector_senior', 'inspector_general'],
                'classifications': ['Medical Equipment']
            },
            {
                'title': 'Technical Equipment Committee',
                'description': 'Committee for computers, machinery, and technical equipment',
                'members': ['inspector_technical', 'inspector_senior'],
                'classifications': ['Technical Equipment']
            },
            {
                'title': 'Office Supplies Inspection Committee',
                'description': 'Committee for office supplies and general office equipment',
                'members': ['inspector_office', 'inspector_general'],
                'classifications': ['Office Supplies', 'Furniture']
            },
            {
                'title': 'Safety & Security Committee',
                'description': 'Committee for safety equipment and security systems',
                'members': ['inspector_safety', 'inspector_technical', 'inspector_senior'],
                'classifications': ['Safety Equipment']
            },
            {
                'title': 'General Inspection Committee',
                'description': 'Multi-purpose committee for general items',
                'members': ['inspector_general', 'inspector_senior'],
                'classifications': ['Vehicles', 'Furniture']
            }
        ]
        
        user_map = {user.username: user for user in inspectors}
        classification_map = {cls.name: cls for cls in classifications}
        
        created_committees = []
        for committee_data in committees_data:
            committee, created = InspectionCommittee.objects.get_or_create(
                title=committee_data['title'],
                defaults={
                    'description': committee_data['description'],
                    'is_active': True
                }
            )
            
            if created:
                # Add members
                for member_username in committee_data['members']:
                    if member_username in user_map:
                        committee.users.add(user_map[member_username])
                
                # Add classifications
                for classification_name in committee_data['classifications']:
                    if classification_name in classification_map:
                        committee.main_classifications.add(classification_map[classification_name])
                
                self.stdout.write(f"✓ Created committee: {committee.title}")
            
            created_committees.append(committee)
        
        return created_committees
