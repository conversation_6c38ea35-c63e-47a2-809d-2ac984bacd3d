# Generated by Django 5.2.1 on 2025-05-24 04:03

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("inventory", "0045_inspectionrequest_delivery_date_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name="itementryrequest",
            name="requested_by",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="requested_entry_requests",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="itementryrequest",
            name="workflow_status",
            field=models.CharField(
                choices=[
                    ("draft", "Draft"),
                    ("pending", "Pending Approval"),
                    ("approved", "Approved"),
                    ("assigned", "Assigned to Store"),
                    ("inspecting", "Under Inspection"),
                    ("completed", "Completed"),
                    ("rejected", "Rejected"),
                ],
                default="pending",
                help_text="Current status in the workflow process",
                max_length=20,
            ),
        ),
    ]
