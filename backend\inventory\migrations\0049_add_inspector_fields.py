# Generated manually for inspector fields

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('inventory', '0048_fix_request_code_field'),
    ]

    operations = [
        migrations.AddField(
            model_name='itementryrequestitem',
            name='assigned_inspector',
            field=models.ForeignKey(
                blank=True,
                help_text='Inspector assigned to inspect this item',
                limit_choices_to={'groups__name': 'Inspector'},
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name='assigned_inspection_items',
                to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name='itementryrequestitem',
            name='inspection_status',
            field=models.CharField(
                choices=[
                    ('pending', 'Pending Inspection'),
                    ('in_progress', 'Inspection In Progress'),
                    ('passed', 'Inspection Passed'),
                    ('failed', 'Inspection Failed'),
                    ('not_required', 'Inspection Not Required')
                ],
                default='not_required',
                help_text='Current inspection status of this item',
                max_length=20
            ),
        ),
        migrations.AddField(
            model_name='itementryrequestitem',
            name='inspection_notes',
            field=models.TextField(blank=True, help_text='Notes from inspection'),
        ),
        migrations.AddField(
            model_name='itementryrequestitem',
            name='inspection_date',
            field=models.DateTimeField(
                blank=True,
                help_text='Date when inspection was completed',
                null=True
            ),
        ),
    ]
