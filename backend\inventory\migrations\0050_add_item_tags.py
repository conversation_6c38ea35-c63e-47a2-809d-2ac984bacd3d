# Generated manually for item tags

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0049_add_inspector_fields'),
    ]

    operations = [
        migrations.CreateModel(
            name='ItemTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('name', models.CharField(help_text="Name of the tag (e.g., 'Sensitive', 'Very Small', 'Building Material')", max_length=100, unique=True)),
                ('slug', models.SlugField(help_text='URL-friendly version of the tag name', max_length=100, unique=True)),
                ('tag_type', models.CharField(choices=[('sensitivity', 'Sensitivity Level'), ('size', 'Size Category'), ('material', 'Material Type'), ('disposal', 'Disposal Method'), ('handling', 'Handling Requirements'), ('storage', 'Storage Requirements'), ('security', 'Security Level'), ('environmental', 'Environmental Impact'), ('maintenance', 'Maintenance Category'), ('other', 'Other')], default='other', help_text='Category of this tag', max_length=20)),
                ('description', models.TextField(blank=True, help_text='Detailed description of what this tag represents')),
                ('color', models.CharField(choices=[('red', 'Red'), ('orange', 'Orange'), ('yellow', 'Yellow'), ('green', 'Green'), ('blue', 'Blue'), ('purple', 'Purple'), ('pink', 'Pink'), ('brown', 'Brown'), ('gray', 'Gray'), ('black', 'Black')], default='blue', help_text='Color for displaying this tag', max_length=10)),
                ('icon', models.CharField(blank=True, help_text="Material-UI icon name for this tag (e.g., 'Security', 'Warning', 'Build')", max_length=50)),
                ('priority', models.PositiveIntegerField(default=0, help_text='Display priority (higher numbers appear first)')),
            ],
            options={
                'verbose_name': 'Item Tag',
                'verbose_name_plural': 'Item Tags',
                'ordering': ['-priority', 'name'],
            },
        ),
        migrations.AddField(
            model_name='itemmaster',
            name='tags',
            field=models.ManyToManyField(blank=True, help_text='Tags for special characteristics like sensitive, fragile, etc.', related_name='items', to='inventory.itemtag'),
        ),
    ]
