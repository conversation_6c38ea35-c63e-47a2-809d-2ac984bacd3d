# Generated manually for audit trail models

from django.db import migrations, models
import django.db.models.deletion
from django.conf import settings


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('inventory', '0050_add_item_tags'),
    ]

    operations = [
        migrations.CreateModel(
            name='AuditTrail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('action_type', models.CharField(choices=[('create', 'Create'), ('update', 'Update'), ('delete', 'Delete'), ('workflow', 'Workflow Transition'), ('assignment', 'Assignment'), ('approval', 'Approval'), ('rejection', 'Rejection'), ('inspection', 'Inspection'), ('notification', 'Notification'), ('export', 'Export'), ('import', 'Import'), ('login', 'Login'), ('logout', 'Logout'), ('access', 'Access'), ('error', 'Error')], max_length=20)),
                ('action_description', models.TextField()),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('object_id', models.PositiveIntegerField(blank=True, null=True)),
                ('old_values', models.JSONField(blank=True, help_text='Previous values before change', null=True)),
                ('new_values', models.JSONField(blank=True, help_text='New values after change', null=True)),
                ('metadata', models.JSONField(blank=True, help_text='Additional metadata', null=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True, null=True)),
                ('session_key', models.CharField(blank=True, max_length=40, null=True)),
                ('severity', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('critical', 'Critical')], default='medium', max_length=10)),
                ('category', models.CharField(blank=True, max_length=50, null=True)),
                ('tags', models.JSONField(default=list, help_text='Tags for categorization and search')),
                ('success', models.BooleanField(default=True)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('content_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='audit_trails', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Audit Trail',
                'verbose_name_plural': 'Audit Trails',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='WorkflowAuditTrail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('object_id', models.PositiveIntegerField()),
                ('from_state', models.CharField(max_length=50)),
                ('to_state', models.CharField(max_length=50)),
                ('action', models.CharField(max_length=50)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('comments', models.TextField(blank=True)),
                ('metadata', models.JSONField(default=dict)),
                ('duration_in_state', models.DurationField(blank=True, help_text='Time spent in previous state', null=True)),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Workflow Audit Trail',
                'verbose_name_plural': 'Workflow Audit Trails',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='SystemMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('metric_type', models.CharField(choices=[('performance', 'Performance'), ('usage', 'Usage'), ('error', 'Error'), ('security', 'Security'), ('business', 'Business')], max_length=20)),
                ('metric_name', models.CharField(max_length=100)),
                ('metric_value', models.FloatField()),
                ('metric_unit', models.CharField(blank=True, max_length=20)),
                ('metadata', models.JSONField(default=dict)),
                ('period_start', models.DateTimeField()),
                ('period_end', models.DateTimeField()),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'System Metric',
                'verbose_name_plural': 'System Metrics',
                'ordering': ['-created_at'],
            },
        ),
        # Add indexes for better performance
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_audit_user_timestamp ON inventory_audittrail(user_id, timestamp);",
            reverse_sql="DROP INDEX IF EXISTS idx_audit_user_timestamp;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_audit_action_timestamp ON inventory_audittrail(action_type, timestamp);",
            reverse_sql="DROP INDEX IF EXISTS idx_audit_action_timestamp;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_audit_content_object ON inventory_audittrail(content_type_id, object_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_audit_content_object;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_workflow_audit_content ON inventory_workflowaudittrail(content_type_id, object_id, timestamp);",
            reverse_sql="DROP INDEX IF EXISTS idx_workflow_audit_content;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_workflow_audit_states ON inventory_workflowaudittrail(from_state, to_state);",
            reverse_sql="DROP INDEX IF EXISTS idx_workflow_audit_states;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_metrics_type_name ON inventory_systemmetrics(metric_type, metric_name, created_at);",
            reverse_sql="DROP INDEX IF EXISTS idx_metrics_type_name;"
        ),
    ]
