# Generated by Django 5.2.1 on 2025-05-26 04:35

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("contenttypes", "0002_remove_content_type_name"),
        ("inventory", "0051_add_audit_trail"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="audittrail",
            name="is_active",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="systemmetrics",
            name="is_active",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="workflowaudittrail",
            name="is_active",
            field=models.BooleanField(default=True),
        ),
        migrations.AlterField(
            model_name="itemtag",
            name="is_active",
            field=models.BooleanField(
                default=True, help_text="Whether this tag is available for use"
            ),
        ),
        migrations.AddIndex(
            model_name="audittrail",
            index=models.Index(
                fields=["user", "timestamp"], name="inventory_a_user_id_a32740_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="audittrail",
            index=models.Index(
                fields=["action_type", "timestamp"],
                name="inventory_a_action__f622db_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="audittrail",
            index=models.Index(
                fields=["content_type", "object_id"],
                name="inventory_a_content_adf41a_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="audittrail",
            index=models.Index(
                fields=["timestamp"], name="inventory_a_timesta_0f6947_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="audittrail",
            index=models.Index(
                fields=["success", "timestamp"], name="inventory_a_success_fd9499_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="systemmetrics",
            index=models.Index(
                fields=["metric_type", "metric_name", "created_at"],
                name="inventory_s_metric__bd1249_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="systemmetrics",
            index=models.Index(
                fields=["period_start", "period_end"],
                name="inventory_s_period__aa622f_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="workflowaudittrail",
            index=models.Index(
                fields=["content_type", "object_id", "timestamp"],
                name="inventory_w_content_493499_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="workflowaudittrail",
            index=models.Index(
                fields=["from_state", "to_state"], name="inventory_w_from_st_c796be_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="workflowaudittrail",
            index=models.Index(
                fields=["user", "timestamp"], name="inventory_w_user_id_4fb2c2_idx"
            ),
        ),
    ]
