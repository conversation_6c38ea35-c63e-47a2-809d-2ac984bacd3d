"""
Audit Trail Models for comprehensive system logging
"""

from django.db import models
from django.contrib.auth.models import User
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey
from .base import TimeStampedModel
import json


class AuditTrail(TimeStampedModel):
    """
    Comprehensive audit trail for all system actions
    Tracks who did what, when, and why
    """
    
    ACTION_TYPES = [
        ('create', 'Create'),
        ('update', 'Update'),
        ('delete', 'Delete'),
        ('workflow', 'Workflow Transition'),
        ('assignment', 'Assignment'),
        ('approval', 'Approval'),
        ('rejection', 'Rejection'),
        ('inspection', 'Inspection'),
        ('notification', 'Notification'),
        ('export', 'Export'),
        ('import', 'Import'),
        ('login', 'Login'),
        ('logout', 'Logout'),
        ('access', 'Access'),
        ('error', 'Error'),
    ]
    
    SEVERITY_LEVELS = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]
    
    # Who performed the action
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='audit_trails'
    )
    
    # What was the action
    action_type = models.CharField(max_length=20, choices=ACTION_TYPES)
    action_description = models.TextField()
    
    # When did it happen
    timestamp = models.DateTimeField(auto_now_add=True)
    
    # What object was affected (generic foreign key)
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, null=True, blank=True)
    object_id = models.PositiveIntegerField(null=True, blank=True)
    content_object = GenericForeignKey('content_type', 'object_id')
    
    # Additional context
    old_values = models.JSONField(null=True, blank=True, help_text="Previous values before change")
    new_values = models.JSONField(null=True, blank=True, help_text="New values after change")
    metadata = models.JSONField(null=True, blank=True, help_text="Additional metadata")
    
    # Request context
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(null=True, blank=True)
    session_key = models.CharField(max_length=40, null=True, blank=True)
    
    # Severity and categorization
    severity = models.CharField(max_length=10, choices=SEVERITY_LEVELS, default='medium')
    category = models.CharField(max_length=50, null=True, blank=True)
    tags = models.JSONField(default=list, help_text="Tags for categorization and search")
    
    # Success/failure tracking
    success = models.BooleanField(default=True)
    error_message = models.TextField(null=True, blank=True)
    
    class Meta:
        verbose_name = "Audit Trail"
        verbose_name_plural = "Audit Trails"
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['action_type', 'timestamp']),
            models.Index(fields=['content_type', 'object_id']),
            models.Index(fields=['timestamp']),
            models.Index(fields=['success', 'timestamp']),
        ]
    
    def __str__(self):
        return f"{self.user} - {self.action_type} - {self.timestamp}"
    
    @classmethod
    def log_action(cls, user, action_type, description, content_object=None, 
                   old_values=None, new_values=None, metadata=None, 
                   request=None, severity='medium', category=None, tags=None, 
                   success=True, error_message=None):
        """
        Convenience method to create audit trail entries
        """
        audit_data = {
            'user': user,
            'action_type': action_type,
            'action_description': description,
            'old_values': old_values,
            'new_values': new_values,
            'metadata': metadata or {},
            'severity': severity,
            'category': category,
            'tags': tags or [],
            'success': success,
            'error_message': error_message,
        }
        
        if content_object:
            audit_data['content_object'] = content_object
        
        if request:
            audit_data.update({
                'ip_address': cls._get_client_ip(request),
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                'session_key': request.session.session_key,
            })
        
        return cls.objects.create(**audit_data)
    
    @staticmethod
    def _get_client_ip(request):
        """Get client IP address from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class WorkflowAuditTrail(TimeStampedModel):
    """
    Specialized audit trail for workflow transitions
    Provides detailed tracking of state changes
    """
    
    # Reference to the main object
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')
    
    # Workflow details
    from_state = models.CharField(max_length=50)
    to_state = models.CharField(max_length=50)
    action = models.CharField(max_length=50)
    
    # Who and when
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    timestamp = models.DateTimeField(auto_now_add=True)
    
    # Additional context
    comments = models.TextField(blank=True)
    metadata = models.JSONField(default=dict)
    
    # Duration tracking
    duration_in_state = models.DurationField(null=True, blank=True, help_text="Time spent in previous state")
    
    class Meta:
        verbose_name = "Workflow Audit Trail"
        verbose_name_plural = "Workflow Audit Trails"
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['content_type', 'object_id', 'timestamp']),
            models.Index(fields=['from_state', 'to_state']),
            models.Index(fields=['user', 'timestamp']),
        ]
    
    def __str__(self):
        return f"{self.from_state} → {self.to_state} by {self.user}"


class SystemMetrics(TimeStampedModel):
    """
    System performance and usage metrics
    """
    
    METRIC_TYPES = [
        ('performance', 'Performance'),
        ('usage', 'Usage'),
        ('error', 'Error'),
        ('security', 'Security'),
        ('business', 'Business'),
    ]
    
    metric_type = models.CharField(max_length=20, choices=METRIC_TYPES)
    metric_name = models.CharField(max_length=100)
    metric_value = models.FloatField()
    metric_unit = models.CharField(max_length=20, blank=True)
    
    # Context
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    metadata = models.JSONField(default=dict)
    
    # Aggregation period
    period_start = models.DateTimeField()
    period_end = models.DateTimeField()
    
    class Meta:
        verbose_name = "System Metric"
        verbose_name_plural = "System Metrics"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['metric_type', 'metric_name', 'created_at']),
            models.Index(fields=['period_start', 'period_end']),
        ]
    
    def __str__(self):
        return f"{self.metric_name}: {self.metric_value} {self.metric_unit}"
