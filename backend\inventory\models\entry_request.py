from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from django.core.exceptions import ValidationError
from .base import TimeStampedModel
from .suppliers import Supplier
from .status import ApprovalStatus
from .classification import MainClassification
from .storage import Store
from serials.models import SerialVoucher, SerialVoucherCategory

class ItemEntryRequest(TimeStampedModel):
    """
    Represents a request for item entry submitted by a Procurement Officer.
    This is the first step in the item receiving process.

    Workflow:
    1. Procurement Officer creates an entry request with purchase details
    2. PAO reviews and approves/rejects the request
    3. If approved, items can be received based on this request
    4. Model 19 receipts are generated for approved requests
    """
    # Request identification
    request_code = models.CharField(max_length=20, unique=True, help_text="Format: IER-YYYY-XXXX")
    title = models.CharField(max_length=100)
    description = models.TextField(blank=True)

    # Purchase information
    po_number = models.CharField(max_length=50, verbose_name="Purchase Order Number")
    po_date = models.DateField(verbose_name="Purchase Order Date", null=True, blank=True)
    supplier = models.ForeignKey(Supplier, on_delete=models.PROTECT, related_name='entry_requests')

    # Classification and storage
    main_classification = models.ForeignKey(
        MainClassification,
        on_delete=models.PROTECT,
        related_name='entry_requests',
        null=True,
        blank=True,
        help_text="Main classification of items in this request"
    )
    target_store = models.ForeignKey(
        Store,
        on_delete=models.PROTECT,
        related_name='entry_requests',
        null=True,
        blank=True,
        help_text="Store where items will be received"
    )

    # Notes and comments
    delivery_note = models.TextField(blank=True, help_text="Details from supplier delivery note")
    additional_notes = models.TextField(blank=True, help_text="Additional notes about this request")

    # Status and workflow
    status = models.ForeignKey(
        ApprovalStatus,
        on_delete=models.PROTECT,
        related_name='entry_requests',
        limit_choices_to={'is_active': True}
    )

    # Workflow tracking
    WORKFLOW_STATUS_CHOICES = [
        ('pending', 'Pending Approval'),
        ('approved', 'Approved'),
        ('assigned', 'Assigned to Store'),
        ('inspecting', 'Under Inspection'),
        ('completed', 'Completed'),
        ('rejected', 'Rejected'),
    ]
    workflow_status = models.CharField(
        max_length=20,
        choices=WORKFLOW_STATUS_CHOICES,
        default='pending',
        help_text="Current status in the workflow process"
    )
    assigned_store = models.ForeignKey(
        Store,
        on_delete=models.SET_NULL,
        related_name='assigned_entry_requests',
        null=True,
        blank=True,
        help_text="Store assigned to handle this request"
    )
    assigned_date = models.DateTimeField(null=True, blank=True)
    assigned_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_entry_requests',
        help_text="User who assigned this request to a store"
    )
    inspection_requested = models.BooleanField(default=False, help_text="Whether inspection has been requested")
    inspection_request_date = models.DateTimeField(null=True, blank=True)
    inspection_failed = models.BooleanField(default=False, help_text="Whether inspection failed")
    model19_generated = models.BooleanField(default=False, help_text="Whether Model 19 has been generated")
    model19_reference = models.CharField(max_length=50, blank=True, help_text="Reference to the Model 19 document")
    dsr_generated = models.BooleanField(default=False, help_text="Whether DSR has been generated")
    dsr_reference = models.CharField(max_length=50, blank=True, help_text="Reference to the DSR document")

    # User tracking
    requested_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='requested_entry_requests'
        # Temporarily removed group constraint for testing
        # limit_choices_to={'groups__name': 'Procurement'}
    )
    approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_entry_requests',
        limit_choices_to={'groups__name': 'PAO'}
    )
    approval_date = models.DateTimeField(null=True, blank=True)
    approval_comments = models.TextField(blank=True)

    # Dates
    expected_delivery_date = models.DateField(null=True, blank=True)
    actual_delivery_date = models.DateField(null=True, blank=True)

    # Flags
    is_urgent = models.BooleanField(default=False, help_text="Mark as urgent for expedited processing")
    is_complete = models.BooleanField(default=False, help_text="All items have been received")

    class Meta:
        verbose_name = "Item Entry Request"
        verbose_name_plural = "Item Entry Requests"
        ordering = ['-created_at']
        permissions = [
            ("can_approve_entry_request", "Can approve entry requests"),
            ("can_reject_entry_request", "Can reject entry requests"),
            ("can_mark_as_received", "Can mark entry requests as received"),
        ]

    def __str__(self):
        return f"{self.request_code}: {self.title}"

    def save(self, *args, **kwargs):
        # Generate request_code if not provided
        if not self.request_code:
            try:
                # Get or create a serial voucher category for item entry requests
                category, _ = SerialVoucherCategory.objects.get_or_create(
                    title="Item Entry Request",
                    defaults={
                        'report_title': "Item Entry Request Vouchers",
                        'is_active': True
                    }
                )

                # Get or create a serial voucher for item entry requests
                current_year = timezone.now().year
                serial_voucher, _ = SerialVoucher.objects.get_or_create(
                    category=category,
                    prefix=f"IER-{current_year}-",
                    defaults={
                        'start_number': 1,
                        'end_number': 9999,
                        'current_number': 1,
                        'is_active': True
                    }
                )

                # Generate the next code
                self.request_code = serial_voucher.get_next_serial()
            except Exception as e:
                raise ValidationError(f"Error generating request code: {str(e)}")

        # Set approval date when approved
        if self.approved_by and not self.approval_date:
            self.approval_date = timezone.now()

        super().save(*args, **kwargs)

    def approve(self, user, comments=None):
        """Approve the entry request"""
        if not user.has_perm('inventory.can_approve_entry_request'):
            raise ValidationError("User does not have permission to approve entry requests")

        # Get the 'Approved' status
        approved_status = ApprovalStatus.objects.filter(name='Approved', is_active=True).first()
        if not approved_status:
            raise ValidationError("Approved status not found in the system")

        self.status = approved_status
        self.approved_by = user
        self.approval_date = timezone.now()
        self.workflow_status = 'approved'
        if comments:
            self.approval_comments = comments
        self.save()

    def reject(self, user, comments=None):
        """Reject the entry request"""
        if not user.has_perm('inventory.can_reject_entry_request'):
            raise ValidationError("User does not have permission to reject entry requests")

        # Get the 'Rejected' status
        rejected_status = ApprovalStatus.objects.filter(name='Rejected', is_active=True).first()
        if not rejected_status:
            raise ValidationError("Rejected status not found in the system")

        self.status = rejected_status
        self.approved_by = user
        self.approval_date = timezone.now()
        self.workflow_status = 'rejected'
        if comments:
            self.approval_comments = comments
        self.save()

    def assign_to_store(self, store, user):
        """Assign the entry request to a store"""
        if not user.has_perm('inventory.can_assign_entry_request'):
            raise ValidationError("User does not have permission to assign entry requests")

        if self.workflow_status != 'approved':
            raise ValidationError("Entry request must be approved before it can be assigned to a store")

        self.assigned_store = store
        self.assigned_by = user
        self.assigned_date = timezone.now()
        self.workflow_status = 'assigned'
        self.save()

    def request_inspection(self, user):
        """Request inspection for the items in this entry request"""
        if self.workflow_status != 'assigned':
            raise ValidationError("Entry request must be assigned to a store before requesting inspection")

        # Check if user belongs to the assigned store
        from ..models.storage import StoreUser
        is_store_user = StoreUser.objects.filter(
            user=user,
            store=self.assigned_store,
            is_active=True
        ).exists()

        if not is_store_user and not user.is_superuser:
            raise ValidationError("Only store users of the assigned store can request inspection")

        self.inspection_requested = True
        self.inspection_request_date = timezone.now()
        self.workflow_status = 'inspecting'
        self.save()

        # Create an inspection request
        from ..models.inspection import InspectionRequest
        inspection_request = InspectionRequest.objects.create(
            title=f"Inspection for {self.request_code}",
            description=f"Inspection requested for entry request {self.request_code}",
            requested_by=user,
            entry_request=self,
            store=self.assigned_store,
            status='pending'
        )

        return inspection_request

    def generate_model19(self, user, reference=None):
        """Generate Model 19 for the items in this entry request"""
        if self.workflow_status not in ['assigned', 'inspecting']:
            raise ValidationError("Entry request must be assigned to a store or under inspection to generate Model 19")

        # Check if user belongs to the assigned store
        from ..models.storage import StoreUser
        is_store_user = StoreUser.objects.filter(
            user=user,
            store=self.assigned_store,
            is_active=True
        ).exists()

        if not is_store_user and not user.is_superuser:
            raise ValidationError("Only store users of the assigned store can generate Model 19")

        self.model19_generated = True
        self.model19_reference = reference or f"M19-{self.request_code}"
        self.workflow_status = 'completed'
        self.is_complete = True
        self.actual_delivery_date = timezone.now().date()
        self.save()

    def generate_dsr(self, user, reference=None):
        """Generate DSR for the items in this entry request"""
        if self.workflow_status != 'inspecting':
            raise ValidationError("Entry request must be under inspection to generate DSR")

        # Check if user belongs to the inspection committee or assigned store
        from ..models.inspection import InspectionCommittee
        is_inspector = InspectionCommittee.objects.filter(
            members=user,
            is_active=True
        ).exists()

        from ..models.storage import StoreUser
        is_store_user = StoreUser.objects.filter(
            user=user,
            store=self.assigned_store,
            is_active=True
        ).exists()

        if not (is_inspector or is_store_user) and not user.is_superuser:
            raise ValidationError("Only inspection committee members or store users can generate DSR")

        self.dsr_generated = True
        self.dsr_reference = reference or f"DSR-{self.request_code}"
        self.save()

    def mark_as_received(self, actual_delivery_date=None):
        """Mark the entry request as received"""
        self.is_complete = True
        self.workflow_status = 'completed'
        if actual_delivery_date:
            self.actual_delivery_date = actual_delivery_date
        else:
            self.actual_delivery_date = timezone.now().date()
        self.save()

class ItemEntryRequestAttachment(TimeStampedModel):
    """
    Represents an attachment for an item entry request.

    This model stores metadata about files attached to entry requests,
    such as purchase orders, delivery notes, and other supporting documents.
    """
    entry_request = models.ForeignKey(
        ItemEntryRequest,
        on_delete=models.CASCADE,
        related_name='attachments'
    )
    file_name = models.CharField(max_length=255)
    file_path = models.CharField(max_length=255)
    file_type = models.CharField(max_length=50)
    file_size = models.IntegerField(help_text="File size in bytes")
    description = models.TextField(blank=True)
    uploaded_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)

    # File categorization
    ATTACHMENT_TYPES = [
        ('PO', 'Purchase Order'),
        ('DN', 'Delivery Note'),
        ('CT', 'Contract Document'),
        ('BD', 'Bid Document'),
        ('SP', 'Technical Specifications'),
        ('QT', 'Quotation'),
        ('IN', 'Invoice'),
        ('PR', 'Proforma Invoice'),
        ('TR', 'Terms & Conditions'),
        ('WR', 'Warranty Document'),
        ('CP', 'Company Profile'),
        ('OT', 'Other'),
    ]
    attachment_type = models.CharField(
        max_length=2,
        choices=ATTACHMENT_TYPES,
        default='OT',
        help_text="Type of attachment"
    )

    # Flags
    is_confidential = models.BooleanField(default=False, help_text="Mark as confidential to restrict access")

    class Meta:
        verbose_name = "Entry Request Attachment"
        verbose_name_plural = "Entry Request Attachments"
        ordering = ['-created_at']
        permissions = [
            ("can_view_confidential_attachments", "Can view confidential attachments"),
        ]

    def __str__(self):
        return f"{self.file_name} ({self.entry_request.request_code})"

    def get_file_extension(self):
        """Return the file extension"""
        if '.' in self.file_name:
            return self.file_name.split('.')[-1].lower()
        return ""

    def is_image(self):
        """Check if the file is an image"""
        image_extensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
        return self.get_file_extension() in image_extensions

    def is_document(self):
        """Check if the file is a document"""
        doc_extensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt']
        return self.get_file_extension() in doc_extensions


class ItemEntryRequestItem(TimeStampedModel):
    """
    Represents an item in an entry request.

    This model allows for detailed specification of items to be received,
    including quantities, specifications, and expected delivery details.
    """
    entry_request = models.ForeignKey(
        ItemEntryRequest,
        on_delete=models.CASCADE,
        related_name='items'
    )

    # Item details
    item_description = models.CharField(max_length=255)
    specifications = models.TextField(blank=True, help_text="Technical specifications of the item")
    quantity = models.PositiveIntegerField(default=1)
    unit_price = models.DecimalField(max_digits=14, decimal_places=2, null=True, blank=True)

    # Classification
    main_classification = models.ForeignKey(
        MainClassification,
        on_delete=models.SET_NULL,
        related_name='entry_request_items',
        null=True,
        blank=True
    )

    # Status tracking
    is_received = models.BooleanField(default=False)
    received_quantity = models.PositiveIntegerField(default=0)
    received_date = models.DateField(null=True, blank=True)

    class Meta:
        verbose_name = "Entry Request Item"
        verbose_name_plural = "Entry Request Items"
        ordering = ['entry_request', 'id']

    def __str__(self):
        return f"{self.item_description} ({self.quantity})"

    def get_total_price(self):
        """Calculate the total price for this item"""
        if self.unit_price is not None:
            return self.unit_price * self.quantity
        return None

    def mark_as_received(self, quantity=None, date=None):
        """Mark the item as received"""
        if quantity is None:
            quantity = self.quantity

        self.received_quantity = quantity
        self.is_received = True
        self.received_date = date or timezone.now().date()
        self.save()
