from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from django.core.validators import MinValueValidator
from .base import TimeStampedModel
from .classification import MainClassification
from .items import ItemMaster, Batch, Item
from .suppliers import Supplier
from .storage import Store
from .reports import DiscrepancyType

class InspectionCommittee(TimeStampedModel):
    """
    Represents a committee responsible for inspecting items.
    Each committee can be assigned to multiple types of items (e.g., Medical Supplies, Office Equipment).
    """
    title = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True, help_text="Whether this committee is active")
    users = models.ManyToManyField(
        User,
        limit_choices_to={'groups__name': 'inspector'},
        related_name='inspection_committees'
    )
    main_classifications = models.ManyToManyField(
        MainClassification,
        related_name='inspection_committees',
        blank=True,
        help_text="Main classifications this committee is responsible for"
    )
    # Keep for backward compatibility, will be deprecated
    main_classification = models.ForeignKey(
        MainClassification,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='single_inspection_committees',
        help_text="Legacy field - use main_classifications instead"
    )

    class Meta:
        verbose_name = "Inspection Committee"
        verbose_name_plural = "Inspection Committees"
        ordering = ['title']

    def __str__(self):
        return self.title

    @property
    def user_count(self):
        """Return the number of users in this committee"""
        return self.users.count()

class InspectionRequest(TimeStampedModel):
    """
    Represents a request for inspection of delivered goods.
    Created by Store Keeper after goods are delivered by supplier.
    Can be linked to an ItemEntryRequest or created independently.
    """
    STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('in_progress', 'In Progress'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('completed', 'Completed'),
    )

    PACKAGING_CONDITION_CHOICES = (
        ('good', 'Good'),
        ('damaged', 'Damaged'),
        ('opened', 'Opened'),
        ('wet', 'Wet/Water Damaged'),
        ('other', 'Other'),
    )

    # Form identification
    form_number = models.CharField(max_length=50, unique=True, null=True, blank=True, default="INSP-DEFAULT",
                                  help_text="Unique inspection form number (e.g., INSP-2024-0001)")

    # Basic information
    title = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    po_number = models.CharField(max_length=50, verbose_name="Purchase Order Number")
    delivery_note_number = models.CharField(max_length=50, verbose_name="Delivery Note Number", blank=True)
    delivery_date = models.DateField(null=True, blank=True)
    bid_documents = models.TextField(blank=True, help_text="Technical specifications from bid documents")
    delivery_note = models.TextField(blank=True, help_text="Details from supplier delivery note")
    supplier = models.ForeignKey(Supplier, on_delete=models.PROTECT, related_name='inspection_requests')
    committee = models.ForeignKey(InspectionCommittee, on_delete=models.PROTECT, related_name='inspection_requests')

    # External packaging verification
    external_packaging_verified = models.BooleanField(default=False, help_text="Whether external packaging has been verified")
    external_packaging_condition = models.CharField(
        max_length=20,
        choices=PACKAGING_CONDITION_CHOICES,
        default='good',
        help_text="Condition of the external packaging"
    )
    packaging_condition_matches = models.BooleanField(default=True, help_text="Whether packaging condition matches delivery receipt")

    # Technical inspection
    technical_inspection_required = models.BooleanField(default=False, help_text="Whether technical inspection is required")
    technical_inspector = models.CharField(max_length=100, blank=True, help_text="Name of technical inspector")
    technical_test_results = models.TextField(blank=True, help_text="Results of technical tests performed")

    # Follow-up inspection
    requires_followup = models.BooleanField(default=False, help_text="Whether a follow-up inspection is required")
    followup_date = models.DateField(null=True, blank=True, help_text="Date for follow-up inspection")
    followup_notes = models.TextField(blank=True, help_text="Notes for follow-up inspection")

    # Store information
    store = models.ForeignKey(
        Store,
        on_delete=models.PROTECT,
        related_name='inspection_requests',
        null=True,
        blank=True,
        help_text="Store where the items are located"
    )

    # Link to entry request
    entry_request = models.ForeignKey(
        'ItemEntryRequest',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='inspection_requests',
        help_text="Entry request that initiated this inspection"
    )

    # User information
    requested_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='requested_inspections'
    )
    inspected_by = models.CharField(max_length=100, blank=True, help_text="Name of the inspector")
    approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_inspections',
        limit_choices_to={'groups__name': 'pao'}
    )
    approval_date = models.DateTimeField(null=True, blank=True)
    approval_comments = models.TextField(blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    batch = models.ForeignKey(
        Batch,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='inspection_requests'
    )

    # Inspection scheduling
    scheduled_date = models.DateTimeField(null=True, blank=True, help_text="Scheduled date for inspection")
    completed_date = models.DateTimeField(null=True, blank=True, help_text="Date when inspection was completed")

    class Meta:
        verbose_name = "Inspection Request"
        verbose_name_plural = "Inspection Requests"
        ordering = ['-created_at']

    def __str__(self):
        return f"Inspection Request: {self.title}"

    def assign_committee(self, committee):
        """Assign a committee to this inspection request"""
        self.committee = committee
        self.status = 'in_progress'
        self.save()

    def schedule_inspection(self, scheduled_date):
        """Schedule the inspection for a specific date"""
        self.scheduled_date = scheduled_date
        self.save()

    def complete_inspection(self, result_type, comments=None, inspected_by=None):
        """Complete the inspection with a result"""
        self.status = 'completed'
        self.completed_date = timezone.now()
        self.save()

        # Create inspection result
        inspection_result = InspectionResult.objects.create(
            inspection_request=self,
            result=result_type,
            comments=comments or '',
            inspection_date=timezone.now()
        )

        # Add inspectors
        if inspected_by:
            if isinstance(inspected_by, list):
                for inspector in inspected_by:
                    inspection_result.inspected_by.add(inspector)
            else:
                inspection_result.inspected_by.add(inspected_by)

        # Update entry request if linked
        if self.entry_request:
            if result_type == 'pass':
                # If passed, mark as ready for Model 19
                self.entry_request.workflow_status = 'assigned'  # Reset to assigned state for Model 19 generation
            elif result_type == 'fail':
                # If failed, mark as failed and update the entry request
                self.entry_request.inspection_failed = True
                self.entry_request.workflow_status = 'inspecting'  # Keep in inspecting state
                self.entry_request.save()

        return inspection_result

class InspectionResult(TimeStampedModel):
    """
    Represents the result of an inspection.
    Created by the Inspection Committee after inspecting the items.
    """
    RESULT_CHOICES = (
        ('passed', 'Passed - Accept All'),
        ('partial', 'Partial Acceptance'),
        ('failed', 'Failed - Reject All'),
    )

    inspection_request = models.OneToOneField(
        InspectionRequest,
        on_delete=models.CASCADE,
        related_name='result'
    )
    result = models.CharField(max_length=20, choices=RESULT_CHOICES)
    findings = models.TextField(blank=True, help_text="Findings and recommendations from the inspection")
    comments = models.TextField(blank=True)

    # Evidence photos
    has_evidence_photos = models.BooleanField(default=False, help_text="Whether evidence photos are attached")

    # Inspection committee
    inspected_by = models.ManyToManyField(
        User,
        related_name='conducted_inspections',
        limit_choices_to={'groups__name': 'inspector'}
    )
    inspection_date = models.DateTimeField(auto_now_add=True)

    # Signatures
    inspector_signature = models.CharField(max_length=100, blank=True, help_text="Name of the inspector who signed")
    inspector_signature_date = models.DateField(null=True, blank=True)
    approver_signature = models.CharField(max_length=100, blank=True, help_text="Name of the approver who signed")
    approver_signature_date = models.DateField(null=True, blank=True)

    class Meta:
        verbose_name = "Inspection Result"
        verbose_name_plural = "Inspection Results"
        ordering = ['-inspection_date']

    def __str__(self):
        return f"Inspection Result: {self.inspection_request.title} - {self.get_result_display()}"

    # Inspection committee only provides inspection results, not DSR
    # Store keeper will generate DSR based on inspection results if needed

    def generate_model19(self, user, store=None):
        """Generate a Model 19 based on this inspection result"""
        if self.result == 'failed':
            raise ValueError("Model 19 cannot be generated for failed inspections")

        # Use the store from the inspection request if not provided
        if not store and self.inspection_request.store:
            store = self.inspection_request.store

        if not store:
            raise ValueError("Store must be specified to generate Model 19")

        # Create Model 19 (this would be implemented based on your Model 19 structure)
        # For now, we'll just update the entry request if it exists
        if self.inspection_request.entry_request:
            self.inspection_request.entry_request.generate_model19(
                user,
                reference=f"M19-{self.inspection_request.entry_request.request_code}"
            )
            return f"Model 19 generated for {self.inspection_request.entry_request.request_code}"

        return "Model 19 generation process initiated"


class InspectionItem(TimeStampedModel):
    """
    Represents an individual item being inspected.
    Each inspection request can have multiple items.
    """
    CONDITION_CHOICES = (
        ('good', 'Good'),
        ('damaged', 'Damaged'),
        ('expired', 'Expired'),
        ('other', 'Other'),
    )

    SPEC_MATCH_CHOICES = (
        ('yes', 'Yes'),
        ('no', 'No'),
        ('partial', 'Partial'),
    )

    STATUS_CHOICES = (
        ('passed', 'Passed'),
        ('failed', 'Failed'),
    )

    inspection_request = models.ForeignKey(
        InspectionRequest,
        on_delete=models.CASCADE,
        related_name='items'
    )

    # Item information
    item_code = models.CharField(max_length=50, help_text="Item code or stock number")
    item_description = models.CharField(max_length=255, help_text="Description of the item")
    item_master = models.ForeignKey(
        ItemMaster,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='inspection_items'
    )

    # Quantities
    quantity_ordered = models.PositiveIntegerField(default=0, validators=[MinValueValidator(0)])
    quantity_received = models.PositiveIntegerField(default=0, validators=[MinValueValidator(0)])

    # Inspection details
    condition = models.CharField(
        max_length=20,
        choices=CONDITION_CHOICES,
        default='good',
        help_text="Physical condition of the item"
    )
    spec_match = models.CharField(
        max_length=20,
        choices=SPEC_MATCH_CHOICES,
        default='yes',
        help_text="Whether the item matches specifications"
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='passed',
        help_text="Inspection status of the item"
    )

    # Discrepancy information
    discrepancy_type = models.ForeignKey(
        DiscrepancyType,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='inspection_items'
    )
    discrepancy_notes = models.TextField(blank=True, help_text="Notes about any discrepancies")

    # Special handling
    quarantine_required = models.BooleanField(default=False, help_text="Whether the item requires quarantine")
    lab_test_required = models.BooleanField(default=False, help_text="Whether the item requires lab testing")
    technical_inspection_notes = models.TextField(blank=True, help_text="Notes from technical inspection")

    class Meta:
        verbose_name = "Inspection Item"
        verbose_name_plural = "Inspection Items"
        ordering = ['id']

    def __str__(self):
        return f"{self.item_description} - {self.get_status_display()}"


class InspectionEvidence(TimeStampedModel):
    """
    Represents evidence photos or attachments for an inspection.
    """
    inspection_request = models.ForeignKey(
        InspectionRequest,
        on_delete=models.CASCADE,
        related_name='evidence_photos'
    )
    inspection_item = models.ForeignKey(
        InspectionItem,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='evidence_photos',
        help_text="Specific item this evidence is related to (optional)"
    )
    title = models.CharField(max_length=100, help_text="Title or description of the evidence")
    file = models.FileField(upload_to='inspection_evidence/%Y/%m/%d/')
    description = models.TextField(blank=True, help_text="Additional description of the evidence")

    class Meta:
        verbose_name = "Inspection Evidence"
        verbose_name_plural = "Inspection Evidence"
        ordering = ['-created_at']

    def __str__(self):
        return f"Evidence: {self.title} for {self.inspection_request}"
