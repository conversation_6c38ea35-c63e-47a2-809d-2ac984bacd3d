from django.db import models
from django.core.validators import MinValueValidator
from django.core.exceptions import ValidationError
from django.utils import timezone
import uuid
from .base import TimeStampedModel
from .classification import SubClassification, EntryMode
from .specifications import (
    ItemType, ItemCategory, ItemBrand, ItemManufacturer,
    ItemShape, ItemSize, UnitOfMeasure, ItemQuality
)
from .status import ItemStatus, PropertyStatus, ApprovalStatus
from .storage import Store, Shelf
from .suppliers import Supplier
from serials.models import SerialVoucher, SerialVoucherCategory

class ItemMaster(TimeStampedModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # Hidden auto-generated fields
    item_code = models.CharField(max_length=50, unique=False, blank=True, null=True, editable=False)  # 3-digit sequential code (hidden)
    stock_classification = models.CharField(max_length=50, unique=True, blank=True, null=True, editable=False)  # Full 10-digit classification code
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    model = models.CharField(max_length=100, blank=True)

    # Classifications (Required for EFG compliance)
    sub_class = models.ForeignKey(SubClassification, on_delete=models.PROTECT)
    item_type = models.ForeignKey(ItemType, on_delete=models.PROTECT)
    item_category = models.ForeignKey(ItemCategory, on_delete=models.PROTECT)
    entry_mode = models.ForeignKey(EntryMode, on_delete=models.PROTECT)

    # Specifications
    brand = models.ForeignKey(ItemBrand, on_delete=models.PROTECT, blank=True, null=True)
    manufacturer = models.ForeignKey(ItemManufacturer, on_delete=models.PROTECT, blank=True, null=True)
    size = models.ForeignKey(ItemSize, on_delete=models.SET_NULL, null=True, blank=True)
    shape = models.ForeignKey(ItemShape, on_delete=models.SET_NULL, null=True, blank=True)
    quality = models.ForeignKey(ItemQuality, on_delete=models.PROTECT, null=True, blank=True)

    # Units and Pricing
    unit_of_measure = models.ForeignKey(UnitOfMeasure, on_delete=models.PROTECT)
    unit_price = models.DecimalField(max_digits=12, decimal_places=2, validators=[MinValueValidator(0)], default=0)
    depreciation_rate = models.FloatField(blank=True, null=True)

    # Stock Control
    min_stock = models.PositiveIntegerField(default=0)
    max_stock = models.PositiveIntegerField(blank=True, null=True)

    # Tags for special characteristics
    tags = models.ManyToManyField(
        'tags.ItemTag',
        blank=True,
        related_name='items',
        help_text="Tags for special characteristics like sensitive, fragile, etc."
    )

    class Meta:
        ordering = ['stock_classification', 'name']
        verbose_name = "Master Item"
        permissions = [
            ("can_approve_item", "Can approve item master records"),
        ]

    def __str__(self):
        return f"{self.stock_classification} - {self.name}"

    def save(self, *args, **kwargs):
        # Generate a sequential 3-digit number for item_code if it doesn't exist
        if not self.item_code:
            # Get last item in this classification to generate sequential code
            last_item = ItemMaster.objects.filter(
                sub_class__main_class=self.sub_class.main_class,
                sub_class=self.sub_class
            ).order_by('-stock_classification').first()

            if last_item and last_item.stock_classification and '-' in last_item.stock_classification:
                try:
                    # Extract the last 3 digits from the stock_classification
                    last_sequence = int(last_item.stock_classification.split('-')[-1])
                    # Generate next sequential number
                    self.item_code = f"{last_sequence + 1:03d}"
                except (ValueError, IndexError):
                    # Fallback to 001 if we can't parse the last sequence
                    self.item_code = "001"
            else:
                # First item in this classification
                self.item_code = "001"

        # Generate stock classification (10-digit code) if it doesn't exist
        if not self.stock_classification:
            # Format: MAIN-SUB-CODE (e.g., 1000-100-001)
            base_code = f"{self.sub_class.main_class.code}-{self.sub_class.code}"
            self.stock_classification = f"{base_code}-{self.item_code}"

        super().save(*args, **kwargs)

    @property
    def current_stock(self):
        return self.items.filter(status__code='in_stock').count()

class Batch(TimeStampedModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    master_item = models.ForeignKey(ItemMaster, on_delete=models.CASCADE, related_name='batches')
    supplier = models.ForeignKey(Supplier, on_delete=models.PROTECT, null=True, blank=True)
    purchase_price = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    quantity = models.PositiveIntegerField(null=True, blank=True)
    invoice_no = models.CharField(max_length=50, null=True, blank=True)
    received_date = models.DateField(default=timezone.now)
    expiry_date = models.DateField(null=True, blank=True)
    warranty_months = models.PositiveIntegerField(null=True, blank=True)
    received_by = models.ForeignKey('auth.User', on_delete=models.PROTECT, null=True, blank=True)
    approval_status = models.ForeignKey(ApprovalStatus, on_delete=models.PROTECT, null=True, blank=True)

    # Model 22 form fields
    series = models.CharField(max_length=50, blank=True)
    page_from = models.PositiveIntegerField(null=True, blank=True)
    page_to = models.PositiveIntegerField(null=True, blank=True)

    @property
    def remaining_quantity(self):
        return self.items.filter(status__code='in_stock').count()

    class Meta:
        verbose_name_plural = "Batches"
        ordering = ['-received_date']

    def __str__(self):
        return f"{self.master_item.name} - {self.invoice_no}"

    def save(self, *args, **kwargs):
        if not self.series:
            try:
                category = SerialVoucherCategory.objects.filter(title__icontains='Batch').first()
                if not category:
                    # Create a default category if none exists
                    category = SerialVoucherCategory.objects.create(
                        title="Batch Vouchers",
                        report_title="Batch Serial Vouchers"
                    )

                voucher = SerialVoucher.objects.filter(is_active=True, category=category).first()
                if not voucher:
                    raise ValidationError("No active serial voucher available for batch. Please create one in the admin panel.")

                self.series = voucher.get_next_serial()
            except Exception as e:
                raise ValidationError(f"Error generating batch series: {str(e)}")

        super().save(*args, **kwargs)

class Item(TimeStampedModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    master = models.ForeignKey(ItemMaster, on_delete=models.PROTECT, related_name='items')
    batch = models.ForeignKey(Batch, on_delete=models.PROTECT, related_name='items')

    # Unique Identifiers
    serial_number = models.CharField(max_length=100, unique=True, blank=True, null=True)
    registry_no = models.CharField(max_length=50, unique=True, blank=True)
    barcode = models.CharField(max_length=50, unique=True, blank=True)
    series = models.CharField(max_length=50, blank=True)

    # Status Tracking
    status = models.ForeignKey(ItemStatus, on_delete=models.PROTECT, null=True, blank=True)
    property_status = models.ForeignKey(PropertyStatus, on_delete=models.PROTECT, null=True, blank=True)
    approval_status = models.ForeignKey(ApprovalStatus, on_delete=models.PROTECT, null=True, blank=True)

    # Location
    store = models.ForeignKey(Store, on_delete=models.PROTECT, null=True, blank=True)
    shelf = models.ForeignKey(Shelf, on_delete=models.SET_NULL, null=True, blank=True)
    page_from = models.PositiveIntegerField(blank=True, null=True)
    page_to = models.PositiveIntegerField(blank=True, null=True)

    # Lifecycle
    received_date = models.DateTimeField(default=timezone.now)
    expiry_date = models.DateField(blank=True, null=True)

    class Meta:
        ordering = ['-received_date']
        indexes = [
            models.Index(fields=['serial_number']),
            models.Index(fields=['registry_no']),
            models.Index(fields=['status']),
        ]

    def save(self, *args, **kwargs):
        if not self.registry_no:
            try:
                category = SerialVoucherCategory.objects.filter(title__icontains='Item').first()
                if not category:
                    # Create a default category if none exists
                    category = SerialVoucherCategory.objects.create(
                        title="Item Vouchers",
                        report_title="Item Registry Vouchers"
                    )

                voucher = SerialVoucher.objects.filter(is_active=True, category=category).first()
                if not voucher:
                    raise ValidationError("No active serial voucher available for items. Please create one in the admin panel.")

                self.registry_no = voucher.get_next_serial()
            except Exception as e:
                raise ValidationError(f"Error generating item registry number: {str(e)}")

        if not self.barcode and self.registry_no:
            self.barcode = f"GOV-{self.registry_no}"

        # If series is not set, use the batch series
        if not self.series and self.batch and self.batch.series:
            self.series = self.batch.series

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.master.name} - {self.serial_number or self.registry_no}"