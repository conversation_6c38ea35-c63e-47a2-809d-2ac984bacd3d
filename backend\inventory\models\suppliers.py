from django.db import models
from .base import TimeStampedModel

class Supplier(TimeStampedModel):
    company_name = models.Char<PERSON>ield(max_length=255)
    contact_person = models.CharField(max_length=255)
    email = models.EmailField()
    phone = models.CharField(max_length=20)
    tin_number = models.CharField(max_length=20)
    vat_number = models.CharField(max_length=20, blank=True)
    website = models.URLField(blank=True)
    country = models.CharField(max_length=100)
    address = models.TextField()
    is_active = models.BooleanField(default=True)

    class Meta:
        ordering = ['company_name']

    def __str__(self):
        return self.company_name