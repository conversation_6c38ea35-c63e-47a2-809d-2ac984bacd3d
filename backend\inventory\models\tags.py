from django.db import models
from .base import TimeStampedModel


class ItemTag(TimeStampedModel):
    """
    Model for item tags used to categorize items by special characteristics
    like sensitive, very small, building materials, disposal methods, etc.
    """

    TAG_TYPES = [
        ('sensitivity', 'Sensitivity Level'),
        ('size', 'Size Category'),
        ('material', 'Material Type'),
        ('disposal', 'Disposal Method'),
        ('handling', 'Handling Requirements'),
        ('storage', 'Storage Requirements'),
        ('security', 'Security Level'),
        ('environmental', 'Environmental Impact'),
        ('maintenance', 'Maintenance Category'),
        ('other', 'Other'),
    ]

    TAG_COLORS = [
        ('red', 'Red'),
        ('orange', 'Orange'),
        ('yellow', 'Yellow'),
        ('green', 'Green'),
        ('blue', 'Blue'),
        ('purple', 'Purple'),
        ('pink', 'Pink'),
        ('brown', 'Brown'),
        ('gray', 'Gray'),
        ('black', 'Black'),
    ]

    name = models.CharField(
        max_length=100,
        unique=True,
        help_text="Name of the tag (e.g., 'Sensitive', 'Very Small', 'Building Material')"
    )

    slug = models.SlugField(
        max_length=100,
        unique=True,
        help_text="URL-friendly version of the tag name"
    )

    tag_type = models.CharField(
        max_length=20,
        choices=TAG_TYPES,
        default='other',
        help_text="Category of this tag"
    )

    description = models.TextField(
        blank=True,
        help_text="Detailed description of what this tag represents"
    )

    color = models.CharField(
        max_length=10,
        choices=TAG_COLORS,
        default='blue',
        help_text="Color for displaying this tag"
    )

    icon = models.CharField(
        max_length=50,
        blank=True,
        help_text="Material-UI icon name for this tag (e.g., 'Security', 'Warning', 'Build')"
    )

    is_active = models.BooleanField(
        default=True,
        help_text="Whether this tag is available for use"
    )

    priority = models.PositiveIntegerField(
        default=0,
        help_text="Display priority (higher numbers appear first)"
    )

    # Predefined tag examples
    PREDEFINED_TAGS = [
        {
            'name': 'Sensitive',
            'slug': 'sensitive',
            'tag_type': 'sensitivity',
            'description': 'Items that require special handling due to sensitivity',
            'color': 'red',
            'icon': 'Security',
            'priority': 100
        },
        {
            'name': 'Very Small',
            'slug': 'very-small',
            'tag_type': 'size',
            'description': 'Items that are very small and easy to lose',
            'color': 'orange',
            'icon': 'CropFree',
            'priority': 90
        },
        {
            'name': 'Building Material',
            'slug': 'building-material',
            'tag_type': 'material',
            'description': 'Construction and building materials',
            'color': 'brown',
            'icon': 'Build',
            'priority': 80
        },
        {
            'name': 'Dispose by Fire',
            'slug': 'dispose-by-fire',
            'tag_type': 'disposal',
            'description': 'Items that must be disposed of by burning',
            'color': 'red',
            'icon': 'Whatshot',
            'priority': 95
        },
        {
            'name': 'Fragile',
            'slug': 'fragile',
            'tag_type': 'handling',
            'description': 'Items that require careful handling',
            'color': 'yellow',
            'icon': 'Warning',
            'priority': 85
        },
        {
            'name': 'Hazardous',
            'slug': 'hazardous',
            'tag_type': 'environmental',
            'description': 'Items that pose environmental or health risks',
            'color': 'red',
            'icon': 'Dangerous',
            'priority': 98
        },
        {
            'name': 'Cold Storage',
            'slug': 'cold-storage',
            'tag_type': 'storage',
            'description': 'Items requiring refrigerated storage',
            'color': 'blue',
            'icon': 'AcUnit',
            'priority': 75
        },
        {
            'name': 'High Value',
            'slug': 'high-value',
            'tag_type': 'security',
            'description': 'High-value items requiring extra security',
            'color': 'purple',
            'icon': 'Diamond',
            'priority': 92
        }
    ]

    class Meta:
        verbose_name = "Item Tag"
        verbose_name_plural = "Item Tags"
        ordering = ['-priority', 'name']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            from django.utils.text import slugify
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    @classmethod
    def create_predefined_tags(cls):
        """Create predefined tags if they don't exist"""
        for tag_data in cls.PREDEFINED_TAGS:
            cls.objects.get_or_create(
                slug=tag_data['slug'],
                defaults=tag_data
            )

    def get_css_class(self):
        """Get CSS class for styling this tag"""
        return f"tag-{self.color}"

    def get_display_style(self):
        """Get inline style for displaying this tag"""
        color_map = {
            'red': '#f44336',
            'orange': '#ff9800',
            'yellow': '#ffeb3b',
            'green': '#4caf50',
            'blue': '#2196f3',
            'purple': '#9c27b0',
            'pink': '#e91e63',
            'brown': '#795548',
            'gray': '#9e9e9e',
            'black': '#424242',
        }
        return {
            'backgroundColor': color_map.get(self.color, '#2196f3'),
            'color': 'white' if self.color in ['red', 'purple', 'black', 'brown'] else 'black'
        }
