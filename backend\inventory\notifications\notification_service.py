"""
Enterprise Notification Service
Handles all system notifications with multiple channels and templates
"""

from typing import List, Dict, Any, Optional
from django.contrib.auth.models import User
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.conf import settings
from django.utils import timezone
from django.db import models
import logging

logger = logging.getLogger(__name__)


class NotificationChannel(models.TextChoices):
    """Available notification channels"""
    EMAIL = 'email', 'Email'
    SMS = 'sms', 'SMS'
    IN_APP = 'in_app', 'In-App'
    PUSH = 'push', 'Push Notification'
    WEBHOOK = 'webhook', 'Webhook'


class NotificationPriority(models.TextChoices):
    """Notification priority levels"""
    LOW = 'low', 'Low'
    MEDIUM = 'medium', 'Medium'
    HIGH = 'high', 'High'
    URGENT = 'urgent', 'Urgent'


class NotificationTemplate:
    """Notification template configuration"""
    
    def __init__(self, name: str, subject_template: str, body_template: str, 
                 channels: List[NotificationChannel], priority: NotificationPriority = NotificationPriority.MEDIUM):
        self.name = name
        self.subject_template = subject_template
        self.body_template = body_template
        self.channels = channels
        self.priority = priority


class NotificationService:
    """
    Enterprise notification service with multiple channels and templates
    """
    
    # Define notification templates
    TEMPLATES = {
        'approval_required': NotificationTemplate(
            name='approval_required',
            subject_template='Entry Request Requires Approval - {request_code}',
            body_template='notifications/approval_required.html',
            channels=[NotificationChannel.EMAIL, NotificationChannel.IN_APP],
            priority=NotificationPriority.HIGH
        ),
        'approval_completed': NotificationTemplate(
            name='approval_completed',
            subject_template='Entry Request {status} - {request_code}',
            body_template='notifications/approval_completed.html',
            channels=[NotificationChannel.EMAIL, NotificationChannel.IN_APP],
            priority=NotificationPriority.MEDIUM
        ),
        'store_assignment': NotificationTemplate(
            name='store_assignment',
            subject_template='Entry Request Assigned to Your Store - {request_code}',
            body_template='notifications/store_assignment.html',
            channels=[NotificationChannel.EMAIL, NotificationChannel.IN_APP],
            priority=NotificationPriority.HIGH
        ),
        'inspection_requested': NotificationTemplate(
            name='inspection_requested',
            subject_template='Inspection Requested - {request_code}',
            body_template='notifications/inspection_requested.html',
            channels=[NotificationChannel.EMAIL, NotificationChannel.IN_APP],
            priority=NotificationPriority.HIGH
        ),
        'inspector_assigned': NotificationTemplate(
            name='inspector_assigned',
            subject_template='You Have Been Assigned for Inspection - {request_code}',
            body_template='notifications/inspector_assigned.html',
            channels=[NotificationChannel.EMAIL, NotificationChannel.IN_APP],
            priority=NotificationPriority.HIGH
        ),
        'inspection_completed': NotificationTemplate(
            name='inspection_completed',
            subject_template='Inspection Completed - {request_code}',
            body_template='notifications/inspection_completed.html',
            channels=[NotificationChannel.EMAIL, NotificationChannel.IN_APP],
            priority=NotificationPriority.MEDIUM
        ),
        'deadline_reminder': NotificationTemplate(
            name='deadline_reminder',
            subject_template='Deadline Reminder - {request_code}',
            body_template='notifications/deadline_reminder.html',
            channels=[NotificationChannel.EMAIL, NotificationChannel.PUSH],
            priority=NotificationPriority.URGENT
        ),
    }
    
    def __init__(self):
        self.enabled_channels = self._get_enabled_channels()
    
    def notify_approval_required(self, entry_request, additional_context: Dict[str, Any] = None):
        """Notify approvers that a request requires approval"""
        try:
            # Get approvers
            recipients = self._get_approvers()
            
            # Prepare context
            context = {
                'entry_request': entry_request,
                'request_code': entry_request.request_code,
                'requester': entry_request.requested_by,
                'items_count': entry_request.items.count(),
                'total_value': entry_request.get_total_value(),
                'urgency': 'URGENT' if entry_request.is_urgent else 'NORMAL',
                'approval_url': self._get_approval_url(entry_request),
                **(additional_context or {})
            }
            
            # Send notifications
            self._send_notification('approval_required', recipients, context)
            
            logger.info(f"Approval required notification sent for {entry_request.request_code}")
            
        except Exception as e:
            logger.error(f"Failed to send approval required notification: {str(e)}")
    
    def notify_approval_completed(self, entry_request, approved: bool, additional_context: Dict[str, Any] = None):
        """Notify requester about approval decision"""
        try:
            recipients = [entry_request.requested_by]
            
            context = {
                'entry_request': entry_request,
                'request_code': entry_request.request_code,
                'status': 'Approved' if approved else 'Rejected',
                'approved': approved,
                'approver': entry_request.approved_by,
                'approval_date': entry_request.approval_date,
                'comments': entry_request.approval_comments,
                'next_steps': self._get_next_steps(entry_request, approved),
                **(additional_context or {})
            }
            
            self._send_notification('approval_completed', recipients, context)
            
            logger.info(f"Approval completed notification sent for {entry_request.request_code}")
            
        except Exception as e:
            logger.error(f"Failed to send approval completed notification: {str(e)}")
    
    def notify_store_assignment(self, entry_request, store, additional_context: Dict[str, Any] = None):
        """Notify store keepers about new assignment"""
        try:
            # Get store keepers for the assigned store
            recipients = self._get_store_keepers(store)
            
            context = {
                'entry_request': entry_request,
                'request_code': entry_request.request_code,
                'store': store,
                'assigned_by': entry_request.assigned_by,
                'assigned_date': entry_request.assigned_date,
                'items_count': entry_request.items.count(),
                'priority': 'HIGH' if entry_request.is_urgent else 'NORMAL',
                'store_url': self._get_store_url(entry_request),
                **(additional_context or {})
            }
            
            self._send_notification('store_assignment', recipients, context)
            
            logger.info(f"Store assignment notification sent for {entry_request.request_code}")
            
        except Exception as e:
            logger.error(f"Failed to send store assignment notification: {str(e)}")
    
    def notify_inspection_requested(self, entry_request, additional_context: Dict[str, Any] = None):
        """Notify inspection team about inspection request"""
        try:
            recipients = self._get_inspectors()
            
            context = {
                'entry_request': entry_request,
                'request_code': entry_request.request_code,
                'store': entry_request.assigned_store,
                'requested_by': entry_request.requested_by,
                'request_date': entry_request.inspection_request_date,
                'items_count': entry_request.items.count(),
                'inspection_url': self._get_inspection_url(entry_request),
                **(additional_context or {})
            }
            
            self._send_notification('inspection_requested', recipients, context)
            
            logger.info(f"Inspection requested notification sent for {entry_request.request_code}")
            
        except Exception as e:
            logger.error(f"Failed to send inspection requested notification: {str(e)}")
    
    def notify_inspector_assigned(self, item, inspector, additional_context: Dict[str, Any] = None):
        """Notify inspector about item assignment"""
        try:
            recipients = [inspector]
            
            context = {
                'item': item,
                'entry_request': item.entry_request,
                'request_code': item.entry_request.request_code,
                'inspector': inspector,
                'assigned_by': item.assigned_by if hasattr(item, 'assigned_by') else None,
                'item_description': item.item_description,
                'inspection_url': self._get_item_inspection_url(item),
                **(additional_context or {})
            }
            
            self._send_notification('inspector_assigned', recipients, context)
            
            logger.info(f"Inspector assigned notification sent for item {item.id}")
            
        except Exception as e:
            logger.error(f"Failed to send inspector assigned notification: {str(e)}")
    
    def notify_deadline_reminder(self, entry_request, days_remaining: int, additional_context: Dict[str, Any] = None):
        """Send deadline reminder notifications"""
        try:
            # Determine recipients based on current state
            recipients = self._get_deadline_recipients(entry_request)
            
            context = {
                'entry_request': entry_request,
                'request_code': entry_request.request_code,
                'days_remaining': days_remaining,
                'deadline_date': entry_request.expected_delivery_date,
                'current_status': entry_request.workflow_status,
                'action_required': self._get_required_action(entry_request),
                'action_url': self._get_action_url(entry_request),
                **(additional_context or {})
            }
            
            self._send_notification('deadline_reminder', recipients, context)
            
            logger.info(f"Deadline reminder sent for {entry_request.request_code}")
            
        except Exception as e:
            logger.error(f"Failed to send deadline reminder: {str(e)}")
    
    def _send_notification(self, template_name: str, recipients: List[User], context: Dict[str, Any]):
        """Send notification using configured template and channels"""
        template = self.TEMPLATES.get(template_name)
        if not template:
            logger.error(f"Unknown notification template: {template_name}")
            return
        
        for channel in template.channels:
            if channel in self.enabled_channels:
                try:
                    if channel == NotificationChannel.EMAIL:
                        self._send_email(template, recipients, context)
                    elif channel == NotificationChannel.IN_APP:
                        self._send_in_app(template, recipients, context)
                    elif channel == NotificationChannel.SMS:
                        self._send_sms(template, recipients, context)
                    elif channel == NotificationChannel.PUSH:
                        self._send_push(template, recipients, context)
                    elif channel == NotificationChannel.WEBHOOK:
                        self._send_webhook(template, recipients, context)
                        
                except Exception as e:
                    logger.error(f"Failed to send {channel} notification: {str(e)}")
    
    def _send_email(self, template: NotificationTemplate, recipients: List[User], context: Dict[str, Any]):
        """Send email notification"""
        if not getattr(settings, 'EMAIL_ENABLED', True):
            return
        
        subject = template.subject_template.format(**context)
        
        # Render HTML body
        html_body = render_to_string(template.body_template, context)
        
        # Get recipient emails
        recipient_emails = [user.email for user in recipients if user.email]
        
        if recipient_emails:
            send_mail(
                subject=subject,
                message='',  # Plain text version
                html_message=html_body,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=recipient_emails,
                fail_silently=False
            )
    
    def _send_in_app(self, template: NotificationTemplate, recipients: List[User], context: Dict[str, Any]):
        """Send in-app notification"""
        # TODO: Implement in-app notification storage
        pass
    
    def _send_sms(self, template: NotificationTemplate, recipients: List[User], context: Dict[str, Any]):
        """Send SMS notification"""
        # TODO: Implement SMS service integration
        pass
    
    def _send_push(self, template: NotificationTemplate, recipients: List[User], context: Dict[str, Any]):
        """Send push notification"""
        # TODO: Implement push notification service
        pass
    
    def _send_webhook(self, template: NotificationTemplate, recipients: List[User], context: Dict[str, Any]):
        """Send webhook notification"""
        # TODO: Implement webhook notifications
        pass
    
    # Helper methods for getting recipients and URLs
    def _get_approvers(self) -> List[User]:
        """Get users who can approve requests"""
        from django.contrib.auth.models import Group
        try:
            pao_group = Group.objects.get(name='PAO')
            return list(pao_group.user_set.filter(is_active=True))
        except Group.DoesNotExist:
            return []
    
    def _get_store_keepers(self, store) -> List[User]:
        """Get store keepers for a specific store"""
        # TODO: Implement store-user relationship
        from django.contrib.auth.models import Group
        try:
            sk_group = Group.objects.get(name='Store Keeper')
            return list(sk_group.user_set.filter(is_active=True))
        except Group.DoesNotExist:
            return []
    
    def _get_inspectors(self) -> List[User]:
        """Get available inspectors"""
        from django.contrib.auth.models import Group
        try:
            inspector_group = Group.objects.get(name='Inspector')
            return list(inspector_group.user_set.filter(is_active=True))
        except Group.DoesNotExist:
            return []
    
    def _get_enabled_channels(self) -> List[NotificationChannel]:
        """Get enabled notification channels from settings"""
        return getattr(settings, 'NOTIFICATION_CHANNELS', [
            NotificationChannel.EMAIL,
            NotificationChannel.IN_APP
        ])
    
    def _get_approval_url(self, entry_request) -> str:
        """Get URL for approval action"""
        return f"/procurement/item-receive/{entry_request.id}/"
    
    def _get_store_url(self, entry_request) -> str:
        """Get URL for store processing"""
        return f"/procurement/item-receive/{entry_request.id}/"
    
    def _get_inspection_url(self, entry_request) -> str:
        """Get URL for inspection"""
        return f"/inspection/requests/{entry_request.id}/"
    
    def _get_item_inspection_url(self, item) -> str:
        """Get URL for item inspection"""
        return f"/inspection/items/{item.id}/"
    
    def _get_next_steps(self, entry_request, approved: bool) -> str:
        """Get next steps description"""
        if approved:
            return "Your request has been approved and will be assigned to a store for processing."
        else:
            return "Please review the comments and resubmit if necessary."
    
    def _get_deadline_recipients(self, entry_request) -> List[User]:
        """Get recipients for deadline reminders based on current state"""
        # TODO: Implement based on workflow state
        return [entry_request.requested_by]
    
    def _get_required_action(self, entry_request) -> str:
        """Get required action description"""
        # TODO: Implement based on workflow state
        return "Please review and take appropriate action."
    
    def _get_action_url(self, entry_request) -> str:
        """Get action URL based on current state"""
        return f"/procurement/item-receive/{entry_request.id}/"
