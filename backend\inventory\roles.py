"""
Role-based permission definitions for the inventory management system.

This module defines the roles and their associated permissions based on the
following matrix:

Action                  | PAO | Storekeeper | Stock Clerk | Procurement | User Dept.
------------------------|-----|-------------|-------------|-------------|------------
Submit Requisition      | ✗   | ✗           | ✗           | ✗           | ✓
Approve/Adjust Req.     | ✓   | ✗           | ✗           | ✗           | ✓ (if adjusted)
Receive Stocks          | ✗   | ✓           | ✗           | ✓           | ✗
Issue Stocks            | ✗   | ✓           | ✗           | ✗           | ✗
Update Bin Cards        | ✗   | ✓           | ✗           | ✗           | ✗
Update Stock Records    | ✗   | ✗           | ✓           | ✗           | ✗
Initiate Disposal       | ✓   | ✗           | ✗           | ✗           | ✗
Conduct Stock Audit     | ✓   | ✗ (assist)  | ✓           | ✗           | ✗
"""

# Role definitions
ROLE_PAO = 'PAO'  # Property Administration Officer
ROLE_CENTRAL_PAO = 'Central PAO'  # Central Property Administration Officer
ROLE_STOREKEEPER = 'Storekeeper'
ROLE_STOCK_CLERK = 'Stock Clerk'
ROLE_PROCUREMENT = 'Procurement'
ROLE_USER_DEPT = 'User Department'
ROLE_INSPECTOR = 'Inspector'  # Inspection Committee Member

# All available roles
ALL_ROLES = [
    ROLE_PAO,
    ROLE_CENTRAL_PAO,
    ROLE_STOREKEEPER,
    ROLE_STOCK_CLERK,
    ROLE_PROCUREMENT,
    ROLE_USER_DEPT,
    ROLE_INSPECTOR
]

# Permission definitions
PERM_SUBMIT_REQUISITION = 'submit_requisition'
PERM_APPROVE_REQUISITION = 'approve_requisition'
PERM_ADJUST_REQUISITION = 'adjust_requisition'
PERM_APPROVE_CROSS_STORE = 'approve_cross_store_transfer'
PERM_CONFIRM_REQUISITION = 'confirm_requisition'
PERM_RECEIVE_STOCKS = 'receive_stocks'
PERM_ISSUE_STOCKS = 'issue_stocks'
PERM_UPDATE_BIN_CARDS = 'update_bin_cards'
PERM_UPDATE_STOCK_RECORDS = 'update_stock_records'
PERM_INITIATE_DISPOSAL = 'initiate_disposal'
PERM_CONDUCT_STOCK_AUDIT = 'conduct_stock_audit'
PERM_ASSIST_STOCK_AUDIT = 'assist_stock_audit'
PERM_CONDUCT_INSPECTION = 'conduct_inspection'
PERM_APPROVE_INSPECTION = 'approve_inspection'
PERM_ASSIGN_INSPECTION = 'assign_inspection'

# Role to permission mapping
ROLE_PERMISSIONS = {
    ROLE_PAO: [
        PERM_APPROVE_REQUISITION,
        PERM_ADJUST_REQUISITION,
        PERM_INITIATE_DISPOSAL,
        PERM_CONDUCT_STOCK_AUDIT,
    ],
    ROLE_CENTRAL_PAO: [
        PERM_APPROVE_REQUISITION,
        PERM_ADJUST_REQUISITION,
        PERM_APPROVE_CROSS_STORE,
        PERM_INITIATE_DISPOSAL,
        PERM_CONDUCT_STOCK_AUDIT,
    ],
    ROLE_STOREKEEPER: [
        PERM_RECEIVE_STOCKS,
        PERM_ISSUE_STOCKS,
        PERM_UPDATE_BIN_CARDS,
        PERM_ASSIST_STOCK_AUDIT,
    ],
    ROLE_STOCK_CLERK: [
        PERM_UPDATE_STOCK_RECORDS,
        PERM_CONDUCT_STOCK_AUDIT,
    ],
    ROLE_PROCUREMENT: [
        PERM_RECEIVE_STOCKS,
    ],
    ROLE_USER_DEPT: [
        PERM_SUBMIT_REQUISITION,
        PERM_CONFIRM_REQUISITION,
        # User departments can approve/adjust requisitions only if they were adjusted
        # This is handled in the permission class logic
    ],
    ROLE_INSPECTOR: [
        PERM_CONDUCT_INSPECTION,
        PERM_APPROVE_INSPECTION,
    ],
}

# Django group to role mapping
GROUP_TO_ROLE = {
    'Admin': ROLE_CENTRAL_PAO,  # Admin users are considered Central PAO
    'Inventory Manager': ROLE_PAO,  # Inventory managers are considered PAO
    'Central PAO': ROLE_CENTRAL_PAO,  # New group for Central PAO
    'Storekeeper': ROLE_STOREKEEPER,
    'Stock Clerk': ROLE_STOCK_CLERK,
    'Procurement': ROLE_PROCUREMENT,
    'User Department': ROLE_USER_DEPT,
    'Inspector': ROLE_INSPECTOR,
}

def get_user_roles(user):
    """
    Get all roles assigned to a user based on their groups.

    Args:
        user: The Django user object

    Returns:
        list: List of role names assigned to the user
    """
    if user.is_superuser:
        # Superusers have Central PAO role by default
        roles = [ROLE_CENTRAL_PAO]
    elif user.is_staff:
        # Staff users have PAO role by default
        roles = [ROLE_PAO]
    else:
        roles = []

    # Add roles based on user's groups
    for group in user.groups.all():
        if group.name in GROUP_TO_ROLE:
            role = GROUP_TO_ROLE[group.name]
            if role not in roles:
                roles.append(role)

    return roles

def user_has_role(user, role):
    """
    Check if a user has a specific role.

    Args:
        user: The Django user object
        role: The role name to check

    Returns:
        bool: True if the user has the role, False otherwise
    """
    return role in get_user_roles(user)

def user_has_permission(user, permission):
    """
    Check if a user has a specific permission based on their roles.

    Args:
        user: The Django user object
        permission: The permission name to check

    Returns:
        bool: True if the user has the permission, False otherwise
    """
    user_roles = get_user_roles(user)

    for role in user_roles:
        if role in ROLE_PERMISSIONS and permission in ROLE_PERMISSIONS[role]:
            return True

    return False
