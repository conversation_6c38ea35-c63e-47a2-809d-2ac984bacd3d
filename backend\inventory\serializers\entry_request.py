from rest_framework import serializers
from django.contrib.auth.models import User
from ..models.entry_request import ItemEntryRequest, ItemEntryRequestAttachment, ItemEntryRequestItem
from ..serializers.users import UserSerializer
from ..serializers.suppliers import SupplierSerializer
from ..serializers.status import ApprovalStatusSerializer
from ..serializers.classification import MainClassificationSerializer
from ..serializers.storage import StoreSerializer

class ItemEntryRequestAttachmentSerializer(serializers.ModelSerializer):
    uploaded_by_name = serializers.SerializerMethodField(read_only=True)
    file_extension = serializers.SerializerMethodField(read_only=True)
    is_image = serializers.SerializerMethodField(read_only=True)
    is_document = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ItemEntryRequestAttachment
        fields = '__all__'

    def get_uploaded_by_name(self, obj):
        if obj.uploaded_by:
            return f"{obj.uploaded_by.first_name} {obj.uploaded_by.last_name}".strip() or obj.uploaded_by.username
        return None

    def get_file_extension(self, obj):
        return obj.get_file_extension()

    def get_is_image(self, obj):
        return obj.is_image()

    def get_is_document(self, obj):
        return obj.is_document()


class ItemEntryRequestItemSerializer(serializers.ModelSerializer):
    total_price = serializers.SerializerMethodField(read_only=True)
    main_classification_name = serializers.CharField(source='main_classification.name', read_only=True)
    assigned_inspector_name = serializers.SerializerMethodField(read_only=True)
    assigned_committee_name = serializers.SerializerMethodField(read_only=True)
    inspection_status_display = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ItemEntryRequestItem
        fields = [
            'id', 'entry_request', 'item_description', 'specifications',
            'quantity', 'unit_price', 'main_classification', 'main_classification_name',
            'is_received', 'received_quantity', 'received_date', 'total_price',
            'assigned_inspector', 'assigned_inspector_name', 'assigned_committee', 'assigned_committee_name',
            'inspection_status', 'inspection_status_display', 'inspection_notes', 'inspection_date',
            'created_at', 'updated_at'
        ]

    def get_total_price(self, obj):
        return obj.get_total_price()

    def get_assigned_inspector_name(self, obj):
        try:
            if hasattr(obj, 'assigned_inspector') and obj.assigned_inspector:
                return f"{obj.assigned_inspector.first_name} {obj.assigned_inspector.last_name}".strip() or obj.assigned_inspector.username
        except AttributeError:
            pass
        return None

    def get_assigned_committee_name(self, obj):
        try:
            if hasattr(obj, 'assigned_committee') and obj.assigned_committee:
                return obj.assigned_committee.title
        except AttributeError:
            pass
        return None

    def get_inspection_status_display(self, obj):
        try:
            if hasattr(obj, 'inspection_status'):
                return obj.get_inspection_status_display()
        except (AttributeError, TypeError):
            pass
        return 'Not Required'

class ItemEntryRequestSerializer(serializers.ModelSerializer):
    requested_by_name = serializers.SerializerMethodField(read_only=True)
    approved_by_name = serializers.SerializerMethodField(read_only=True)
    supplier_name = serializers.CharField(source='supplier.company_name', read_only=True)
    status_name = serializers.CharField(source='status.name', read_only=True)
    main_classification_name = serializers.CharField(source='main_classification.name', read_only=True, allow_null=True)
    target_store_name = serializers.CharField(source='target_store.name', read_only=True, allow_null=True)
    assigned_store_name = serializers.CharField(source='assigned_store.name', read_only=True, allow_null=True)
    attachments = ItemEntryRequestAttachmentSerializer(many=True, read_only=True)
    items = ItemEntryRequestItemSerializer(many=True, read_only=True)
    total_items_count = serializers.SerializerMethodField(read_only=True)
    received_items_count = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ItemEntryRequest
        # Use explicit fields to avoid issues with commented-out fields
        fields = [
            'id', 'request_code', 'title', 'description', 'po_number', 'po_date',
            'supplier', 'supplier_name', 'status', 'status_name', 'workflow_status',
            'main_classification', 'main_classification_name', 'target_store', 'target_store_name',
            'assigned_store', 'assigned_store_name', 'assigned_by', 'assigned_date',
            'delivery_note', 'additional_notes', 'expected_delivery_date', 'actual_delivery_date',
            'is_urgent', 'is_complete', 'inspection_requested', 'inspection_request_date',
            'inspection_failed', 'model19_generated', 'model19_reference',
            'dsr_generated', 'dsr_reference', 'requested_by', 'requested_by_name',
            'approved_by', 'approved_by_name', 'approval_date', 'approval_comments',
            'attachments', 'items', 'total_items_count', 'received_items_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = ('request_code', 'created_at', 'updated_at', 'requested_by', 'approved_by', 'approval_date')

    def get_requested_by_name(self, obj):
        if obj.requested_by:
            return f"{obj.requested_by.first_name} {obj.requested_by.last_name}".strip() or obj.requested_by.username
        return None

    def get_approved_by_name(self, obj):
        if obj.approved_by:
            return f"{obj.approved_by.first_name} {obj.approved_by.last_name}".strip() or obj.approved_by.username
        return None

    def get_total_items_count(self, obj):
        return obj.items.count()

    def get_received_items_count(self, obj):
        return obj.items.filter(is_received=True).count()

class ItemEntryRequestListSerializer(serializers.ModelSerializer):
    requested_by_name = serializers.SerializerMethodField(read_only=True)
    supplier_name = serializers.CharField(source='supplier.company_name', read_only=True)
    status_name = serializers.CharField(source='status.name', read_only=True)
    main_classification_name = serializers.CharField(source='main_classification.name', read_only=True, allow_null=True)
    target_store_name = serializers.CharField(source='target_store.name', read_only=True, allow_null=True)
    total_items_count = serializers.SerializerMethodField(read_only=True)
    received_items_count = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ItemEntryRequest
        fields = ['id', 'request_code', 'title', 'po_number', 'supplier', 'supplier_name',
                 'status', 'status_name', 'workflow_status', 'requested_by', 'requested_by_name',
                 'main_classification', 'main_classification_name', 'target_store', 'target_store_name',
                 'is_urgent', 'is_complete', 'total_items_count', 'received_items_count',
                 'expected_delivery_date', 'actual_delivery_date', 'created_at', 'updated_at']

    def get_requested_by_name(self, obj):
        if obj.requested_by:
            return f"{obj.requested_by.first_name} {obj.requested_by.last_name}".strip() or obj.requested_by.username
        return None

    def get_total_items_count(self, obj):
        return obj.items.count()

    def get_received_items_count(self, obj):
        return obj.items.filter(is_received=True).count()

class ItemEntryRequestDetailSerializer(serializers.ModelSerializer):
    requested_by = UserSerializer(read_only=True)
    approved_by = UserSerializer(read_only=True)
    supplier = SupplierSerializer(read_only=True)
    status = ApprovalStatusSerializer(read_only=True)
    main_classification = MainClassificationSerializer(read_only=True)
    target_store = StoreSerializer(read_only=True)
    assigned_store = StoreSerializer(read_only=True)
    attachments = ItemEntryRequestAttachmentSerializer(many=True, read_only=True)
    items = ItemEntryRequestItemSerializer(many=True, read_only=True)
    total_items_count = serializers.SerializerMethodField(read_only=True)
    received_items_count = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ItemEntryRequest
        # Use explicit fields to avoid issues with commented-out fields
        fields = [
            'id', 'request_code', 'title', 'description', 'po_number', 'po_date',
            'supplier', 'status', 'workflow_status', 'main_classification', 'target_store',
            'assigned_store', 'assigned_by', 'assigned_date', 'delivery_note', 'additional_notes',
            'expected_delivery_date', 'actual_delivery_date', 'is_urgent', 'is_complete',
            'inspection_requested', 'inspection_request_date', 'inspection_failed',
            'model19_generated', 'model19_reference', 'dsr_generated', 'dsr_reference',
            'requested_by', 'approved_by', 'approval_date', 'approval_comments',
            'attachments', 'items', 'total_items_count', 'received_items_count',
            'created_at', 'updated_at'
        ]

    def get_total_items_count(self, obj):
        return obj.items.count()

    def get_received_items_count(self, obj):
        return obj.items.filter(is_received=True).count()
