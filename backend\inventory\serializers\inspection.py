from rest_framework import serializers
from django.contrib.auth.models import User
from ..models.inspection import InspectionCommittee, InspectionRequest, InspectionResult, InspectionItem, InspectionEvidence
from ..serializers.reports import DiscrepancyTypeSerializer
from ..serializers.items import ItemMasterListSerializer

class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'first_name', 'last_name', 'email']

class InspectionCommitteeSerializer(serializers.ModelSerializer):
    users_detail = UserSerializer(source='users', many=True, read_only=True)
    main_classifications_details = serializers.SerializerMethodField()

    class Meta:
        model = InspectionCommittee
        fields = '__all__'

    def get_main_classifications_details(self, obj):
        from ..serializers.classification import MainClassificationListSerializer
        return MainClassificationListSerializer(obj.main_classifications.all(), many=True).data

    def create(self, validated_data):
        users_data = validated_data.pop('users', [])
        main_classifications_data = validated_data.pop('main_classifications', [])

        committee = InspectionCommittee.objects.create(**validated_data)

        if users_data:
            committee.users.set(users_data)
        if main_classifications_data:
            committee.main_classifications.set(main_classifications_data)

        return committee

    def update(self, instance, validated_data):
        users_data = validated_data.pop('users', None)
        main_classifications_data = validated_data.pop('main_classifications', None)

        # Update basic fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # Update many-to-many relationships
        if users_data is not None:
            instance.users.set(users_data)
        if main_classifications_data is not None:
            instance.main_classifications.set(main_classifications_data)

        return instance

class InspectionCommitteeListSerializer(serializers.ModelSerializer):
    user_count = serializers.SerializerMethodField()
    main_classification_name = serializers.CharField(source='main_classification.name', read_only=True, default='')
    classifications_count = serializers.SerializerMethodField()

    class Meta:
        model = InspectionCommittee
        fields = ['id', 'title', 'main_classification', 'main_classification_name',
                 'classifications_count', 'user_count', 'is_active']

    def get_user_count(self, obj):
        return obj.users.count()

    def get_classifications_count(self, obj):
        return obj.main_classifications.count()

class InspectionRequestSerializer(serializers.ModelSerializer):
    requested_by_name = serializers.SerializerMethodField(read_only=True)
    approved_by_name = serializers.SerializerMethodField(read_only=True)
    committee_name = serializers.CharField(source='committee.title', read_only=True)
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    external_packaging_condition_display = serializers.CharField(source='get_external_packaging_condition_display', read_only=True)
    items = serializers.SerializerMethodField(read_only=True)
    evidence_photos = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = InspectionRequest
        fields = '__all__'

    def get_requested_by_name(self, obj):
        if obj.requested_by:
            return f"{obj.requested_by.first_name} {obj.requested_by.last_name}".strip() or obj.requested_by.username
        return None

    def get_approved_by_name(self, obj):
        if obj.approved_by:
            return f"{obj.approved_by.first_name} {obj.approved_by.last_name}".strip() or obj.approved_by.username
        return None

    def get_items(self, obj):
        from .inspection import InspectionItemSerializer
        return InspectionItemSerializer(obj.items.all(), many=True, context=self.context).data

    def get_evidence_photos(self, obj):
        from .inspection import InspectionEvidenceSerializer
        return InspectionEvidenceSerializer(obj.evidence_photos.all(), many=True, context=self.context).data

class InspectionRequestListSerializer(serializers.ModelSerializer):
    requested_by_name = serializers.SerializerMethodField(read_only=True)
    committee_name = serializers.CharField(source='committee.title', read_only=True)
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    items_count = serializers.SerializerMethodField(read_only=True)
    external_packaging_condition_display = serializers.CharField(source='get_external_packaging_condition_display', read_only=True)

    class Meta:
        model = InspectionRequest
        fields = ['id', 'form_number', 'title', 'po_number', 'delivery_note_number', 'delivery_date',
                  'supplier', 'supplier_name', 'committee', 'committee_name',
                  'requested_by', 'requested_by_name', 'inspected_by',
                  'status', 'status_display', 'external_packaging_condition', 'external_packaging_condition_display',
                  'technical_inspection_required', 'requires_followup', 'items_count', 'created_at']

    def get_requested_by_name(self, obj):
        if obj.requested_by:
            return f"{obj.requested_by.first_name} {obj.requested_by.last_name}".strip() or obj.requested_by.username
        return None

    def get_items_count(self, obj):
        return obj.items.count()

class InspectionItemSerializer(serializers.ModelSerializer):
    item_master_details = ItemMasterListSerializer(source='item_master', read_only=True)
    discrepancy_type_details = DiscrepancyTypeSerializer(source='discrepancy_type', read_only=True)
    condition_display = serializers.CharField(source='get_condition_display', read_only=True)
    spec_match_display = serializers.CharField(source='get_spec_match_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)

    class Meta:
        model = InspectionItem
        fields = '__all__'

class InspectionEvidenceSerializer(serializers.ModelSerializer):
    file_url = serializers.SerializerMethodField()

    class Meta:
        model = InspectionEvidence
        fields = '__all__'

    def get_file_url(self, obj):
        request = self.context.get('request')
        if obj.file and hasattr(obj.file, 'url') and request:
            return request.build_absolute_uri(obj.file.url)
        return None

class InspectionResultSerializer(serializers.ModelSerializer):
    inspected_by_details = UserSerializer(source='inspected_by', many=True, read_only=True)
    result_display = serializers.CharField(source='get_result_display', read_only=True)

    class Meta:
        model = InspectionResult
        fields = '__all__'
