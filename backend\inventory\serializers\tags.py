from rest_framework import serializers
from ..models.tags import ItemTag


class ItemTagSerializer(serializers.ModelSerializer):
    """Serializer for ItemTag model"""
    
    display_style = serializers.SerializerMethodField(read_only=True)
    css_class = serializers.SerializerMethodField(read_only=True)
    items_count = serializers.SerializerMethodField(read_only=True)
    
    class Meta:
        model = ItemTag
        fields = [
            'id', 'name', 'slug', 'tag_type', 'description', 'color', 'icon',
            'is_active', 'priority', 'display_style', 'css_class', 'items_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['slug', 'created_at', 'updated_at']
    
    def get_display_style(self, obj):
        """Get inline style for displaying this tag"""
        return obj.get_display_style()
    
    def get_css_class(self, obj):
        """Get CSS class for styling this tag"""
        return obj.get_css_class()
    
    def get_items_count(self, obj):
        """Get count of items using this tag"""
        return obj.items.count()


class ItemTagListSerializer(serializers.ModelSerializer):
    """Simplified serializer for listing tags"""
    
    display_style = serializers.SerializerMethodField(read_only=True)
    items_count = serializers.SerializerMethodField(read_only=True)
    
    class Meta:
        model = ItemTag
        fields = [
            'id', 'name', 'slug', 'tag_type', 'color', 'icon',
            'priority', 'display_style', 'items_count'
        ]
    
    def get_display_style(self, obj):
        """Get inline style for displaying this tag"""
        return obj.get_display_style()
    
    def get_items_count(self, obj):
        """Get count of items using this tag"""
        return obj.items.count()


class ItemTagCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating new tags"""
    
    class Meta:
        model = ItemTag
        fields = [
            'name', 'tag_type', 'description', 'color', 'icon', 'priority'
        ]
    
    def validate_name(self, value):
        """Validate tag name is unique"""
        if ItemTag.objects.filter(name__iexact=value).exists():
            raise serializers.ValidationError("A tag with this name already exists.")
        return value
