"""
Enterprise Service Layer for Entry Request Management
Implements business logic, validation, and orchestration
"""

from typing import Dict, List, Optional, Tuple, Any
from django.db import transaction
from django.core.exceptions import ValidationError, PermissionDenied
from django.contrib.auth.models import User
from django.utils import timezone
from django.core.cache import cache
import logging

from ..models.entry_request import ItemEntryRequest, ItemEntryRequestItem
from ..models.storage import Store
from ..models.audit import AuditTrail, WorkflowAuditTrail
from ..workflows.entry_request_workflow import EntryRequestWorkflow, WorkflowAction, WorkflowState
from ..notifications.notification_service import NotificationService

logger = logging.getLogger(__name__)


class EntryRequestService:
    """
    Enterprise service for Entry Request operations
    Provides high-level business operations with proper validation and audit trail
    """
    
    def __init__(self):
        self.workflow = EntryRequestWorkflow()
        self.notification_service = NotificationService()
    
    @transaction.atomic
    def create_entry_request(self, user: User, data: Dict[str, Any], 
                           request=None) -> Tuple[bool, str, Optional[ItemEntryRequest]]:
        """
        Create a new entry request with validation and audit trail
        
        Args:
            user: User creating the request
            data: Request data
            request: HTTP request for audit trail
            
        Returns:
            Tuple of (success, message, entry_request)
        """
        try:
            # Validate user permissions
            if not self._can_create_request(user):
                raise PermissionDenied("User does not have permission to create requests")
            
            # Validate business rules
            validation_result = self._validate_request_data(data)
            if not validation_result['valid']:
                return False, validation_result['message'], None
            
            # Create the request
            entry_request = ItemEntryRequest.objects.create(
                requested_by=user,
                workflow_status=WorkflowState.DRAFT.value,
                **self._extract_request_fields(data)
            )
            
            # Create items
            items_data = data.get('items', [])
            for item_data in items_data:
                ItemEntryRequestItem.objects.create(
                    entry_request=entry_request,
                    **self._extract_item_fields(item_data)
                )
            
            # Create audit trail
            AuditTrail.log_action(
                user=user,
                action_type='create',
                description=f"Created entry request {entry_request.request_code}",
                content_object=entry_request,
                new_values=data,
                request=request,
                category='entry_request',
                tags=['creation', 'entry_request']
            )
            
            # Cache invalidation
            self._invalidate_request_cache(user)
            
            logger.info(f"Entry request {entry_request.request_code} created by {user.username}")
            
            return True, "Entry request created successfully", entry_request
            
        except Exception as e:
            logger.error(f"Failed to create entry request: {str(e)}")
            return False, f"Failed to create entry request: {str(e)}", None
    
    @transaction.atomic
    def submit_for_approval(self, entry_request: ItemEntryRequest, user: User, 
                           request=None) -> Tuple[bool, str]:
        """
        Submit entry request for approval
        """
        try:
            # Validate submission
            if not self._can_submit_request(entry_request, user):
                raise PermissionDenied("Cannot submit this request")
            
            # Execute workflow transition
            success, message = self.workflow.execute_transition(
                entry_request, WorkflowAction.SUBMIT, user
            )
            
            if success:
                # Send notifications
                self.notification_service.notify_approval_required(entry_request)
                
                # Create audit trail
                AuditTrail.log_action(
                    user=user,
                    action_type='workflow',
                    description=f"Submitted entry request {entry_request.request_code} for approval",
                    content_object=entry_request,
                    request=request,
                    category='workflow',
                    tags=['submission', 'approval_required']
                )
                
                self._invalidate_request_cache(user)
            
            return success, message
            
        except Exception as e:
            logger.error(f"Failed to submit request: {str(e)}")
            return False, f"Failed to submit request: {str(e)}"
    
    @transaction.atomic
    def approve_request(self, entry_request: ItemEntryRequest, user: User, 
                       comments: str = "", request=None) -> Tuple[bool, str]:
        """
        Approve entry request
        """
        try:
            # Validate approval
            if not self._can_approve_request(entry_request, user):
                raise PermissionDenied("Cannot approve this request")
            
            # Execute workflow transition
            success, message = self.workflow.execute_transition(
                entry_request, WorkflowAction.APPROVE, user, comments=comments
            )
            
            if success:
                # Send notifications
                self.notification_service.notify_approval_completed(entry_request, approved=True)
                
                # Create audit trail
                AuditTrail.log_action(
                    user=user,
                    action_type='approval',
                    description=f"Approved entry request {entry_request.request_code}",
                    content_object=entry_request,
                    metadata={'comments': comments},
                    request=request,
                    category='approval',
                    tags=['approval', 'approved']
                )
                
                self._invalidate_request_cache()
            
            return success, message
            
        except Exception as e:
            logger.error(f"Failed to approve request: {str(e)}")
            return False, f"Failed to approve request: {str(e)}"
    
    @transaction.atomic
    def assign_to_store(self, entry_request: ItemEntryRequest, store: Store, 
                       user: User, comments: str = "", request=None) -> Tuple[bool, str]:
        """
        Assign entry request to store
        """
        try:
            # Validate assignment
            if not self._can_assign_to_store(entry_request, user):
                raise PermissionDenied("Cannot assign this request to store")
            
            # Execute workflow transition
            success, message = self.workflow.execute_transition(
                entry_request, WorkflowAction.ASSIGN_STORE, user, 
                store_id=store.id, comments=comments
            )
            
            if success:
                # Send notifications
                self.notification_service.notify_store_assignment(entry_request, store)
                
                # Create audit trail
                AuditTrail.log_action(
                    user=user,
                    action_type='assignment',
                    description=f"Assigned entry request {entry_request.request_code} to store {store.name}",
                    content_object=entry_request,
                    metadata={'store_id': store.id, 'store_name': store.name, 'comments': comments},
                    request=request,
                    category='assignment',
                    tags=['store_assignment', 'workflow']
                )
                
                self._invalidate_request_cache()
            
            return success, message
            
        except Exception as e:
            logger.error(f"Failed to assign to store: {str(e)}")
            return False, f"Failed to assign to store: {str(e)}"
    
    @transaction.atomic
    def request_inspection(self, entry_request: ItemEntryRequest, user: User, 
                          comments: str = "", request=None) -> Tuple[bool, str]:
        """
        Request inspection for entry request
        """
        try:
            # Validate inspection request
            if not self._can_request_inspection(entry_request, user):
                raise PermissionDenied("Cannot request inspection for this request")
            
            # Execute workflow transition
            success, message = self.workflow.execute_transition(
                entry_request, WorkflowAction.REQUEST_INSPECTION, user, comments=comments
            )
            
            if success:
                # Send notifications
                self.notification_service.notify_inspection_requested(entry_request)
                
                # Create audit trail
                AuditTrail.log_action(
                    user=user,
                    action_type='inspection',
                    description=f"Requested inspection for entry request {entry_request.request_code}",
                    content_object=entry_request,
                    metadata={'comments': comments},
                    request=request,
                    category='inspection',
                    tags=['inspection_request', 'workflow']
                )
                
                self._invalidate_request_cache()
            
            return success, message
            
        except Exception as e:
            logger.error(f"Failed to request inspection: {str(e)}")
            return False, f"Failed to request inspection: {str(e)}"
    
    def get_user_requests(self, user: User, filters: Dict[str, Any] = None) -> List[ItemEntryRequest]:
        """
        Get requests accessible to user with caching
        """
        cache_key = f"user_requests_{user.id}_{hash(str(filters))}"
        cached_result = cache.get(cache_key)
        
        if cached_result is not None:
            return cached_result
        
        # Build queryset based on user role
        queryset = ItemEntryRequest.objects.all()
        
        user_role = self.workflow._get_user_role(user)
        if user_role.value == 'requester':
            queryset = queryset.filter(requested_by=user)
        elif user_role.value == 'store_keeper':
            queryset = queryset.filter(assigned_store__in=self._get_user_stores(user))
        
        # Apply filters
        if filters:
            queryset = self._apply_filters(queryset, filters)
        
        result = list(queryset.select_related('requested_by', 'approved_by', 'assigned_store')
                     .prefetch_related('items', 'attachments'))
        
        # Cache for 5 minutes
        cache.set(cache_key, result, 300)
        
        return result
    
    # Private helper methods
    def _can_create_request(self, user: User) -> bool:
        """Check if user can create requests"""
        return user.has_perm('inventory.add_itementryrequest')
    
    def _can_submit_request(self, entry_request: ItemEntryRequest, user: User) -> bool:
        """Check if user can submit request"""
        return (entry_request.requested_by == user and 
                entry_request.workflow_status == WorkflowState.DRAFT.value)
    
    def _can_approve_request(self, entry_request: ItemEntryRequest, user: User) -> bool:
        """Check if user can approve request"""
        user_role = self.workflow._get_user_role(user)
        return user_role.value in ['pao', 'admin']
    
    def _can_assign_to_store(self, entry_request: ItemEntryRequest, user: User) -> bool:
        """Check if user can assign to store"""
        user_role = self.workflow._get_user_role(user)
        return user_role.value in ['pao', 'admin']
    
    def _can_request_inspection(self, entry_request: ItemEntryRequest, user: User) -> bool:
        """Check if user can request inspection"""
        user_role = self.workflow._get_user_role(user)
        return (user_role.value == 'store_keeper' and 
                entry_request.workflow_status == WorkflowState.ASSIGNED_TO_STORE.value)
    
    def _validate_request_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate request data"""
        # Implement comprehensive validation
        if not data.get('title'):
            return {'valid': False, 'message': 'Title is required'}
        
        if not data.get('items'):
            return {'valid': False, 'message': 'At least one item is required'}
        
        return {'valid': True, 'message': 'Valid'}
    
    def _extract_request_fields(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract request fields from data"""
        return {
            'title': data.get('title'),
            'description': data.get('description', ''),
            'po_number': data.get('po_number'),
            'po_date': data.get('po_date'),
            'supplier_id': data.get('supplier_id'),
            'is_urgent': data.get('is_urgent', False),
        }
    
    def _extract_item_fields(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract item fields from data"""
        return {
            'item_description': data.get('item_description'),
            'specifications': data.get('specifications', ''),
            'quantity': data.get('quantity'),
            'unit_price': data.get('unit_price'),
        }
    
    def _invalidate_request_cache(self, user: User = None):
        """Invalidate request-related cache"""
        if user:
            cache.delete_pattern(f"user_requests_{user.id}_*")
        else:
            cache.delete_pattern("user_requests_*")
    
    def _get_user_stores(self, user: User) -> List[Store]:
        """Get stores accessible to user"""
        # Implement based on user-store relationship
        return Store.objects.all()  # Placeholder
    
    def _apply_filters(self, queryset, filters: Dict[str, Any]):
        """Apply filters to queryset"""
        # Implement filtering logic
        return queryset
