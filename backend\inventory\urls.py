from django.urls import path, include
from django.http import JsonResponse
from rest_framework.routers import DefaultRouter
from rest_framework.decorators import api_view
from .views.swagger import openapi_schema_view
from .views import (
    # Organization
    OrganizationTypeViewSet, OrganizationViewSet, OfficeViewSet,
    # Classification
    MainClassificationViewSet, SubClassificationViewSet, EntryModeViewSet,
    # Specifications
    ItemTypeViewSet, ItemCategoryViewSet, ItemBrandViewSet, ItemManufacturerViewSet,
    ItemQualityViewSet, ItemShapeViewSet, ItemSizeViewSet, UnitOfMeasureViewSet,
    # Status
    ItemStatusViewSet, PropertyStatusViewSet, ApprovalStatusViewSet,
    # Storage
    StoreTypeViewSet, StoreViewSet, ShelfViewSet,
    # Suppliers
    SupplierViewSet,
    # Gate Pass
    GatePassViewSet,
    # Items
    ItemMasterViewSet, BatchViewSet, ItemViewSet,
    # Receiving
    Model19ReceiptViewSet, Model19ItemViewSet
)
from .views.reports import DiscrepancyTypeViewSet, DamageReportViewSet, ReportItemViewSet
from .views.requisitions import RequisitionStatusViewSet, ItemRequisitionViewSet, RequisitionItemViewSet, RequisitionAuditLogViewSet, ItemSourceAllocationViewSet
from .views.inspection import (
    InspectionCommitteeViewSet, InspectionRequestViewSet, InspectionResultViewSet,
    InspectionItemViewSet, InspectionEvidenceViewSet
)
from .views.users import UserViewSet, GroupViewSet
from .views.entry_request import ItemEntryRequestViewSet, ItemEntryRequestAttachmentViewSet, ItemEntryRequestItemViewSet
# from .views.tags import ItemTagViewSet  # Temporarily commented out

router = DefaultRouter()

# Organization routes
router.register(r'organization-types', OrganizationTypeViewSet)
router.register(r'organizations', OrganizationViewSet)
router.register(r'offices', OfficeViewSet)

# Classification routes
router.register(r'main-classifications', MainClassificationViewSet)
router.register(r'sub-classifications', SubClassificationViewSet)
router.register(r'entry-modes', EntryModeViewSet)

# Specifications routes
router.register(r'item-types', ItemTypeViewSet)
router.register(r'item-categories', ItemCategoryViewSet)
router.register(r'item-brands', ItemBrandViewSet)
router.register(r'item-manufacturers', ItemManufacturerViewSet)
router.register(r'item-qualities', ItemQualityViewSet)
router.register(r'item-shapes', ItemShapeViewSet)
router.register(r'item-sizes', ItemSizeViewSet)
router.register(r'units-of-measure', UnitOfMeasureViewSet)

# Status routes
router.register(r'item-statuses', ItemStatusViewSet)
router.register(r'property-statuses', PropertyStatusViewSet)
router.register(r'approval-statuses', ApprovalStatusViewSet)

# Storage routes
router.register(r'store-types', StoreTypeViewSet)
router.register(r'stores', StoreViewSet)
router.register(r'shelves', ShelfViewSet)

# Supplier routes
router.register(r'suppliers', SupplierViewSet)

# Gate Pass routes
router.register(r'gate-passes', GatePassViewSet)

# Item routes
router.register(r'item-masters', ItemMasterViewSet)
router.register(r'batches', BatchViewSet)
router.register(r'items', ItemViewSet)

# Report routes
router.register(r'discrepancy-types', DiscrepancyTypeViewSet)
router.register(r'damage-reports', DamageReportViewSet)
router.register(r'report-items', ReportItemViewSet)

# Requisition routes
router.register(r'requisition-statuses', RequisitionStatusViewSet)
router.register(r'requisitions', ItemRequisitionViewSet, basename='requisition')
router.register(r'requisition-items', RequisitionItemViewSet)
router.register(r'requisition-audit-logs', RequisitionAuditLogViewSet)
router.register(r'item-source-allocations', ItemSourceAllocationViewSet)

# Inspection routes
router.register(r'inspection-committees', InspectionCommitteeViewSet)
router.register(r'inspection-requests', InspectionRequestViewSet)
router.register(r'inspection-results', InspectionResultViewSet)
router.register(r'inspection-items', InspectionItemViewSet)
router.register(r'inspection-evidence', InspectionEvidenceViewSet)

# User routes
router.register(r'users', UserViewSet)
router.register(r'groups', GroupViewSet)

# Entry Request routes
router.register(r'entry-requests', ItemEntryRequestViewSet)
router.register(r'entry-request-attachments', ItemEntryRequestAttachmentViewSet)
router.register(r'entry-request-items', ItemEntryRequestItemViewSet)

# Receiving routes
router.register(r'model19-receipts', Model19ReceiptViewSet)
router.register(r'model19-items', Model19ItemViewSet)

# Tags routes - Temporarily commented out
# router.register(r'item-tags', ItemTagViewSet)

app_name = 'inventory'

@api_view(['GET'])
def test_endpoint(request):
    return JsonResponse({'status': 'ok', 'message': 'API is working'})

urlpatterns = [
    path('', include(router.urls)),
    path('test/', test_endpoint, name='test-endpoint'),
    # Custom Swagger view for format=openapi
    path('swagger/', openapi_schema_view, name='openapi-schema'),
]
