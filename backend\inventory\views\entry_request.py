from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from django_filters.rest_framework import DjangoFilterBackend
from ..models.entry_request import ItemEntryRequest, ItemEntryRequestAttachment, ItemEntryRequestItem
from ..serializers.entry_request import (
    ItemEntryRequestSerializer,
    ItemEntryRequestListSerializer,
    ItemEntryRequestDetailSerializer,
    ItemEntryRequestAttachmentSerializer,
    ItemEntryRequestItemSerializer
)
from ..permissions import BaseModelPermission, IsAdmin, IsPAO, IsProcurement

class ItemEntryRequestViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing item entry requests.

    This viewset provides CRUD operations for ItemEntryRequest objects,
    as well as additional actions for approving, rejecting, and marking
    requests as received.
    """
    queryset = ItemEntryRequest.objects.all()
    permission_classes = [permissions.IsAuthenticated & (BaseModelPermission | IsAdmin | IsProcurement | IsPAO)]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'workflow_status', 'supplier', 'requested_by', 'approved_by',
                       'main_classification', 'target_store', 'assigned_store', 'is_urgent', 'is_complete',
                       'inspection_requested', 'inspection_failed', 'model19_generated', 'dsr_generated']
    search_fields = ['request_code', 'title', 'description', 'po_number', 'model19_reference', 'dsr_reference']
    ordering_fields = ['created_at', 'request_code', 'title', 'expected_delivery_date',
                      'actual_delivery_date', 'assigned_date', 'is_urgent', 'is_complete']

    def get_serializer_class(self):
        if self.action == 'list':
            return ItemEntryRequestListSerializer
        elif self.action == 'retrieve':
            return ItemEntryRequestDetailSerializer
        return ItemEntryRequestSerializer

    def perform_create(self, serializer):
        serializer.save(requested_by=self.request.user)

    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAuthenticated & (IsAdmin | IsPAO)])
    def approve(self, request, pk=None):
        """
        Approve an item entry request.

        This action changes the status of the request to 'Approved',
        sets the approved_by field to the current user, and records
        the approval date and any comments provided.
        """
        entry_request = self.get_object()

        try:
            # Use the model's approve method
            entry_request.approve(request.user, request.data.get('comments', ''))
            serializer = self.get_serializer(entry_request)
            return Response(serializer.data)
        except Exception as e:
            return Response(
                {'detail': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAuthenticated & (IsAdmin | IsPAO)])
    def reject(self, request, pk=None):
        """
        Reject an item entry request.

        This action changes the status of the request to 'Rejected',
        sets the approved_by field to the current user, and records
        the approval date and the rejection reason.
        """
        entry_request = self.get_object()

        try:
            # Use the model's reject method
            entry_request.reject(request.user, request.data.get('comments', ''))
            serializer = self.get_serializer(entry_request)
            return Response(serializer.data)
        except Exception as e:
            return Response(
                {'detail': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['post'])
    def mark_as_received(self, request, pk=None):
        """
        Mark an entry request as received.

        This action sets the is_complete flag to True and records
        the actual delivery date.
        """
        entry_request = self.get_object()

        try:
            # Use the model's mark_as_received method
            delivery_date = request.data.get('delivery_date')
            if delivery_date:
                from datetime import datetime
                delivery_date = datetime.strptime(delivery_date, '%Y-%m-%d').date()

            entry_request.mark_as_received(delivery_date)
            serializer = self.get_serializer(entry_request)
            return Response(serializer.data)
        except Exception as e:
            return Response(
                {'detail': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['post'])
    def toggle_urgent(self, request, pk=None):
        """
        Toggle the urgent flag for an entry request.
        """
        entry_request = self.get_object()
        entry_request.is_urgent = not entry_request.is_urgent
        entry_request.save()

        serializer = self.get_serializer(entry_request)
        return Response(serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAuthenticated & (IsAdmin | IsPAO)])
    def assign_to_store(self, request, pk=None):
        """
        Assign the entry request to a store.

        This action assigns the entry request to a specific store for processing.
        The store_id must be provided in the request data.
        """
        entry_request = self.get_object()
        store_id = request.data.get('store_id')

        if not store_id:
            return Response(
                {'detail': 'Store ID is required.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            from ..models.storage import Store
            store = Store.objects.get(id=store_id)

            entry_request.assign_to_store(store, request.user)
            serializer = self.get_serializer(entry_request)
            return Response(serializer.data)
        except Store.DoesNotExist:
            return Response(
                {'detail': 'Store not found.'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {'detail': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['post'])
    def request_inspection(self, request, pk=None):
        """
        Request inspection for the items in this entry request.

        This action creates an inspection request for the items in this entry request.
        """
        entry_request = self.get_object()

        try:
            inspection_request = entry_request.request_inspection(request.user)

            # Return the inspection request details
            from ..serializers.inspection import InspectionRequestSerializer
            serializer = InspectionRequestSerializer(inspection_request)
            return Response(serializer.data)
        except Exception as e:
            return Response(
                {'detail': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['post'])
    def generate_model19(self, request, pk=None):
        """
        Generate Model 19 for the items in this entry request.

        This action generates a Model 19 receipt for the items in this entry request.
        """
        entry_request = self.get_object()
        reference = request.data.get('reference')

        try:
            entry_request.generate_model19(request.user, reference)
            serializer = self.get_serializer(entry_request)
            return Response(serializer.data)
        except Exception as e:
            return Response(
                {'detail': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

class ItemEntryRequestAttachmentViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing item entry request attachments.

    This viewset provides CRUD operations for ItemEntryRequestAttachment objects,
    with filtering by entry request, file type, and uploaded by.
    """
    queryset = ItemEntryRequestAttachment.objects.all()
    serializer_class = ItemEntryRequestAttachmentSerializer
    permission_classes = [permissions.IsAuthenticated & (BaseModelPermission | IsAdmin | IsProcurement | IsPAO)]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['entry_request', 'file_type', 'attachment_type', 'is_confidential', 'uploaded_by']
    search_fields = ['file_name', 'description', 'entry_request__request_code']
    ordering_fields = ['created_at', 'file_name', 'file_size', 'attachment_type']

    def perform_create(self, serializer):
        # Handle file upload
        uploaded_file = self.request.FILES.get('file')
        if uploaded_file:
            from django.core.files.storage import default_storage

            # Get the entry request ID
            entry_request_id = self.request.data.get('entry_request')

            # Create the file path
            file_path = f"entry_requests/{entry_request_id}/{uploaded_file.name}"

            # Save the file
            saved_path = default_storage.save(file_path, uploaded_file)

            # Save the attachment with the file path
            serializer.save(
                uploaded_by=self.request.user,
                file_path=saved_path,
                file_name=uploaded_file.name,
                file_type=uploaded_file.content_type,
                file_size=uploaded_file.size
            )
        else:
            serializer.save(uploaded_by=self.request.user)

    def get_queryset(self):
        qs = super().get_queryset()
        # Only show confidential attachments to users with permission
        if not self.request.user.has_perm('inventory.can_view_confidential_attachments'):
            qs = qs.filter(is_confidential=False)
        return qs

    @action(detail=True, methods=['post'])
    def toggle_confidential(self, request, pk=None):
        """
        Toggle the confidential flag for an attachment.
        """
        attachment = self.get_object()
        attachment.is_confidential = not attachment.is_confidential
        attachment.save()

        serializer = self.get_serializer(attachment)
        return Response(serializer.data)


class ItemEntryRequestItemViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing item entry request items.

    This viewset provides CRUD operations for ItemEntryRequestItem objects,
    with filtering by entry request, received status, and classification.
    """
    queryset = ItemEntryRequestItem.objects.all()
    serializer_class = ItemEntryRequestItemSerializer
    permission_classes = [permissions.IsAuthenticated & (BaseModelPermission | IsAdmin | IsProcurement | IsPAO)]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['entry_request', 'is_received', 'main_classification']
    search_fields = ['item_description', 'specifications', 'entry_request__request_code']
    ordering_fields = ['entry_request', 'item_description', 'quantity', 'unit_price', 'received_date']

    @action(detail=True, methods=['post'])
    def mark_as_received(self, request, pk=None):
        """
        Mark an item as received.

        This action sets the is_received flag to True and records
        the received quantity and date.
        """
        item = self.get_object()

        try:
            quantity = request.data.get('quantity')
            if quantity:
                quantity = int(quantity)

            date = request.data.get('date')
            if date:
                from datetime import datetime
                date = datetime.strptime(date, '%Y-%m-%d').date()

            item.mark_as_received(quantity, date)
            serializer = self.get_serializer(item)
            return Response(serializer.data)
        except Exception as e:
            return Response(
                {'detail': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAuthenticated])
    def assign_inspector(self, request, pk=None):
        """
        Assign an inspector to an item.

        This action assigns an inspector to a specific item for inspection.
        """
        item = self.get_object()

        try:
            inspector_id = request.data.get('inspector_id')
            if not inspector_id:
                return Response(
                    {'detail': 'Inspector ID is required.'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            from django.contrib.auth.models import User
            inspector = User.objects.get(id=inspector_id)

            item.assign_inspector(inspector, request.user)
            serializer = self.get_serializer(item)
            return Response(serializer.data)
        except User.DoesNotExist:
            return Response(
                {'detail': 'Inspector not found.'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {'detail': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAuthenticated])
    def update_inspection_status(self, request, pk=None):
        """
        Update the inspection status of an item.

        This action allows inspectors to update the inspection status and notes.
        """
        item = self.get_object()

        try:
            status_value = request.data.get('status')
            notes = request.data.get('notes', '')

            if not status_value:
                return Response(
                    {'detail': 'Status is required.'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            item.update_inspection_status(status_value, notes, request.user)
            serializer = self.get_serializer(item)
            return Response(serializer.data)
        except Exception as e:
            return Response(
                {'detail': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )