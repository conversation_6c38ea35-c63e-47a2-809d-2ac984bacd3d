from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters import rest_framework as filters
from ..models.organization import OrganizationType, Organization, Office
from ..serializers.organization import (
    OrganizationTypeSerializer,
    OrganizationSerializer,
    OrganizationListSerializer,
    OfficeSerializer,
    OfficeListSerializer
)
from ..permissions import BaseModelPermission, IsAdmin, IsInventoryManager

class OrganizationTypeViewSet(viewsets.ModelViewSet):
    queryset = OrganizationType.objects.all()
    serializer_class = OrganizationTypeSerializer
    permission_classes = [permissions.IsAuthenticated & (BaseModelPermission | IsAdmin)]
    filterset_fields = ['name', 'is_active']
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'created_at']

class OrganizationViewSet(viewsets.ModelViewSet):
    queryset = Organization.objects.all()
    permission_classes = [permissions.IsAuthenticated & (BaseModelPermission | IsAdmin | IsInventoryManager)]
    filterset_fields = ['org_type', 'is_active', 'is_main']
    search_fields = ['name', 'shortcode', 'email']
    ordering_fields = ['name', 'created_at']

    def get_serializer_class(self):
        if self.action == 'list':
            return OrganizationListSerializer
        return OrganizationSerializer

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['request'] = self.request
        return context

    @action(detail=False, methods=['get'])
    def main(self, request):
        """Get the main organization"""
        try:
            main_org = Organization.objects.filter(is_main=True, is_active=True).first()
            if main_org:
                serializer = self.get_serializer(main_org)
                return Response(serializer.data)
            else:
                # If no main organization is set, return the first active organization
                first_org = Organization.objects.filter(is_active=True).first()
                if first_org:
                    serializer = self.get_serializer(first_org)
                    return Response(serializer.data)
                else:
                    return Response(
                        {'error': 'No organizations found'},
                        status=status.HTTP_404_NOT_FOUND
                    )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['get'])
    def offices(self, request, pk=None):
        organization = self.get_object()
        offices = Office.objects.filter(organization=organization)
        serializer = OfficeListSerializer(offices, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def set_as_main(self, request, pk=None):
        """Set this organization as the main organization"""
        organization = self.get_object()
        organization.is_main = True
        organization.save()
        serializer = self.get_serializer(organization)
        return Response(serializer.data)

class OfficeViewSet(viewsets.ModelViewSet):
    queryset = Office.objects.all()
    permission_classes = [permissions.IsAuthenticated & (BaseModelPermission | IsAdmin | IsInventoryManager)]
    filterset_fields = ['organization', 'is_active', 'is_main']
    search_fields = ['name', 'code']
    ordering_fields = ['name', 'created_at']

    def get_serializer_class(self):
        if self.action == 'list':
            return OfficeListSerializer
        return OfficeSerializer

    @action(detail=False, methods=['get'])
    def main(self, request):
        """Get the main office"""
        try:
            office = Office.objects.get(is_main=True)
            serializer = self.get_serializer(office)
            return Response(serializer.data)
        except Office.DoesNotExist:
            return Response({"detail": "No main office set."}, status=404)

    @action(detail=True, methods=['post'])
    def set_as_main(self, request, pk=None):
        """Set this office as the main office"""
        office = self.get_object()
        office.is_main = True
        office.save()
        serializer = self.get_serializer(office)
        return Response(serializer.data)

