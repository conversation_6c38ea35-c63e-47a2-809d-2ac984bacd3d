from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from ..models.tags import ItemTag
from ..serializers.tags import ItemTagSerializer, ItemTagListSerializer, ItemTagCreateSerializer
from ..permissions import BaseModelPermission


class ItemTagViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing item tags.
    
    Provides CRUD operations for item tags with filtering and search capabilities.
    """
    queryset = ItemTag.objects.all()
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['tag_type', 'color', 'is_active']
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'priority', 'created_at']
    ordering = ['-priority', 'name']
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return ItemTagListSerializer
        elif self.action == 'create':
            return ItemTagCreateSerializer
        return ItemTagSerializer
    
    def get_queryset(self):
        """Filter queryset based on user permissions and active status"""
        queryset = super().get_queryset()
        
        # For non-admin users, only show active tags
        if not self.request.user.is_superuser:
            queryset = queryset.filter(is_active=True)
        
        return queryset
    
    @action(detail=False, methods=['get'])
    def active(self, request):
        """
        Get only active tags.
        
        Returns a list of active tags ordered by priority.
        """
        active_tags = self.get_queryset().filter(is_active=True)
        serializer = ItemTagListSerializer(active_tags, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def by_type(self, request):
        """
        Get tags grouped by type.
        
        Returns tags organized by their tag_type.
        """
        tag_types = ItemTag.TAG_TYPES
        result = {}
        
        for tag_type_code, tag_type_name in tag_types:
            tags = self.get_queryset().filter(
                tag_type=tag_type_code,
                is_active=True
            ).order_by('-priority', 'name')
            
            result[tag_type_code] = {
                'name': tag_type_name,
                'tags': ItemTagListSerializer(tags, many=True).data
            }
        
        return Response(result)
    
    @action(detail=False, methods=['post'], permission_classes=[permissions.IsAdminUser])
    def create_predefined(self, request):
        """
        Create predefined tags.
        
        Creates the standard set of predefined tags if they don't exist.
        Only available to admin users.
        """
        try:
            ItemTag.create_predefined_tags()
            return Response(
                {'message': 'Predefined tags created successfully'},
                status=status.HTTP_201_CREATED
            )
        except Exception as e:
            return Response(
                {'error': f'Failed to create predefined tags: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=True, methods=['post'])
    def toggle_active(self, request, pk=None):
        """
        Toggle the active status of a tag.
        
        Switches between active and inactive status.
        """
        tag = self.get_object()
        tag.is_active = not tag.is_active
        tag.save()
        
        serializer = self.get_serializer(tag)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def popular(self, request):
        """
        Get most popular tags.
        
        Returns tags ordered by the number of items using them.
        """
        from django.db.models import Count
        
        popular_tags = self.get_queryset().filter(
            is_active=True
        ).annotate(
            items_count=Count('items')
        ).order_by('-items_count', '-priority', 'name')[:10]
        
        serializer = ItemTagListSerializer(popular_tags, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def colors(self, request):
        """
        Get available tag colors.
        
        Returns the list of available colors for tags.
        """
        colors = [
            {'value': color_code, 'label': color_name}
            for color_code, color_name in ItemTag.TAG_COLORS
        ]
        return Response(colors)
    
    @action(detail=False, methods=['get'])
    def types(self, request):
        """
        Get available tag types.
        
        Returns the list of available tag types.
        """
        types = [
            {'value': type_code, 'label': type_name}
            for type_code, type_name in ItemTag.TAG_TYPES
        ]
        return Response(types)
