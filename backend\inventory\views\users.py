from rest_framework import viewsets, permissions, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django.contrib.auth.models import User, Group
from django_filters.rest_framework import DjangoFilterBackend
from ..permissions import BaseModelPermission, IsAdmin
from ..serializers.users import UserSerializer, GroupSerializer

class UserViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows users to be viewed.
    This is a read-only viewset to prevent unauthorized user management.
    User management should be done through the Django admin interface.
    """
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated & (BaseModelPermission | IsAdmin)]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['username', 'email', 'is_active', 'groups__name']
    search_fields = ['username', 'first_name', 'last_name', 'email']
    ordering_fields = ['username', 'first_name', 'last_name', 'date_joined']

    @action(detail=False, methods=['get'], permission_classes=[permissions.IsAuthenticated])
    def inspectors(self, request):
        """
        Get all users with Inspector role.

        Returns a list of users who are members of the Inspector group.
        """
        inspectors = User.objects.filter(
            groups__name='Inspector',
            is_active=True
        ).distinct()

        serializer = self.get_serializer(inspectors, many=True)
        return Response(serializer.data)

class GroupViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows groups to be viewed.
    This is a read-only viewset to prevent unauthorized group management.
    Group management should be done through the Django admin interface.
    """
    queryset = Group.objects.all()
    serializer_class = GroupSerializer
    permission_classes = [permissions.IsAuthenticated & (BaseModelPermission | IsAdmin)]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['name']
    search_fields = ['name']
    ordering_fields = ['name']
