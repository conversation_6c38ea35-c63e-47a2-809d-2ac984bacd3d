#!/usr/bin/env python
"""
Seed script for inspection users and committees
Creates sample inspection users and committees for testing
"""

import os
import sys
import django
from django.db import transaction

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth.models import User, Group
from inventory.models import InspectionCommittee, MainClassification


def create_inspector_group():
    """Create Inspector group if it doesn't exist"""
    group, created = Group.objects.get_or_create(name='Inspector')
    if created:
        print("✓ Created Inspector group")
    else:
        print("✓ Inspector group already exists")
    return group


def create_inspection_users():
    """Create sample inspection users"""
    inspector_group = create_inspector_group()
    
    # Sample inspector users
    inspectors_data = [
        {
            'username': 'inspector_medical',
            'first_name': 'Dr. <PERSON>',
            'last_name': '<PERSON>',
            'email': '<EMAIL>',
            'specialty': 'Medical Equipment'
        },
        {
            'username': 'inspector_technical',
            'first_name': 'Eng. <PERSON>',
            'last_name': '<PERSON>',
            'email': '<EMAIL>',
            'specialty': 'Technical Equipment'
        },
        {
            'username': 'inspector_office',
            'first_name': 'Ms. <PERSON>',
            'last_name': '<PERSON>',
            'email': '<EMAIL>',
            'specialty': 'Office Supplies'
        },
        {
            'username': 'inspector_safety',
            'first_name': 'Mr. James',
            'last_name': '<PERSON>',
            'email': '<EMAIL>',
            'specialty': 'Safety Equipment'
        },
        {
            'username': 'inspector_general',
            'first_name': 'Ms. Lisa',
            'last_name': 'Anderson',
            'email': '<EMAIL>',
            'specialty': 'General Inspection'
        },
        {
            'username': 'inspector_senior',
            'first_name': 'Dr. Robert',
            'last_name': 'Martinez',
            'email': '<EMAIL>',
            'specialty': 'Senior Inspector'
        }
    ]
    
    created_users = []
    for user_data in inspectors_data:
        user, created = User.objects.get_or_create(
            username=user_data['username'],
            defaults={
                'first_name': user_data['first_name'],
                'last_name': user_data['last_name'],
                'email': user_data['email'],
                'is_active': True,
                'is_staff': True
            }
        )
        
        if created:
            user.set_password('inspector123')  # Default password
            user.save()
            print(f"✓ Created inspector: {user.first_name} {user.last_name}")
        else:
            print(f"✓ Inspector already exists: {user.first_name} {user.last_name}")
        
        # Add to inspector group
        user.groups.add(inspector_group)
        created_users.append(user)
    
    return created_users


def create_main_classifications():
    """Create sample main classifications if they don't exist"""
    classifications_data = [
        {'name': 'Medical Equipment', 'code': 'MED'},
        {'name': 'Office Supplies', 'code': 'OFF'},
        {'name': 'Technical Equipment', 'code': 'TECH'},
        {'name': 'Safety Equipment', 'code': 'SAFE'},
        {'name': 'Furniture', 'code': 'FURN'},
        {'name': 'Vehicles', 'code': 'VEH'}
    ]
    
    created_classifications = []
    for class_data in classifications_data:
        classification, created = MainClassification.objects.get_or_create(
            name=class_data['name'],
            defaults={'code': class_data['code']}
        )
        
        if created:
            print(f"✓ Created classification: {classification.name}")
        else:
            print(f"✓ Classification already exists: {classification.name}")
        
        created_classifications.append(classification)
    
    return created_classifications


def create_inspection_committees(inspectors, classifications):
    """Create sample inspection committees"""
    
    # Committee data with their members and specializations
    committees_data = [
        {
            'title': 'Medical Equipment Inspection Committee',
            'description': 'Specialized committee for inspecting medical devices, pharmaceuticals, and healthcare equipment',
            'members': ['inspector_medical', 'inspector_senior', 'inspector_general'],
            'classifications': ['Medical Equipment']
        },
        {
            'title': 'Technical Equipment Committee',
            'description': 'Committee responsible for inspecting computers, machinery, and technical equipment',
            'members': ['inspector_technical', 'inspector_senior'],
            'classifications': ['Technical Equipment']
        },
        {
            'title': 'Office Supplies Inspection Committee',
            'description': 'Committee for inspecting office supplies, stationery, and general office equipment',
            'members': ['inspector_office', 'inspector_general'],
            'classifications': ['Office Supplies', 'Furniture']
        },
        {
            'title': 'Safety & Security Committee',
            'description': 'Specialized committee for safety equipment, security systems, and protective gear',
            'members': ['inspector_safety', 'inspector_technical', 'inspector_senior'],
            'classifications': ['Safety Equipment']
        },
        {
            'title': 'General Inspection Committee',
            'description': 'Multi-purpose committee for general items and miscellaneous equipment',
            'members': ['inspector_general', 'inspector_senior'],
            'classifications': ['Vehicles', 'Furniture']
        },
        {
            'title': 'High-Value Items Committee',
            'description': 'Committee for inspecting high-value and sensitive equipment requiring senior oversight',
            'members': ['inspector_senior', 'inspector_medical', 'inspector_technical'],
            'classifications': ['Medical Equipment', 'Technical Equipment']
        }
    ]
    
    # Create a mapping of usernames to user objects
    user_map = {user.username: user for user in inspectors}
    classification_map = {cls.name: cls for cls in classifications}
    
    created_committees = []
    for committee_data in committees_data:
        committee, created = InspectionCommittee.objects.get_or_create(
            title=committee_data['title'],
            defaults={
                'description': committee_data['description'],
                'is_active': True
            }
        )
        
        if created:
            print(f"✓ Created committee: {committee.title}")
            
            # Add members to the committee
            for member_username in committee_data['members']:
                if member_username in user_map:
                    committee.users.add(user_map[member_username])
            
            # Add classifications to the committee
            for classification_name in committee_data['classifications']:
                if classification_name in classification_map:
                    committee.main_classifications.add(classification_map[classification_name])
            
            committee.save()
            print(f"  → Added {len(committee_data['members'])} members")
            print(f"  → Added {len(committee_data['classifications'])} classifications")
        else:
            print(f"✓ Committee already exists: {committee.title}")
        
        created_committees.append(committee)
    
    return created_committees


@transaction.atomic
def main():
    """Main seeding function"""
    print("🌱 Starting Inspection System Seeding...")
    print("=" * 60)
    
    try:
        # Create inspector users
        print("\n1. Creating Inspector Users...")
        inspectors = create_inspection_users()
        
        # Create main classifications
        print("\n2. Creating Main Classifications...")
        classifications = create_main_classifications()
        
        # Create inspection committees
        print("\n3. Creating Inspection Committees...")
        committees = create_inspection_committees(inspectors, classifications)
        
        print("\n" + "=" * 60)
        print("🎉 SEEDING COMPLETED SUCCESSFULLY!")
        print(f"✅ Created/Updated {len(inspectors)} inspector users")
        print(f"✅ Created/Updated {len(classifications)} main classifications")
        print(f"✅ Created/Updated {len(committees)} inspection committees")
        
        print("\n📋 SUMMARY:")
        print("Inspector Users:")
        for user in inspectors:
            print(f"  • {user.first_name} {user.last_name} ({user.username})")
        
        print("\nInspection Committees:")
        for committee in committees:
            member_count = committee.users.count()
            classification_count = committee.main_classifications.count()
            print(f"  • {committee.title} ({member_count} members, {classification_count} classifications)")
        
        print("\n🔐 Default Password for all inspectors: inspector123")
        print("🎯 All committees are active and ready for assignment")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ Error during seeding: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == '__main__':
    success = main()
    if success:
        print("\n✅ You can now test the inspection committee assignment system!")
        print("🚀 Go to the frontend and assign committees to items.")
    else:
        print("\n❌ Seeding failed. Please check the errors above.")
        sys.exit(1)
