#!/usr/bin/env python
"""
Simple test script to check Django setup
"""

import os
import sys
import django
from django.conf import settings

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')

try:
    django.setup()
    print("✓ Django setup successful")
    
    # Test basic imports
    from inventory.models import ItemEntryRequest
    print("✓ ItemEntryRequest model imported successfully")
    
    from inventory.serializers.entry_request import ItemEntryRequestSerializer
    print("✓ ItemEntryRequestSerializer imported successfully")
    
    from inventory.views.entry_request import ItemEntryRequestViewSet
    print("✓ ItemEntryRequestViewSet imported successfully")
    
    # Test database connection
    from django.db import connection
    with connection.cursor() as cursor:
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        if result:
            print("✓ Database connection successful")
    
    print("\n" + "="*50)
    print("✓ All basic tests passed!")
    print("The server should start without 500 errors now.")
    print("="*50)
    
except Exception as e:
    print(f"✗ Error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
