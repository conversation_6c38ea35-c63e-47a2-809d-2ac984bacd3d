#!/usr/bin/env python
"""
Verification script for inspection system data
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth.models import User, Group
from inventory.models import InspectionCommittee, MainClassification


def verify_inspection_data():
    """Verify the seeded inspection data"""
    print("🔍 Verifying Inspection System Data...")
    print("=" * 60)
    
    # Check Inspector Group
    try:
        inspector_group = Group.objects.get(name='Inspector')
        inspector_count = inspector_group.user_set.count()
        print(f"✅ Inspector Group: {inspector_count} members")
    except Group.DoesNotExist:
        print("❌ Inspector Group not found")
        return False
    
    # Check Inspector Users
    print("\n👥 Inspector Users:")
    inspectors = User.objects.filter(groups__name='Inspector')
    for inspector in inspectors:
        committees_count = inspector.inspection_committees.count()
        print(f"  • {inspector.first_name} {inspector.last_name} ({inspector.username}) - {committees_count} committees")
    
    # Check Main Classifications
    print(f"\n📋 Main Classifications ({MainClassification.objects.count()}):")
    for classification in MainClassification.objects.all():
        committees_count = classification.inspection_committees.count()
        print(f"  • {classification.name} ({classification.code}) - {committees_count} committees")
    
    # Check Inspection Committees
    print(f"\n🏛️ Inspection Committees ({InspectionCommittee.objects.count()}):")
    for committee in InspectionCommittee.objects.all():
        members_count = committee.users.count()
        classifications_count = committee.main_classifications.count()
        status = "Active" if committee.is_active else "Inactive"
        print(f"  • {committee.title}")
        print(f"    └─ {members_count} members, {classifications_count} classifications, {status}")
        
        # List members
        if members_count > 0:
            members = [f"{u.first_name} {u.last_name}" for u in committee.users.all()]
            print(f"    └─ Members: {', '.join(members)}")
        
        # List classifications
        if classifications_count > 0:
            classifications = [c.name for c in committee.main_classifications.all()]
            print(f"    └─ Classifications: {', '.join(classifications)}")
    
    # Test API Data Format
    print(f"\n🔌 API Data Format Test:")
    committees = InspectionCommittee.objects.filter(is_active=True)
    print(f"Active committees for API: {committees.count()}")
    
    for committee in committees[:3]:  # Show first 3 as example
        api_data = {
            'id': committee.id,
            'title': committee.title,
            'user_count': committee.user_count,
            'is_active': committee.is_active
        }
        print(f"  • API format: {api_data}")
    
    print("\n" + "=" * 60)
    print("✅ VERIFICATION COMPLETED!")
    print(f"📊 Summary:")
    print(f"   • {inspectors.count()} Inspector Users")
    print(f"   • {MainClassification.objects.count()} Main Classifications")
    print(f"   • {InspectionCommittee.objects.count()} Inspection Committees")
    print(f"   • {InspectionCommittee.objects.filter(is_active=True).count()} Active Committees")
    
    # Test committee assignment readiness
    print(f"\n🎯 System Readiness:")
    active_committees = InspectionCommittee.objects.filter(is_active=True)
    if active_committees.exists():
        print("✅ Ready for committee assignment!")
        print("✅ Frontend can load committees from /inspection-committees/")
        print("✅ Items can be assigned to committees")
    else:
        print("❌ No active committees found")
        return False
    
    return True


if __name__ == '__main__':
    try:
        success = verify_inspection_data()
        if success:
            print("\n🚀 You can now test the inspection committee assignment in the frontend!")
        else:
            print("\n❌ Issues found. Please run the seeding script again.")
            sys.exit(1)
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
