{"ast": null, "code": "import api from '../utils/axios';\nexport const authService = {\n  login: async credentials => {\n    try {\n      // Try multiple endpoints\n      const endpoints = ['/api/v1/auth/token/', '/api/auth/token/', '/auth/token/', '/api/v1/auth/login/', '/api/auth/login/', '/auth/login/', '/login/', '/api/token/'];\n      for (const endpoint of endpoints) {\n        try {\n          console.log(`Trying to login at ${endpoint}`);\n          const response = await api.post(endpoint, credentials);\n          console.log('Login response data:', response.data);\n\n          // Handle different token formats\n          if (response.data.token) {\n            localStorage.setItem('token', response.data.token);\n            localStorage.setItem('user', JSON.stringify(response.data.user || {}));\n          } else if (response.data.access) {\n            localStorage.setItem('token', response.data.access);\n            localStorage.setItem('user', JSON.stringify(response.data.user || {}));\n          } else if (response.data.access_token) {\n            localStorage.setItem('token', response.data.access_token);\n            localStorage.setItem('user', JSON.stringify(response.data.user || {}));\n          }\n\n          // If we don't have user data but have a token, create a minimal user object\n          if (!response.data.user && (response.data.token || response.data.access || response.data.access_token)) {\n            localStorage.setItem('user', JSON.stringify({\n              username: credentials.username,\n              id: 'unknown'\n            }));\n          }\n          return response.data;\n        } catch (error) {\n          console.warn(`Error with ${endpoint} endpoint:`, error.message);\n          // Continue to the next endpoint\n        }\n      }\n\n      // If we get here, all endpoints failed\n      console.error('All login endpoints failed');\n      throw new Error('Login failed. Please check your credentials or contact the administrator.');\n    } catch (error) {\n      var _error$response;\n      console.error('Login error:', error);\n\n      // Handle different error formats\n      if ((_error$response = error.response) !== null && _error$response !== void 0 && _error$response.data) {\n        if (typeof error.response.data === 'string') {\n          throw error.response.data;\n        } else if (error.response.data.detail) {\n          throw error.response.data.detail;\n        } else if (error.response.data.error) {\n          throw error.response.data.error;\n        } else if (error.response.data.message) {\n          throw error.response.data.message;\n        } else if (typeof error.response.data === 'object') {\n          // If it's an object with multiple fields, create a readable message\n          const errorMessages = Object.entries(error.response.data).map(([key, value]) => `${key}: ${Array.isArray(value) ? value.join(', ') : value}`).join('; ');\n          throw errorMessages;\n        }\n      }\n      throw error.message || 'Login failed. Please try again.';\n    }\n  },\n  register: async userData => {\n    try {\n      // Try multiple endpoints\n      const endpoints = ['/auth/register/', '/api/auth/register/', '/api/v1/auth/register/', '/register/', '/api/users/'];\n\n      // Set the correct content type for FormData\n      const config = {\n        headers: {\n          'Content-Type': userData instanceof FormData ? 'multipart/form-data' : 'application/json'\n        }\n      };\n      for (const endpoint of endpoints) {\n        try {\n          console.log(`Trying to register at ${endpoint}`);\n          const response = await api.post(endpoint, userData, config);\n          return response.data;\n        } catch (error) {\n          console.warn(`Error with ${endpoint} endpoint:`, error.message);\n          // Continue to the next endpoint\n        }\n      }\n\n      // If we get here, all endpoints failed\n      throw new Error('All registration endpoints failed');\n    } catch (error) {\n      var _error$response2;\n      throw ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data) || error.message;\n    }\n  },\n  logout: () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n  },\n  getCurrentUser: async () => {\n    try {\n      const response = await api.get('/auth/user/');\n      return response.data;\n    } catch (error) {\n      var _error$response3;\n      throw ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data) || error.message;\n    }\n  },\n  updateProfile: async userData => {\n    try {\n      // Try multiple endpoints\n      const endpoints = ['/auth/user/', '/api/auth/user/', '/api/v1/auth/user/', '/api/users/me/', '/api/v1/users/me/'];\n\n      // Set the correct content type for FormData\n      const config = {\n        headers: {\n          'Content-Type': userData instanceof FormData ? 'multipart/form-data' : 'application/json'\n        }\n      };\n      for (const endpoint of endpoints) {\n        try {\n          console.log(`Trying to update user profile at ${endpoint}`);\n          const response = await api.patch(endpoint, userData, config);\n          return response.data;\n        } catch (error) {\n          console.warn(`Error with ${endpoint} endpoint:`, error.message);\n          // Continue to the next endpoint\n        }\n      }\n\n      // If we get here, all endpoints failed\n      throw new Error('All profile update endpoints failed');\n    } catch (error) {\n      var _error$response4;\n      throw ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : _error$response4.data) || error.message;\n    }\n  }\n};", "map": {"version": 3, "names": ["api", "authService", "login", "credentials", "endpoints", "endpoint", "console", "log", "response", "post", "data", "token", "localStorage", "setItem", "JSON", "stringify", "user", "access", "access_token", "username", "id", "error", "warn", "message", "Error", "_error$response", "detail", "errorMessages", "Object", "entries", "map", "key", "value", "Array", "isArray", "join", "register", "userData", "config", "headers", "FormData", "_error$response2", "logout", "removeItem", "getCurrentUser", "get", "_error$response3", "updateProfile", "patch", "_error$response4"], "sources": ["C:/Users/<USER>/Desktop/project ai/Store Management System/asset management/frontend/src/services/auth.js"], "sourcesContent": ["import api from '../utils/axios';\n\nexport const authService = {\n  login: async (credentials) => {\n    try {\n      // Try multiple endpoints\n      const endpoints = [\n        '/api/v1/auth/token/',\n        '/api/auth/token/',\n        '/auth/token/',\n        '/api/v1/auth/login/',\n        '/api/auth/login/',\n        '/auth/login/',\n        '/login/',\n        '/api/token/'\n      ];\n\n      for (const endpoint of endpoints) {\n        try {\n          console.log(`Trying to login at ${endpoint}`);\n          const response = await api.post(endpoint, credentials);\n\n          console.log('Login response data:', response.data);\n\n          // Handle different token formats\n          if (response.data.token) {\n            localStorage.setItem('token', response.data.token);\n            localStorage.setItem('user', JSON.stringify(response.data.user || {}));\n          } else if (response.data.access) {\n            localStorage.setItem('token', response.data.access);\n            localStorage.setItem('user', JSON.stringify(response.data.user || {}));\n          } else if (response.data.access_token) {\n            localStorage.setItem('token', response.data.access_token);\n            localStorage.setItem('user', JSON.stringify(response.data.user || {}));\n          }\n\n          // If we don't have user data but have a token, create a minimal user object\n          if (!response.data.user && (response.data.token || response.data.access || response.data.access_token)) {\n            localStorage.setItem('user', JSON.stringify({\n              username: credentials.username,\n              id: 'unknown'\n            }));\n          }\n\n          return response.data;\n        } catch (error) {\n          console.warn(`Error with ${endpoint} endpoint:`, error.message);\n          // Continue to the next endpoint\n        }\n      }\n\n      // If we get here, all endpoints failed\n      console.error('All login endpoints failed');\n      throw new Error('Login failed. Please check your credentials or contact the administrator.');\n    } catch (error) {\n      console.error('Login error:', error);\n\n      // Handle different error formats\n      if (error.response?.data) {\n        if (typeof error.response.data === 'string') {\n          throw error.response.data;\n        } else if (error.response.data.detail) {\n          throw error.response.data.detail;\n        } else if (error.response.data.error) {\n          throw error.response.data.error;\n        } else if (error.response.data.message) {\n          throw error.response.data.message;\n        } else if (typeof error.response.data === 'object') {\n          // If it's an object with multiple fields, create a readable message\n          const errorMessages = Object.entries(error.response.data)\n            .map(([key, value]) => `${key}: ${Array.isArray(value) ? value.join(', ') : value}`)\n            .join('; ');\n          throw errorMessages;\n        }\n      }\n\n      throw error.message || 'Login failed. Please try again.';\n    }\n  },\n\n  register: async (userData) => {\n    try {\n      // Try multiple endpoints\n      const endpoints = [\n        '/auth/register/',\n        '/api/auth/register/',\n        '/api/v1/auth/register/',\n        '/register/',\n        '/api/users/'\n      ];\n\n      // Set the correct content type for FormData\n      const config = {\n        headers: {\n          'Content-Type': userData instanceof FormData ? 'multipart/form-data' : 'application/json'\n        }\n      };\n\n      for (const endpoint of endpoints) {\n        try {\n          console.log(`Trying to register at ${endpoint}`);\n          const response = await api.post(endpoint, userData, config);\n          return response.data;\n        } catch (error) {\n          console.warn(`Error with ${endpoint} endpoint:`, error.message);\n          // Continue to the next endpoint\n        }\n      }\n\n      // If we get here, all endpoints failed\n      throw new Error('All registration endpoints failed');\n    } catch (error) {\n      throw error.response?.data || error.message;\n    }\n  },\n\n  logout: () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n  },\n\n  getCurrentUser: async () => {\n    try {\n      const response = await api.get('/auth/user/');\n      return response.data;\n    } catch (error) {\n      throw error.response?.data || error.message;\n    }\n  },\n\n  updateProfile: async (userData) => {\n    try {\n      // Try multiple endpoints\n      const endpoints = [\n        '/auth/user/',\n        '/api/auth/user/',\n        '/api/v1/auth/user/',\n        '/api/users/me/',\n        '/api/v1/users/me/'\n      ];\n\n      // Set the correct content type for FormData\n      const config = {\n        headers: {\n          'Content-Type': userData instanceof FormData ? 'multipart/form-data' : 'application/json'\n        }\n      };\n\n      for (const endpoint of endpoints) {\n        try {\n          console.log(`Trying to update user profile at ${endpoint}`);\n          const response = await api.patch(endpoint, userData, config);\n          return response.data;\n        } catch (error) {\n          console.warn(`Error with ${endpoint} endpoint:`, error.message);\n          // Continue to the next endpoint\n        }\n      }\n\n      // If we get here, all endpoints failed\n      throw new Error('All profile update endpoints failed');\n    } catch (error) {\n      throw error.response?.data || error.message;\n    }\n  }\n};\n\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,gBAAgB;AAEhC,OAAO,MAAMC,WAAW,GAAG;EACzBC,KAAK,EAAE,MAAOC,WAAW,IAAK;IAC5B,IAAI;MACF;MACA,MAAMC,SAAS,GAAG,CAChB,qBAAqB,EACrB,kBAAkB,EAClB,cAAc,EACd,qBAAqB,EACrB,kBAAkB,EAClB,cAAc,EACd,SAAS,EACT,aAAa,CACd;MAED,KAAK,MAAMC,QAAQ,IAAID,SAAS,EAAE;QAChC,IAAI;UACFE,OAAO,CAACC,GAAG,CAAC,sBAAsBF,QAAQ,EAAE,CAAC;UAC7C,MAAMG,QAAQ,GAAG,MAAMR,GAAG,CAACS,IAAI,CAACJ,QAAQ,EAAEF,WAAW,CAAC;UAEtDG,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEC,QAAQ,CAACE,IAAI,CAAC;;UAElD;UACA,IAAIF,QAAQ,CAACE,IAAI,CAACC,KAAK,EAAE;YACvBC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEL,QAAQ,CAACE,IAAI,CAACC,KAAK,CAAC;YAClDC,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACP,QAAQ,CAACE,IAAI,CAACM,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;UACxE,CAAC,MAAM,IAAIR,QAAQ,CAACE,IAAI,CAACO,MAAM,EAAE;YAC/BL,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEL,QAAQ,CAACE,IAAI,CAACO,MAAM,CAAC;YACnDL,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACP,QAAQ,CAACE,IAAI,CAACM,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;UACxE,CAAC,MAAM,IAAIR,QAAQ,CAACE,IAAI,CAACQ,YAAY,EAAE;YACrCN,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEL,QAAQ,CAACE,IAAI,CAACQ,YAAY,CAAC;YACzDN,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACP,QAAQ,CAACE,IAAI,CAACM,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;UACxE;;UAEA;UACA,IAAI,CAACR,QAAQ,CAACE,IAAI,CAACM,IAAI,KAAKR,QAAQ,CAACE,IAAI,CAACC,KAAK,IAAIH,QAAQ,CAACE,IAAI,CAACO,MAAM,IAAIT,QAAQ,CAACE,IAAI,CAACQ,YAAY,CAAC,EAAE;YACtGN,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAAC;cAC1CI,QAAQ,EAAEhB,WAAW,CAACgB,QAAQ;cAC9BC,EAAE,EAAE;YACN,CAAC,CAAC,CAAC;UACL;UAEA,OAAOZ,QAAQ,CAACE,IAAI;QACtB,CAAC,CAAC,OAAOW,KAAK,EAAE;UACdf,OAAO,CAACgB,IAAI,CAAC,cAAcjB,QAAQ,YAAY,EAAEgB,KAAK,CAACE,OAAO,CAAC;UAC/D;QACF;MACF;;MAEA;MACAjB,OAAO,CAACe,KAAK,CAAC,4BAA4B,CAAC;MAC3C,MAAM,IAAIG,KAAK,CAAC,2EAA2E,CAAC;IAC9F,CAAC,CAAC,OAAOH,KAAK,EAAE;MAAA,IAAAI,eAAA;MACdnB,OAAO,CAACe,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;;MAEpC;MACA,KAAAI,eAAA,GAAIJ,KAAK,CAACb,QAAQ,cAAAiB,eAAA,eAAdA,eAAA,CAAgBf,IAAI,EAAE;QACxB,IAAI,OAAOW,KAAK,CAACb,QAAQ,CAACE,IAAI,KAAK,QAAQ,EAAE;UAC3C,MAAMW,KAAK,CAACb,QAAQ,CAACE,IAAI;QAC3B,CAAC,MAAM,IAAIW,KAAK,CAACb,QAAQ,CAACE,IAAI,CAACgB,MAAM,EAAE;UACrC,MAAML,KAAK,CAACb,QAAQ,CAACE,IAAI,CAACgB,MAAM;QAClC,CAAC,MAAM,IAAIL,KAAK,CAACb,QAAQ,CAACE,IAAI,CAACW,KAAK,EAAE;UACpC,MAAMA,KAAK,CAACb,QAAQ,CAACE,IAAI,CAACW,KAAK;QACjC,CAAC,MAAM,IAAIA,KAAK,CAACb,QAAQ,CAACE,IAAI,CAACa,OAAO,EAAE;UACtC,MAAMF,KAAK,CAACb,QAAQ,CAACE,IAAI,CAACa,OAAO;QACnC,CAAC,MAAM,IAAI,OAAOF,KAAK,CAACb,QAAQ,CAACE,IAAI,KAAK,QAAQ,EAAE;UAClD;UACA,MAAMiB,aAAa,GAAGC,MAAM,CAACC,OAAO,CAACR,KAAK,CAACb,QAAQ,CAACE,IAAI,CAAC,CACtDoB,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK,GAAGD,GAAG,KAAKE,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,GAAGA,KAAK,CAACG,IAAI,CAAC,IAAI,CAAC,GAAGH,KAAK,EAAE,CAAC,CACnFG,IAAI,CAAC,IAAI,CAAC;UACb,MAAMR,aAAa;QACrB;MACF;MAEA,MAAMN,KAAK,CAACE,OAAO,IAAI,iCAAiC;IAC1D;EACF,CAAC;EAEDa,QAAQ,EAAE,MAAOC,QAAQ,IAAK;IAC5B,IAAI;MACF;MACA,MAAMjC,SAAS,GAAG,CAChB,iBAAiB,EACjB,qBAAqB,EACrB,wBAAwB,EACxB,YAAY,EACZ,aAAa,CACd;;MAED;MACA,MAAMkC,MAAM,GAAG;QACbC,OAAO,EAAE;UACP,cAAc,EAAEF,QAAQ,YAAYG,QAAQ,GAAG,qBAAqB,GAAG;QACzE;MACF,CAAC;MAED,KAAK,MAAMnC,QAAQ,IAAID,SAAS,EAAE;QAChC,IAAI;UACFE,OAAO,CAACC,GAAG,CAAC,yBAAyBF,QAAQ,EAAE,CAAC;UAChD,MAAMG,QAAQ,GAAG,MAAMR,GAAG,CAACS,IAAI,CAACJ,QAAQ,EAAEgC,QAAQ,EAAEC,MAAM,CAAC;UAC3D,OAAO9B,QAAQ,CAACE,IAAI;QACtB,CAAC,CAAC,OAAOW,KAAK,EAAE;UACdf,OAAO,CAACgB,IAAI,CAAC,cAAcjB,QAAQ,YAAY,EAAEgB,KAAK,CAACE,OAAO,CAAC;UAC/D;QACF;MACF;;MAEA;MACA,MAAM,IAAIC,KAAK,CAAC,mCAAmC,CAAC;IACtD,CAAC,CAAC,OAAOH,KAAK,EAAE;MAAA,IAAAoB,gBAAA;MACd,MAAM,EAAAA,gBAAA,GAAApB,KAAK,CAACb,QAAQ,cAAAiC,gBAAA,uBAAdA,gBAAA,CAAgB/B,IAAI,KAAIW,KAAK,CAACE,OAAO;IAC7C;EACF,CAAC;EAEDmB,MAAM,EAAEA,CAAA,KAAM;IACZ9B,YAAY,CAAC+B,UAAU,CAAC,OAAO,CAAC;IAChC/B,YAAY,CAAC+B,UAAU,CAAC,MAAM,CAAC;EACjC,CAAC;EAEDC,cAAc,EAAE,MAAAA,CAAA,KAAY;IAC1B,IAAI;MACF,MAAMpC,QAAQ,GAAG,MAAMR,GAAG,CAAC6C,GAAG,CAAC,aAAa,CAAC;MAC7C,OAAOrC,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOW,KAAK,EAAE;MAAA,IAAAyB,gBAAA;MACd,MAAM,EAAAA,gBAAA,GAAAzB,KAAK,CAACb,QAAQ,cAAAsC,gBAAA,uBAAdA,gBAAA,CAAgBpC,IAAI,KAAIW,KAAK,CAACE,OAAO;IAC7C;EACF,CAAC;EAEDwB,aAAa,EAAE,MAAOV,QAAQ,IAAK;IACjC,IAAI;MACF;MACA,MAAMjC,SAAS,GAAG,CAChB,aAAa,EACb,iBAAiB,EACjB,oBAAoB,EACpB,gBAAgB,EAChB,mBAAmB,CACpB;;MAED;MACA,MAAMkC,MAAM,GAAG;QACbC,OAAO,EAAE;UACP,cAAc,EAAEF,QAAQ,YAAYG,QAAQ,GAAG,qBAAqB,GAAG;QACzE;MACF,CAAC;MAED,KAAK,MAAMnC,QAAQ,IAAID,SAAS,EAAE;QAChC,IAAI;UACFE,OAAO,CAACC,GAAG,CAAC,oCAAoCF,QAAQ,EAAE,CAAC;UAC3D,MAAMG,QAAQ,GAAG,MAAMR,GAAG,CAACgD,KAAK,CAAC3C,QAAQ,EAAEgC,QAAQ,EAAEC,MAAM,CAAC;UAC5D,OAAO9B,QAAQ,CAACE,IAAI;QACtB,CAAC,CAAC,OAAOW,KAAK,EAAE;UACdf,OAAO,CAACgB,IAAI,CAAC,cAAcjB,QAAQ,YAAY,EAAEgB,KAAK,CAACE,OAAO,CAAC;UAC/D;QACF;MACF;;MAEA;MACA,MAAM,IAAIC,KAAK,CAAC,qCAAqC,CAAC;IACxD,CAAC,CAAC,OAAOH,KAAK,EAAE;MAAA,IAAA4B,gBAAA;MACd,MAAM,EAAAA,gBAAA,GAAA5B,KAAK,CAACb,QAAQ,cAAAyC,gBAAA,uBAAdA,gBAAA,CAAgBvC,IAAI,KAAIW,KAAK,CAACE,OAAO;IAC7C;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}