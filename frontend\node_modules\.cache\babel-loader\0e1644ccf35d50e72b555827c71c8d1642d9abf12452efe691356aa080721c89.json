{"ast": null, "code": "/**\n * Utility functions for checking user permissions based on roles\n */\n\n// Role definitions\nexport const ROLE_PAO = 'PAO'; // Property Administration Officer\nexport const ROLE_CENTRAL_PAO = 'Central PAO'; // Central Property Administration Officer\nexport const ROLE_STOREKEEPER = 'Storekeeper';\nexport const ROLE_STOCK_CLERK = 'Stock Clerk';\nexport const ROLE_PROCUREMENT = 'Procurement';\nexport const ROLE_USER_DEPT = 'User Department';\n\n// Group to role mapping\nconst GROUP_TO_ROLE = {\n  'Admin': ROLE_CENTRAL_PAO,\n  'Inventory Manager': ROLE_PAO,\n  'Central PAO': ROL<PERSON>_CENTRAL_PAO,\n  'Storekeeper': R<PERSON><PERSON>_STOREKEEPER,\n  'Stock Clerk': ROLE_STOCK_CLERK,\n  'Procurement': ROLE_PROCUREMENT,\n  'User Department': ROLE_USER_DEPT\n};\n\n/**\n * Get user roles based on their groups\n *\n * @param {Object} user - The user object from the authentication service\n * @returns {Array} - Array of role names\n */\nexport const getUserRoles = user => {\n  if (!user) return [];\n  const roles = [];\n\n  // Superusers are considered Central PAO by default\n  if (user.is_superuser) {\n    roles.push(ROLE_CENTRAL_PAO);\n  }\n  // Staff users are considered PAO by default\n  else if (user.is_staff) {\n    roles.push(ROLE_PAO);\n  }\n\n  // Add roles based on user's groups\n  if (user.groups && Array.isArray(user.groups)) {\n    user.groups.forEach(group => {\n      const role = GROUP_TO_ROLE[group];\n      if (role && !roles.includes(role)) {\n        roles.push(role);\n      }\n    });\n  }\n  return roles;\n};\n\n/**\n * Check if a user has a specific role\n *\n * @param {Object} user - The user object from the authentication service\n * @param {String} role - The role to check\n * @returns {Boolean} - True if the user has the role\n */\nexport const hasRole = (user, role) => {\n  const roles = getUserRoles(user);\n  return roles.includes(role);\n};\n\n/**\n * Check if a user can submit requisitions\n *\n * @param {Object} user - The user object from the authentication service\n * @returns {Boolean} - True if the user can submit requisitions\n */\nexport const canSubmitRequisition = user => {\n  return hasRole(user, ROLE_USER_DEPT) || hasRole(user, ROLE_PAO);\n};\n\n/**\n * Check if a user can approve requisitions\n *\n * @param {Object} user - The user object from the authentication service\n * @param {Object} requisition - The requisition object\n * @returns {Boolean} - True if the user can approve requisitions\n */\nexport const canApproveRequisition = (user, requisition) => {\n  // PAO can approve any requisition that doesn't require cross-store transfer\n  if (hasRole(user, ROLE_PAO)) {\n    if (requisition && requisition.status_details) {\n      return requisition.status_details.name.toLowerCase() !== 'pending central pao approval';\n    }\n    return true;\n  }\n\n  // User Department can only approve requisitions that were adjusted\n  if (hasRole(user, ROLE_USER_DEPT)) {\n    if (requisition && requisition.status_details) {\n      return requisition.status_details.name.toLowerCase() === 'adjusted - awaiting confirmation';\n    }\n  }\n  return false;\n};\n\n/**\n * Check if a user can approve cross-store transfers\n *\n * @param {Object} user - The user object from the authentication service\n * @param {Object} requisition - The requisition object\n * @returns {Boolean} - True if the user can approve cross-store transfers\n */\nexport const canApproveCrossStore = (user, requisition) => {\n  // Only Central PAO can approve cross-store transfers\n  if (hasRole(user, ROLE_CENTRAL_PAO)) {\n    if (requisition && requisition.status_details) {\n      return requisition.status_details.name.toLowerCase() === 'pending central pao approval';\n    }\n  }\n  return false;\n};\n\n/**\n * Check if a user can adjust requisitions\n *\n * @param {Object} user - The user object from the authentication service\n * @param {Object} requisition - The requisition object\n * @returns {Boolean} - True if the user can adjust requisitions\n */\nexport const canAdjustRequisition = (user, requisition) => {\n  // Only PAO can adjust requisitions\n  if (!hasRole(user, ROLE_PAO)) {\n    return false;\n  }\n\n  // Can only adjust pending requisitions\n  if (requisition && requisition.status_details) {\n    return requisition.status_details.name.toLowerCase() === 'pending approval';\n  }\n  return false;\n};\n\n/**\n * Check if a user can receive stocks\n *\n * @param {Object} user - The user object from the authentication service\n * @returns {Boolean} - True if the user can receive stocks\n */\nexport const canReceiveStocks = user => {\n  return hasRole(user, ROLE_STOREKEEPER) || hasRole(user, ROLE_PROCUREMENT);\n};\n\n/**\n * Check if a user can issue stocks\n *\n * @param {Object} user - The user object from the authentication service\n * @returns {Boolean} - True if the user can issue stocks\n */\nexport const canIssueStocks = user => {\n  return hasRole(user, ROLE_STOREKEEPER);\n};\n\n/**\n * Check if a user can update bin cards\n *\n * @param {Object} user - The user object from the authentication service\n * @returns {Boolean} - True if the user can update bin cards\n */\nexport const canUpdateBinCards = user => {\n  return hasRole(user, ROLE_STOREKEEPER);\n};\n\n/**\n * Check if a user can update stock records\n *\n * @param {Object} user - The user object from the authentication service\n * @returns {Boolean} - True if the user can update stock records\n */\nexport const canUpdateStockRecords = user => {\n  return hasRole(user, ROLE_STOCK_CLERK);\n};\n\n/**\n * Check if a user can initiate disposal\n *\n * @param {Object} user - The user object from the authentication service\n * @returns {Boolean} - True if the user can initiate disposal\n */\nexport const canInitiateDisposal = user => {\n  return hasRole(user, ROLE_PAO);\n};\n\n/**\n * Check if a user can conduct stock audit\n *\n * @param {Object} user - The user object from the authentication service\n * @returns {Boolean} - True if the user can conduct stock audit\n */\nexport const canConductStockAudit = user => {\n  return hasRole(user, ROLE_PAO) || hasRole(user, ROLE_STOCK_CLERK);\n};\n\n/**\n * Check if a user can assist in stock audit\n *\n * @param {Object} user - The user object from the authentication service\n * @returns {Boolean} - True if the user can assist in stock audit\n */\nexport const canAssistStockAudit = user => {\n  return hasRole(user, ROLE_STOREKEEPER);\n};\n\n/**\n * Get menu items visibility based on user roles\n *\n * @param {Object} user - The user object from the authentication service\n * @returns {Object} - Object with menu categories as keys and boolean values\n */\nexport const getMenuVisibility = user => {\n  const roles = getUserRoles(user);\n  const isPAO = roles.includes(ROLE_PAO);\n  const isStorekeeper = roles.includes(ROLE_STOREKEEPER);\n  const isStockClerk = roles.includes(ROLE_STOCK_CLERK);\n  const isProcurement = roles.includes(ROLE_PROCUREMENT);\n  const isUserDept = roles.includes(ROLE_USER_DEPT);\n  return {\n    // Dashboard is visible to all authenticated users\n    dashboard: true,\n    // Organization management is only for PAO\n    organization: isPAO,\n    // Classification management is only for PAO\n    classification: isPAO,\n    // Specification management is only for PAO\n    specification: isPAO,\n    // Storage management is for PAO and Storekeeper\n    storage: isPAO || isStorekeeper,\n    // Supplier management is for PAO and Procurement\n    supplier: isPAO || isProcurement,\n    // Status management is only for PAO\n    status: isPAO,\n    // Inventory management varies by role\n    inventory: true,\n    // All roles need some inventory access\n\n    // Item masters management is for PAO, Stock Clerk, and Procurement\n    itemMasters: isPAO || isStockClerk || isProcurement,\n    // Batches management is for PAO, Storekeeper, Stock Clerk, and Procurement\n    batches: isPAO || isStorekeeper || isStockClerk || isProcurement,\n    // Items management is for PAO, Storekeeper, and Stock Clerk\n    items: isPAO || isStorekeeper || isStockClerk,\n    // Gate passes are for PAO and Storekeeper\n    gatePasses: isPAO || isStorekeeper,\n    // Requisition management is for all roles but with different permissions\n    requisition: true,\n    // Requisition statuses management is only for PAO\n    requisitionStatuses: isPAO,\n    // Reports are for PAO and Stock Clerk\n    reports: isPAO || isStockClerk,\n    // Receiving & Inspection is for PAO, Storekeeper, and Procurement\n    receiving: isPAO || isStorekeeper || isProcurement,\n    // Item Receive (Pre-Registration) is for PAO and Procurement\n    itemReceive: isPAO || isProcurement\n  };\n};", "map": {"version": 3, "names": ["ROLE_PAO", "ROLE_CENTRAL_PAO", "ROLE_STOREKEEPER", "ROLE_STOCK_CLERK", "ROLE_PROCUREMENT", "ROLE_USER_DEPT", "GROUP_TO_ROLE", "getUserRoles", "user", "roles", "is_superuser", "push", "is_staff", "groups", "Array", "isArray", "for<PERSON>ach", "group", "role", "includes", "hasRole", "canSubmitRequisition", "canApproveRequisition", "requisition", "status_details", "name", "toLowerCase", "canApproveCrossStore", "canAdjustRequisition", "canReceiveStocks", "canIssueStocks", "canUpdateBinCards", "canUpdateStockRecords", "canInitiateDisposal", "canConductStockAudit", "canAssistStockAudit", "getMenuVisibility", "isPAO", "isStorekeeper", "isStockClerk", "isProcurement", "isUserDept", "dashboard", "organization", "classification", "specification", "storage", "supplier", "status", "inventory", "itemMasters", "batches", "items", "gatePasses", "requisitionStatuses", "reports", "receiving", "itemReceive"], "sources": ["C:/Users/<USER>/Desktop/project ai/Store Management System/asset management/frontend/src/utils/permissions.js"], "sourcesContent": ["/**\n * Utility functions for checking user permissions based on roles\n */\n\n// Role definitions\nexport const ROLE_PAO = 'PAO';  // Property Administration Officer\nexport const ROLE_CENTRAL_PAO = 'Central PAO';  // Central Property Administration Officer\nexport const ROLE_STOREKEEPER = 'Storekeeper';\nexport const ROLE_STOCK_CLERK = 'Stock Clerk';\nexport const ROLE_PROCUREMENT = 'Procurement';\nexport const ROLE_USER_DEPT = 'User Department';\n\n// Group to role mapping\nconst GROUP_TO_ROLE = {\n  'Admin': ROLE_CENTRAL_PAO,\n  'Inventory Manager': ROLE_PAO,\n  'Central PAO': ROL<PERSON>_CENTRAL_PAO,\n  'Storekeeper': R<PERSON><PERSON>_STOREKEEPER,\n  'Stock Clerk': ROLE_STOCK_CLERK,\n  'Procurement': ROLE_PROCUREMENT,\n  'User Department': ROLE_USER_DEPT,\n};\n\n/**\n * Get user roles based on their groups\n *\n * @param {Object} user - The user object from the authentication service\n * @returns {Array} - Array of role names\n */\nexport const getUserRoles = (user) => {\n  if (!user) return [];\n\n  const roles = [];\n\n  // Superusers are considered Central PAO by default\n  if (user.is_superuser) {\n    roles.push(ROLE_CENTRAL_PAO);\n  }\n  // Staff users are considered PAO by default\n  else if (user.is_staff) {\n    roles.push(ROLE_PAO);\n  }\n\n  // Add roles based on user's groups\n  if (user.groups && Array.isArray(user.groups)) {\n    user.groups.forEach(group => {\n      const role = GROUP_TO_ROLE[group];\n      if (role && !roles.includes(role)) {\n        roles.push(role);\n      }\n    });\n  }\n\n  return roles;\n};\n\n/**\n * Check if a user has a specific role\n *\n * @param {Object} user - The user object from the authentication service\n * @param {String} role - The role to check\n * @returns {Boolean} - True if the user has the role\n */\nexport const hasRole = (user, role) => {\n  const roles = getUserRoles(user);\n  return roles.includes(role);\n};\n\n/**\n * Check if a user can submit requisitions\n *\n * @param {Object} user - The user object from the authentication service\n * @returns {Boolean} - True if the user can submit requisitions\n */\nexport const canSubmitRequisition = (user) => {\n  return hasRole(user, ROLE_USER_DEPT) || hasRole(user, ROLE_PAO);\n};\n\n/**\n * Check if a user can approve requisitions\n *\n * @param {Object} user - The user object from the authentication service\n * @param {Object} requisition - The requisition object\n * @returns {Boolean} - True if the user can approve requisitions\n */\nexport const canApproveRequisition = (user, requisition) => {\n  // PAO can approve any requisition that doesn't require cross-store transfer\n  if (hasRole(user, ROLE_PAO)) {\n    if (requisition && requisition.status_details) {\n      return requisition.status_details.name.toLowerCase() !== 'pending central pao approval';\n    }\n    return true;\n  }\n\n  // User Department can only approve requisitions that were adjusted\n  if (hasRole(user, ROLE_USER_DEPT)) {\n    if (requisition && requisition.status_details) {\n      return requisition.status_details.name.toLowerCase() === 'adjusted - awaiting confirmation';\n    }\n  }\n\n  return false;\n};\n\n/**\n * Check if a user can approve cross-store transfers\n *\n * @param {Object} user - The user object from the authentication service\n * @param {Object} requisition - The requisition object\n * @returns {Boolean} - True if the user can approve cross-store transfers\n */\nexport const canApproveCrossStore = (user, requisition) => {\n  // Only Central PAO can approve cross-store transfers\n  if (hasRole(user, ROLE_CENTRAL_PAO)) {\n    if (requisition && requisition.status_details) {\n      return requisition.status_details.name.toLowerCase() === 'pending central pao approval';\n    }\n  }\n\n  return false;\n};\n\n/**\n * Check if a user can adjust requisitions\n *\n * @param {Object} user - The user object from the authentication service\n * @param {Object} requisition - The requisition object\n * @returns {Boolean} - True if the user can adjust requisitions\n */\nexport const canAdjustRequisition = (user, requisition) => {\n  // Only PAO can adjust requisitions\n  if (!hasRole(user, ROLE_PAO)) {\n    return false;\n  }\n\n  // Can only adjust pending requisitions\n  if (requisition && requisition.status_details) {\n    return requisition.status_details.name.toLowerCase() === 'pending approval';\n  }\n\n  return false;\n};\n\n/**\n * Check if a user can receive stocks\n *\n * @param {Object} user - The user object from the authentication service\n * @returns {Boolean} - True if the user can receive stocks\n */\nexport const canReceiveStocks = (user) => {\n  return hasRole(user, ROLE_STOREKEEPER) || hasRole(user, ROLE_PROCUREMENT);\n};\n\n/**\n * Check if a user can issue stocks\n *\n * @param {Object} user - The user object from the authentication service\n * @returns {Boolean} - True if the user can issue stocks\n */\nexport const canIssueStocks = (user) => {\n  return hasRole(user, ROLE_STOREKEEPER);\n};\n\n/**\n * Check if a user can update bin cards\n *\n * @param {Object} user - The user object from the authentication service\n * @returns {Boolean} - True if the user can update bin cards\n */\nexport const canUpdateBinCards = (user) => {\n  return hasRole(user, ROLE_STOREKEEPER);\n};\n\n/**\n * Check if a user can update stock records\n *\n * @param {Object} user - The user object from the authentication service\n * @returns {Boolean} - True if the user can update stock records\n */\nexport const canUpdateStockRecords = (user) => {\n  return hasRole(user, ROLE_STOCK_CLERK);\n};\n\n/**\n * Check if a user can initiate disposal\n *\n * @param {Object} user - The user object from the authentication service\n * @returns {Boolean} - True if the user can initiate disposal\n */\nexport const canInitiateDisposal = (user) => {\n  return hasRole(user, ROLE_PAO);\n};\n\n/**\n * Check if a user can conduct stock audit\n *\n * @param {Object} user - The user object from the authentication service\n * @returns {Boolean} - True if the user can conduct stock audit\n */\nexport const canConductStockAudit = (user) => {\n  return hasRole(user, ROLE_PAO) || hasRole(user, ROLE_STOCK_CLERK);\n};\n\n/**\n * Check if a user can assist in stock audit\n *\n * @param {Object} user - The user object from the authentication service\n * @returns {Boolean} - True if the user can assist in stock audit\n */\nexport const canAssistStockAudit = (user) => {\n  return hasRole(user, ROLE_STOREKEEPER);\n};\n\n/**\n * Get menu items visibility based on user roles\n *\n * @param {Object} user - The user object from the authentication service\n * @returns {Object} - Object with menu categories as keys and boolean values\n */\nexport const getMenuVisibility = (user) => {\n  const roles = getUserRoles(user);\n  const isPAO = roles.includes(ROLE_PAO);\n  const isStorekeeper = roles.includes(ROLE_STOREKEEPER);\n  const isStockClerk = roles.includes(ROLE_STOCK_CLERK);\n  const isProcurement = roles.includes(ROLE_PROCUREMENT);\n  const isUserDept = roles.includes(ROLE_USER_DEPT);\n\n  return {\n    // Dashboard is visible to all authenticated users\n    dashboard: true,\n\n    // Organization management is only for PAO\n    organization: isPAO,\n\n    // Classification management is only for PAO\n    classification: isPAO,\n\n    // Specification management is only for PAO\n    specification: isPAO,\n\n    // Storage management is for PAO and Storekeeper\n    storage: isPAO || isStorekeeper,\n\n    // Supplier management is for PAO and Procurement\n    supplier: isPAO || isProcurement,\n\n    // Status management is only for PAO\n    status: isPAO,\n\n    // Inventory management varies by role\n    inventory: true, // All roles need some inventory access\n\n    // Item masters management is for PAO, Stock Clerk, and Procurement\n    itemMasters: isPAO || isStockClerk || isProcurement,\n\n    // Batches management is for PAO, Storekeeper, Stock Clerk, and Procurement\n    batches: isPAO || isStorekeeper || isStockClerk || isProcurement,\n\n    // Items management is for PAO, Storekeeper, and Stock Clerk\n    items: isPAO || isStorekeeper || isStockClerk,\n\n    // Gate passes are for PAO and Storekeeper\n    gatePasses: isPAO || isStorekeeper,\n\n    // Requisition management is for all roles but with different permissions\n    requisition: true,\n\n    // Requisition statuses management is only for PAO\n    requisitionStatuses: isPAO,\n\n    // Reports are for PAO and Stock Clerk\n    reports: isPAO || isStockClerk,\n\n    // Receiving & Inspection is for PAO, Storekeeper, and Procurement\n    receiving: isPAO || isStorekeeper || isProcurement,\n\n    // Item Receive (Pre-Registration) is for PAO and Procurement\n    itemReceive: isPAO || isProcurement,\n  };\n};\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA,OAAO,MAAMA,QAAQ,GAAG,KAAK,CAAC,CAAE;AAChC,OAAO,MAAMC,gBAAgB,GAAG,aAAa,CAAC,CAAE;AAChD,OAAO,MAAMC,gBAAgB,GAAG,aAAa;AAC7C,OAAO,MAAMC,gBAAgB,GAAG,aAAa;AAC7C,OAAO,MAAMC,gBAAgB,GAAG,aAAa;AAC7C,OAAO,MAAMC,cAAc,GAAG,iBAAiB;;AAE/C;AACA,MAAMC,aAAa,GAAG;EACpB,OAAO,EAAEL,gBAAgB;EACzB,mBAAmB,EAAED,QAAQ;EAC7B,aAAa,EAAEC,gBAAgB;EAC/B,aAAa,EAAEC,gBAAgB;EAC/B,aAAa,EAAEC,gBAAgB;EAC/B,aAAa,EAAEC,gBAAgB;EAC/B,iBAAiB,EAAEC;AACrB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,YAAY,GAAIC,IAAI,IAAK;EACpC,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;EAEpB,MAAMC,KAAK,GAAG,EAAE;;EAEhB;EACA,IAAID,IAAI,CAACE,YAAY,EAAE;IACrBD,KAAK,CAACE,IAAI,CAACV,gBAAgB,CAAC;EAC9B;EACA;EAAA,KACK,IAAIO,IAAI,CAACI,QAAQ,EAAE;IACtBH,KAAK,CAACE,IAAI,CAACX,QAAQ,CAAC;EACtB;;EAEA;EACA,IAAIQ,IAAI,CAACK,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACP,IAAI,CAACK,MAAM,CAAC,EAAE;IAC7CL,IAAI,CAACK,MAAM,CAACG,OAAO,CAACC,KAAK,IAAI;MAC3B,MAAMC,IAAI,GAAGZ,aAAa,CAACW,KAAK,CAAC;MACjC,IAAIC,IAAI,IAAI,CAACT,KAAK,CAACU,QAAQ,CAACD,IAAI,CAAC,EAAE;QACjCT,KAAK,CAACE,IAAI,CAACO,IAAI,CAAC;MAClB;IACF,CAAC,CAAC;EACJ;EAEA,OAAOT,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMW,OAAO,GAAGA,CAACZ,IAAI,EAAEU,IAAI,KAAK;EACrC,MAAMT,KAAK,GAAGF,YAAY,CAACC,IAAI,CAAC;EAChC,OAAOC,KAAK,CAACU,QAAQ,CAACD,IAAI,CAAC;AAC7B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMG,oBAAoB,GAAIb,IAAI,IAAK;EAC5C,OAAOY,OAAO,CAACZ,IAAI,EAAEH,cAAc,CAAC,IAAIe,OAAO,CAACZ,IAAI,EAAER,QAAQ,CAAC;AACjE,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMsB,qBAAqB,GAAGA,CAACd,IAAI,EAAEe,WAAW,KAAK;EAC1D;EACA,IAAIH,OAAO,CAACZ,IAAI,EAAER,QAAQ,CAAC,EAAE;IAC3B,IAAIuB,WAAW,IAAIA,WAAW,CAACC,cAAc,EAAE;MAC7C,OAAOD,WAAW,CAACC,cAAc,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,KAAK,8BAA8B;IACzF;IACA,OAAO,IAAI;EACb;;EAEA;EACA,IAAIN,OAAO,CAACZ,IAAI,EAAEH,cAAc,CAAC,EAAE;IACjC,IAAIkB,WAAW,IAAIA,WAAW,CAACC,cAAc,EAAE;MAC7C,OAAOD,WAAW,CAACC,cAAc,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,KAAK,kCAAkC;IAC7F;EACF;EAEA,OAAO,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,oBAAoB,GAAGA,CAACnB,IAAI,EAAEe,WAAW,KAAK;EACzD;EACA,IAAIH,OAAO,CAACZ,IAAI,EAAEP,gBAAgB,CAAC,EAAE;IACnC,IAAIsB,WAAW,IAAIA,WAAW,CAACC,cAAc,EAAE;MAC7C,OAAOD,WAAW,CAACC,cAAc,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,KAAK,8BAA8B;IACzF;EACF;EAEA,OAAO,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,oBAAoB,GAAGA,CAACpB,IAAI,EAAEe,WAAW,KAAK;EACzD;EACA,IAAI,CAACH,OAAO,CAACZ,IAAI,EAAER,QAAQ,CAAC,EAAE;IAC5B,OAAO,KAAK;EACd;;EAEA;EACA,IAAIuB,WAAW,IAAIA,WAAW,CAACC,cAAc,EAAE;IAC7C,OAAOD,WAAW,CAACC,cAAc,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,KAAK,kBAAkB;EAC7E;EAEA,OAAO,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMG,gBAAgB,GAAIrB,IAAI,IAAK;EACxC,OAAOY,OAAO,CAACZ,IAAI,EAAEN,gBAAgB,CAAC,IAAIkB,OAAO,CAACZ,IAAI,EAAEJ,gBAAgB,CAAC;AAC3E,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM0B,cAAc,GAAItB,IAAI,IAAK;EACtC,OAAOY,OAAO,CAACZ,IAAI,EAAEN,gBAAgB,CAAC;AACxC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM6B,iBAAiB,GAAIvB,IAAI,IAAK;EACzC,OAAOY,OAAO,CAACZ,IAAI,EAAEN,gBAAgB,CAAC;AACxC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM8B,qBAAqB,GAAIxB,IAAI,IAAK;EAC7C,OAAOY,OAAO,CAACZ,IAAI,EAAEL,gBAAgB,CAAC;AACxC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM8B,mBAAmB,GAAIzB,IAAI,IAAK;EAC3C,OAAOY,OAAO,CAACZ,IAAI,EAAER,QAAQ,CAAC;AAChC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMkC,oBAAoB,GAAI1B,IAAI,IAAK;EAC5C,OAAOY,OAAO,CAACZ,IAAI,EAAER,QAAQ,CAAC,IAAIoB,OAAO,CAACZ,IAAI,EAAEL,gBAAgB,CAAC;AACnE,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMgC,mBAAmB,GAAI3B,IAAI,IAAK;EAC3C,OAAOY,OAAO,CAACZ,IAAI,EAAEN,gBAAgB,CAAC;AACxC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMkC,iBAAiB,GAAI5B,IAAI,IAAK;EACzC,MAAMC,KAAK,GAAGF,YAAY,CAACC,IAAI,CAAC;EAChC,MAAM6B,KAAK,GAAG5B,KAAK,CAACU,QAAQ,CAACnB,QAAQ,CAAC;EACtC,MAAMsC,aAAa,GAAG7B,KAAK,CAACU,QAAQ,CAACjB,gBAAgB,CAAC;EACtD,MAAMqC,YAAY,GAAG9B,KAAK,CAACU,QAAQ,CAAChB,gBAAgB,CAAC;EACrD,MAAMqC,aAAa,GAAG/B,KAAK,CAACU,QAAQ,CAACf,gBAAgB,CAAC;EACtD,MAAMqC,UAAU,GAAGhC,KAAK,CAACU,QAAQ,CAACd,cAAc,CAAC;EAEjD,OAAO;IACL;IACAqC,SAAS,EAAE,IAAI;IAEf;IACAC,YAAY,EAAEN,KAAK;IAEnB;IACAO,cAAc,EAAEP,KAAK;IAErB;IACAQ,aAAa,EAAER,KAAK;IAEpB;IACAS,OAAO,EAAET,KAAK,IAAIC,aAAa;IAE/B;IACAS,QAAQ,EAAEV,KAAK,IAAIG,aAAa;IAEhC;IACAQ,MAAM,EAAEX,KAAK;IAEb;IACAY,SAAS,EAAE,IAAI;IAAE;;IAEjB;IACAC,WAAW,EAAEb,KAAK,IAAIE,YAAY,IAAIC,aAAa;IAEnD;IACAW,OAAO,EAAEd,KAAK,IAAIC,aAAa,IAAIC,YAAY,IAAIC,aAAa;IAEhE;IACAY,KAAK,EAAEf,KAAK,IAAIC,aAAa,IAAIC,YAAY;IAE7C;IACAc,UAAU,EAAEhB,KAAK,IAAIC,aAAa;IAElC;IACAf,WAAW,EAAE,IAAI;IAEjB;IACA+B,mBAAmB,EAAEjB,KAAK;IAE1B;IACAkB,OAAO,EAAElB,KAAK,IAAIE,YAAY;IAE9B;IACAiB,SAAS,EAAEnB,KAAK,IAAIC,aAAa,IAAIE,aAAa;IAElD;IACAiB,WAAW,EAAEpB,KAAK,IAAIG;EACxB,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}