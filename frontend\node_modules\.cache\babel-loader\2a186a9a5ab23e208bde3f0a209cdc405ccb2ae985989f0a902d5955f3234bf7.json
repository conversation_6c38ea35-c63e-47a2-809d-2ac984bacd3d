{"ast": null, "code": "import api from '../utils/axios';\nexport const authService = {\n  login: async credentials => {\n    try {\n      const response = await api.post('/auth/token/', credentials);\n      const {\n        token,\n        user\n      } = response.data;\n      if (token) {\n        localStorage.setItem('token', token);\n        localStorage.setItem('user', JSON.stringify(user || {}));\n      }\n      return response.data;\n    } catch (error) {\n      var _error$response;\n      console.error('Login error:', error);\n      throw ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data) || error.message;\n    }\n  },\n  register: async userData => {\n    try {\n      const response = await api.post('/users/', userData);\n      return response.data;\n    } catch (error) {\n      var _error$response2;\n      throw ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data) || error.message;\n    }\n  },\n  logout: () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n  },\n  getCurrentUser: async () => {\n    try {\n      const response = await api.get('/auth/user/');\n      return response.data;\n    } catch (error) {\n      var _error$response3;\n      throw ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data) || error.message;\n    }\n  },\n  updateProfile: async userData => {\n    try {\n      const response = await api.patch('/auth/user/', userData);\n      return response.data;\n    } catch (error) {\n      var _error$response4;\n      throw ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : _error$response4.data) || error.message;\n    }\n  }\n};", "map": {"version": 3, "names": ["api", "authService", "login", "credentials", "response", "post", "token", "user", "data", "localStorage", "setItem", "JSON", "stringify", "error", "_error$response", "console", "message", "register", "userData", "_error$response2", "logout", "removeItem", "getCurrentUser", "get", "_error$response3", "updateProfile", "patch", "_error$response4"], "sources": ["C:/Users/<USER>/Desktop/project ai/Store Management System/asset management/frontend/src/services/auth.js"], "sourcesContent": ["import api from '../utils/axios';\n\nexport const authService = {\n  login: async (credentials) => {\n    try {\n      const response = await api.post('/auth/token/', credentials);\n      const { token, user } = response.data;\n\n      if (token) {\n        localStorage.setItem('token', token);\n        localStorage.setItem('user', JSON.stringify(user || {}));\n      }\n\n      return response.data;\n    } catch (error) {\n      console.error('Login error:', error);\n      throw error.response?.data || error.message;\n    }\n  },\n\n  register: async (userData) => {\n    try {\n      const response = await api.post('/users/', userData);\n      return response.data;\n    } catch (error) {\n      throw error.response?.data || error.message;\n    }\n  },\n\n  logout: () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n  },\n\n  getCurrentUser: async () => {\n    try {\n      const response = await api.get('/auth/user/');\n      return response.data;\n    } catch (error) {\n      throw error.response?.data || error.message;\n    }\n  },\n\n  updateProfile: async (userData) => {\n    try {\n      const response = await api.patch('/auth/user/', userData);\n      return response.data;\n    } catch (error) {\n      throw error.response?.data || error.message;\n    }\n  }\n};\n\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,gBAAgB;AAEhC,OAAO,MAAMC,WAAW,GAAG;EACzBC,KAAK,EAAE,MAAOC,WAAW,IAAK;IAC5B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMJ,GAAG,CAACK,IAAI,CAAC,cAAc,EAAEF,WAAW,CAAC;MAC5D,MAAM;QAAEG,KAAK;QAAEC;MAAK,CAAC,GAAGH,QAAQ,CAACI,IAAI;MAErC,IAAIF,KAAK,EAAE;QACTG,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEJ,KAAK,CAAC;QACpCG,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACL,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;MAC1D;MAEA,OAAOH,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOK,KAAK,EAAE;MAAA,IAAAC,eAAA;MACdC,OAAO,CAACF,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC,MAAM,EAAAC,eAAA,GAAAD,KAAK,CAACT,QAAQ,cAAAU,eAAA,uBAAdA,eAAA,CAAgBN,IAAI,KAAIK,KAAK,CAACG,OAAO;IAC7C;EACF,CAAC;EAEDC,QAAQ,EAAE,MAAOC,QAAQ,IAAK;IAC5B,IAAI;MACF,MAAMd,QAAQ,GAAG,MAAMJ,GAAG,CAACK,IAAI,CAAC,SAAS,EAAEa,QAAQ,CAAC;MACpD,OAAOd,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOK,KAAK,EAAE;MAAA,IAAAM,gBAAA;MACd,MAAM,EAAAA,gBAAA,GAAAN,KAAK,CAACT,QAAQ,cAAAe,gBAAA,uBAAdA,gBAAA,CAAgBX,IAAI,KAAIK,KAAK,CAACG,OAAO;IAC7C;EACF,CAAC;EAEDI,MAAM,EAAEA,CAAA,KAAM;IACZX,YAAY,CAACY,UAAU,CAAC,OAAO,CAAC;IAChCZ,YAAY,CAACY,UAAU,CAAC,MAAM,CAAC;EACjC,CAAC;EAEDC,cAAc,EAAE,MAAAA,CAAA,KAAY;IAC1B,IAAI;MACF,MAAMlB,QAAQ,GAAG,MAAMJ,GAAG,CAACuB,GAAG,CAAC,aAAa,CAAC;MAC7C,OAAOnB,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOK,KAAK,EAAE;MAAA,IAAAW,gBAAA;MACd,MAAM,EAAAA,gBAAA,GAAAX,KAAK,CAACT,QAAQ,cAAAoB,gBAAA,uBAAdA,gBAAA,CAAgBhB,IAAI,KAAIK,KAAK,CAACG,OAAO;IAC7C;EACF,CAAC;EAEDS,aAAa,EAAE,MAAOP,QAAQ,IAAK;IACjC,IAAI;MACF,MAAMd,QAAQ,GAAG,MAAMJ,GAAG,CAAC0B,KAAK,CAAC,aAAa,EAAER,QAAQ,CAAC;MACzD,OAAOd,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOK,KAAK,EAAE;MAAA,IAAAc,gBAAA;MACd,MAAM,EAAAA,gBAAA,GAAAd,KAAK,CAACT,QAAQ,cAAAuB,gBAAA,uBAAdA,gBAAA,CAAgBnB,IAAI,KAAIK,KAAK,CAACG,OAAO;IAC7C;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}