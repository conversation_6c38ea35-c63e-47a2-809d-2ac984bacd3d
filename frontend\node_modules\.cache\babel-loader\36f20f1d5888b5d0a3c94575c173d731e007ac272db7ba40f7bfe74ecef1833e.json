{"ast": null, "code": "import api from './axios';\nimport { jwtDecode } from 'jwt-decode';\n\n/**\n * Get the current user's token from localStorage\n *\n * @returns {string|null} - The JWT token or null if not found\n */\nexport const getToken = () => {\n  return localStorage.getItem('token');\n};\n\n/**\n * Set the user's token in localStorage\n *\n * @param {string} token - The JWT token to store\n */\nexport const setToken = token => {\n  localStorage.setItem('token', token);\n};\n\n/**\n * Remove the user's token from localStorage\n */\nexport const removeToken = () => {\n  localStorage.removeItem('token');\n};\n\n/**\n * Check if the user is authenticated (has a valid token)\n *\n * @returns {boolean} - True if authenticated, false otherwise\n */\nexport const isAuthenticated = () => {\n  const token = getToken();\n  if (!token) {\n    return false;\n  }\n  try {\n    const decoded = jwtDecode(token);\n    const currentTime = Date.now() / 1000;\n\n    // Check if token is expired\n    if (decoded.exp < currentTime) {\n      removeToken();\n      return false;\n    }\n    return true;\n  } catch (error) {\n    console.error('Error decoding token:', error);\n    removeToken();\n    return false;\n  }\n};\n\n/**\n * Get the current user's information from the token\n *\n * @returns {Object|null} - User information or null if not authenticated\n */\nexport const getUserInfo = () => {\n  const token = getToken();\n  if (!token) {\n    return null;\n  }\n  try {\n    const decoded = jwtDecode(token);\n    return decoded;\n  } catch (error) {\n    console.error('Error decoding token:', error);\n    return null;\n  }\n};\n\n/**\n * Check if the current user has a specific role\n *\n * @param {string|string[]} roles - Role or array of roles to check\n * @returns {boolean} - True if user has the role, false otherwise\n */\nexport const hasRole = roles => {\n  const userInfo = getUserInfo();\n  if (!userInfo || !userInfo.roles) {\n    return false;\n  }\n  if (Array.isArray(roles)) {\n    return roles.some(role => userInfo.roles.includes(role));\n  }\n  return userInfo.roles.includes(roles);\n};\n\n/**\n * Check if the current user has a specific permission\n *\n * @param {string|string[]} permissions - Permission or array of permissions to check\n * @returns {boolean} - True if user has the permission, false otherwise\n */\nexport const hasPermission = permissions => {\n  const userInfo = getUserInfo();\n  if (!userInfo || !userInfo.permissions) {\n    return false;\n  }\n  if (Array.isArray(permissions)) {\n    return permissions.some(permission => userInfo.permissions.includes(permission));\n  }\n  return userInfo.permissions.includes(permissions);\n};\n\n/**\n * Authentication service for login, logout, and user management\n */\nexport const authService = {\n  /**\n   * Login with username and password\n   *\n   * @param {Object} credentials - Login credentials\n   * @param {string} credentials.username - Username\n   * @param {string} credentials.password - Password\n   * @returns {Promise} - Promise resolving to the login response\n   */\n  login: async credentials => {\n    try {\n      const response = await api.post('/auth/token/', credentials);\n      const {\n        token\n      } = response.data;\n      if (token) {\n        setToken(token);\n      }\n      return response.data;\n    } catch (error) {\n      console.error('Login error:', error);\n      throw error;\n    }\n  },\n  /**\n   * Logout the current user\n   */\n  logout: () => {\n    removeToken();\n    window.location.href = '/login';\n  },\n  /**\n   * Get the current user's profile\n   *\n   * @returns {Promise} - Promise resolving to the user profile\n   */\n  getCurrentUser: async () => {\n    try {\n      const response = await api.get('/auth/me/');\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching current user:', error);\n      throw error;\n    }\n  },\n  /**\n   * Update the current user's profile\n   *\n   * @param {Object} userData - Updated user data\n   * @returns {Promise} - Promise resolving to the updated user profile\n   */\n  updateProfile: async userData => {\n    try {\n      const response = await api.put('/auth/me/', userData);\n      return response.data;\n    } catch (error) {\n      console.error('Error updating profile:', error);\n      throw error;\n    }\n  },\n  /**\n   * Change the current user's password\n   *\n   * @param {Object} passwordData - Password change data\n   * @param {string} passwordData.old_password - Current password\n   * @param {string} passwordData.new_password - New password\n   * @returns {Promise} - Promise resolving to the password change response\n   */\n  changePassword: async passwordData => {\n    try {\n      const response = await api.post('/auth/change-password/', passwordData);\n      return response.data;\n    } catch (error) {\n      console.error('Error changing password:', error);\n      throw error;\n    }\n  }\n};\n\n// Create a named export object to satisfy ESLint\nconst authUtils = {\n  getToken,\n  setToken,\n  removeToken,\n  isAuthenticated,\n  getUserInfo,\n  hasRole,\n  hasPermission,\n  authService\n};\nexport default authUtils;", "map": {"version": 3, "names": ["api", "jwtDecode", "getToken", "localStorage", "getItem", "setToken", "token", "setItem", "removeToken", "removeItem", "isAuthenticated", "decoded", "currentTime", "Date", "now", "exp", "error", "console", "getUserInfo", "hasRole", "roles", "userInfo", "Array", "isArray", "some", "role", "includes", "hasPermission", "permissions", "permission", "authService", "login", "credentials", "response", "post", "data", "logout", "window", "location", "href", "getCurrentUser", "get", "updateProfile", "userData", "put", "changePassword", "passwordData", "authUtils"], "sources": ["C:/Users/<USER>/Desktop/project ai/Store Management System/asset management/frontend/src/utils/auth.js"], "sourcesContent": ["import api from './axios';\nimport { jwtDecode } from 'jwt-decode';\n\n/**\n * Get the current user's token from localStorage\n *\n * @returns {string|null} - The JWT token or null if not found\n */\nexport const getToken = () => {\n  return localStorage.getItem('token');\n};\n\n/**\n * Set the user's token in localStorage\n *\n * @param {string} token - The JWT token to store\n */\nexport const setToken = (token) => {\n  localStorage.setItem('token', token);\n};\n\n/**\n * Remove the user's token from localStorage\n */\nexport const removeToken = () => {\n  localStorage.removeItem('token');\n};\n\n/**\n * Check if the user is authenticated (has a valid token)\n *\n * @returns {boolean} - True if authenticated, false otherwise\n */\nexport const isAuthenticated = () => {\n  const token = getToken();\n\n  if (!token) {\n    return false;\n  }\n\n  try {\n    const decoded = jwtDecode(token);\n    const currentTime = Date.now() / 1000;\n\n    // Check if token is expired\n    if (decoded.exp < currentTime) {\n      removeToken();\n      return false;\n    }\n\n    return true;\n  } catch (error) {\n    console.error('Error decoding token:', error);\n    removeToken();\n    return false;\n  }\n};\n\n/**\n * Get the current user's information from the token\n *\n * @returns {Object|null} - User information or null if not authenticated\n */\nexport const getUserInfo = () => {\n  const token = getToken();\n\n  if (!token) {\n    return null;\n  }\n\n  try {\n    const decoded = jwtDecode(token);\n    return decoded;\n  } catch (error) {\n    console.error('Error decoding token:', error);\n    return null;\n  }\n};\n\n/**\n * Check if the current user has a specific role\n *\n * @param {string|string[]} roles - Role or array of roles to check\n * @returns {boolean} - True if user has the role, false otherwise\n */\nexport const hasRole = (roles) => {\n  const userInfo = getUserInfo();\n\n  if (!userInfo || !userInfo.roles) {\n    return false;\n  }\n\n  if (Array.isArray(roles)) {\n    return roles.some(role => userInfo.roles.includes(role));\n  }\n\n  return userInfo.roles.includes(roles);\n};\n\n/**\n * Check if the current user has a specific permission\n *\n * @param {string|string[]} permissions - Permission or array of permissions to check\n * @returns {boolean} - True if user has the permission, false otherwise\n */\nexport const hasPermission = (permissions) => {\n  const userInfo = getUserInfo();\n\n  if (!userInfo || !userInfo.permissions) {\n    return false;\n  }\n\n  if (Array.isArray(permissions)) {\n    return permissions.some(permission => userInfo.permissions.includes(permission));\n  }\n\n  return userInfo.permissions.includes(permissions);\n};\n\n/**\n * Authentication service for login, logout, and user management\n */\nexport const authService = {\n  /**\n   * Login with username and password\n   *\n   * @param {Object} credentials - Login credentials\n   * @param {string} credentials.username - Username\n   * @param {string} credentials.password - Password\n   * @returns {Promise} - Promise resolving to the login response\n   */\n  login: async (credentials) => {\n    try {\n      const response = await api.post('/auth/token/', credentials);\n      const { token } = response.data;\n\n      if (token) {\n        setToken(token);\n      }\n\n      return response.data;\n    } catch (error) {\n      console.error('Login error:', error);\n      throw error;\n    }\n  },\n\n  /**\n   * Logout the current user\n   */\n  logout: () => {\n    removeToken();\n    window.location.href = '/login';\n  },\n\n  /**\n   * Get the current user's profile\n   *\n   * @returns {Promise} - Promise resolving to the user profile\n   */\n  getCurrentUser: async () => {\n    try {\n      const response = await api.get('/auth/me/');\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching current user:', error);\n      throw error;\n    }\n  },\n\n  /**\n   * Update the current user's profile\n   *\n   * @param {Object} userData - Updated user data\n   * @returns {Promise} - Promise resolving to the updated user profile\n   */\n  updateProfile: async (userData) => {\n    try {\n      const response = await api.put('/auth/me/', userData);\n      return response.data;\n    } catch (error) {\n      console.error('Error updating profile:', error);\n      throw error;\n    }\n  },\n\n  /**\n   * Change the current user's password\n   *\n   * @param {Object} passwordData - Password change data\n   * @param {string} passwordData.old_password - Current password\n   * @param {string} passwordData.new_password - New password\n   * @returns {Promise} - Promise resolving to the password change response\n   */\n  changePassword: async (passwordData) => {\n    try {\n      const response = await api.post('/auth/change-password/', passwordData);\n      return response.data;\n    } catch (error) {\n      console.error('Error changing password:', error);\n      throw error;\n    }\n  },\n};\n\n// Create a named export object to satisfy ESLint\nconst authUtils = {\n  getToken,\n  setToken,\n  removeToken,\n  isAuthenticated,\n  getUserInfo,\n  hasRole,\n  hasPermission,\n  authService,\n};\n\nexport default authUtils;\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,SAAS;AACzB,SAASC,SAAS,QAAQ,YAAY;;AAEtC;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAC5B,OAAOC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;AACtC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,QAAQ,GAAIC,KAAK,IAAK;EACjCH,YAAY,CAACI,OAAO,CAAC,OAAO,EAAED,KAAK,CAAC;AACtC,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAME,WAAW,GAAGA,CAAA,KAAM;EAC/BL,YAAY,CAACM,UAAU,CAAC,OAAO,CAAC;AAClC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,eAAe,GAAGA,CAAA,KAAM;EACnC,MAAMJ,KAAK,GAAGJ,QAAQ,CAAC,CAAC;EAExB,IAAI,CAACI,KAAK,EAAE;IACV,OAAO,KAAK;EACd;EAEA,IAAI;IACF,MAAMK,OAAO,GAAGV,SAAS,CAACK,KAAK,CAAC;IAChC,MAAMM,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI;;IAErC;IACA,IAAIH,OAAO,CAACI,GAAG,GAAGH,WAAW,EAAE;MAC7BJ,WAAW,CAAC,CAAC;MACb,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb,CAAC,CAAC,OAAOQ,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC7CR,WAAW,CAAC,CAAC;IACb,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMU,WAAW,GAAGA,CAAA,KAAM;EAC/B,MAAMZ,KAAK,GAAGJ,QAAQ,CAAC,CAAC;EAExB,IAAI,CAACI,KAAK,EAAE;IACV,OAAO,IAAI;EACb;EAEA,IAAI;IACF,MAAMK,OAAO,GAAGV,SAAS,CAACK,KAAK,CAAC;IAChC,OAAOK,OAAO;EAChB,CAAC,CAAC,OAAOK,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC7C,OAAO,IAAI;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMG,OAAO,GAAIC,KAAK,IAAK;EAChC,MAAMC,QAAQ,GAAGH,WAAW,CAAC,CAAC;EAE9B,IAAI,CAACG,QAAQ,IAAI,CAACA,QAAQ,CAACD,KAAK,EAAE;IAChC,OAAO,KAAK;EACd;EAEA,IAAIE,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,EAAE;IACxB,OAAOA,KAAK,CAACI,IAAI,CAACC,IAAI,IAAIJ,QAAQ,CAACD,KAAK,CAACM,QAAQ,CAACD,IAAI,CAAC,CAAC;EAC1D;EAEA,OAAOJ,QAAQ,CAACD,KAAK,CAACM,QAAQ,CAACN,KAAK,CAAC;AACvC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMO,aAAa,GAAIC,WAAW,IAAK;EAC5C,MAAMP,QAAQ,GAAGH,WAAW,CAAC,CAAC;EAE9B,IAAI,CAACG,QAAQ,IAAI,CAACA,QAAQ,CAACO,WAAW,EAAE;IACtC,OAAO,KAAK;EACd;EAEA,IAAIN,KAAK,CAACC,OAAO,CAACK,WAAW,CAAC,EAAE;IAC9B,OAAOA,WAAW,CAACJ,IAAI,CAACK,UAAU,IAAIR,QAAQ,CAACO,WAAW,CAACF,QAAQ,CAACG,UAAU,CAAC,CAAC;EAClF;EAEA,OAAOR,QAAQ,CAACO,WAAW,CAACF,QAAQ,CAACE,WAAW,CAAC;AACnD,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAME,WAAW,GAAG;EACzB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,KAAK,EAAE,MAAOC,WAAW,IAAK;IAC5B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMjC,GAAG,CAACkC,IAAI,CAAC,cAAc,EAAEF,WAAW,CAAC;MAC5D,MAAM;QAAE1B;MAAM,CAAC,GAAG2B,QAAQ,CAACE,IAAI;MAE/B,IAAI7B,KAAK,EAAE;QACTD,QAAQ,CAACC,KAAK,CAAC;MACjB;MAEA,OAAO2B,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC,MAAMA,KAAK;IACb;EACF,CAAC;EAED;AACF;AACA;EACEoB,MAAM,EAAEA,CAAA,KAAM;IACZ5B,WAAW,CAAC,CAAC;IACb6B,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC,CAAC;EAED;AACF;AACA;AACA;AACA;EACEC,cAAc,EAAE,MAAAA,CAAA,KAAY;IAC1B,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMjC,GAAG,CAACyC,GAAG,CAAC,WAAW,CAAC;MAC3C,OAAOR,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK;IACb;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACE0B,aAAa,EAAE,MAAOC,QAAQ,IAAK;IACjC,IAAI;MACF,MAAMV,QAAQ,GAAG,MAAMjC,GAAG,CAAC4C,GAAG,CAAC,WAAW,EAAED,QAAQ,CAAC;MACrD,OAAOV,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE6B,cAAc,EAAE,MAAOC,YAAY,IAAK;IACtC,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAMjC,GAAG,CAACkC,IAAI,CAAC,wBAAwB,EAAEY,YAAY,CAAC;MACvE,OAAOb,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,MAAMA,KAAK;IACb;EACF;AACF,CAAC;;AAED;AACA,MAAM+B,SAAS,GAAG;EAChB7C,QAAQ;EACRG,QAAQ;EACRG,WAAW;EACXE,eAAe;EACfQ,WAAW;EACXC,OAAO;EACPQ,aAAa;EACbG;AACF,CAAC;AAED,eAAeiB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}