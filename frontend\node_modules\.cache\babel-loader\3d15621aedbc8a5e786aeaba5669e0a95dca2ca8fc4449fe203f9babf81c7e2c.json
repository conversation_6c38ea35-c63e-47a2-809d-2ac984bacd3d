{"ast": null, "code": "import api from '../utils/axios';\nimport { prepareDropdownData } from '../utils/filters';\n\n// Suppliers\nexport const getSuppliers = async (params = {}, includeInactive = false) => {\n  try {\n    // If not explicitly requesting inactive items, only get active ones\n    if (!includeInactive && !params.is_active) {\n      params = {\n        ...params,\n        is_active: true\n      };\n    }\n    console.log('Fetching suppliers with params:', params);\n    const response = await api.get('/suppliers/', {\n      params\n    });\n    console.log('Suppliers fetched successfully:', response.data);\n\n    // Handle different response formats\n    let suppliersData;\n    if (Array.isArray(response.data)) {\n      suppliersData = response.data;\n    } else if (response.data.results && Array.isArray(response.data.results)) {\n      suppliersData = response.data.results;\n    } else if (response.data.data && Array.isArray(response.data.data)) {\n      suppliersData = response.data.data;\n    } else {\n      console.warn('Unexpected data format:', response.data);\n      suppliersData = [];\n    }\n    console.log('Processed suppliers data:', suppliersData);\n    return prepareDropdownData(suppliersData, includeInactive);\n  } catch (error) {\n    console.error('Failed to fetch suppliers:', error);\n    return []; // Return empty array instead of throwing to prevent UI errors\n  }\n};\nexport const getSupplier = async id => {\n  try {\n    // List of possible endpoints to try\n    const endpoints = [`/suppliers/${id}/`, `/api/suppliers/${id}/`, `/api/v1/suppliers/${id}/`, `/supplier/${id}/`, `/api/supplier/${id}/`, `/api/v1/supplier/${id}/`];\n\n    // Try each endpoint\n    for (const endpoint of endpoints) {\n      try {\n        console.log(`Trying to fetch supplier from ${endpoint}`);\n        const response = await api.get(endpoint);\n        console.log(`Supplier fetched successfully from ${endpoint}:`, response.data);\n        return response.data;\n      } catch (error) {\n        console.warn(`Error with ${endpoint} endpoint:`, error.message);\n        // Continue to the next endpoint\n      }\n    }\n\n    // If we get here, all endpoints failed\n    throw new Error(`All supplier endpoints failed for ID ${id}`);\n  } catch (error) {\n    console.error(`Error fetching supplier with ID ${id}:`, error);\n    throw error;\n  }\n};\nexport const createSupplier = async data => {\n  try {\n    // List of possible endpoints to try\n    const endpoints = ['/suppliers/', '/api/suppliers/', '/api/v1/suppliers/', '/supplier/', '/api/supplier/', '/api/v1/supplier/'];\n\n    // Try each endpoint\n    for (const endpoint of endpoints) {\n      try {\n        console.log(`Trying to create supplier at ${endpoint}`);\n        const response = await api.post(endpoint, data);\n        console.log(`Supplier created successfully at ${endpoint}:`, response.data);\n        return response.data;\n      } catch (error) {\n        console.warn(`Error with ${endpoint} endpoint:`, error.message);\n        // Continue to the next endpoint\n      }\n    }\n\n    // If we get here, all endpoints failed\n    throw new Error('All supplier creation endpoints failed');\n  } catch (error) {\n    console.error('Error creating supplier:', error);\n    throw error;\n  }\n};\nexport const updateSupplier = async (id, data) => {\n  try {\n    // List of possible endpoints to try\n    const endpoints = [`/suppliers/${id}/`, `/api/suppliers/${id}/`, `/api/v1/suppliers/${id}/`, `/supplier/${id}/`, `/api/supplier/${id}/`, `/api/v1/supplier/${id}/`];\n\n    // Try each endpoint\n    for (const endpoint of endpoints) {\n      try {\n        console.log(`Trying to update supplier at ${endpoint}`);\n        const response = await api.put(endpoint, data);\n        console.log(`Supplier updated successfully at ${endpoint}:`, response.data);\n        return response.data;\n      } catch (error) {\n        console.warn(`Error with ${endpoint} endpoint:`, error.message);\n        // Continue to the next endpoint\n      }\n    }\n\n    // If we get here, all endpoints failed\n    throw new Error(`All supplier update endpoints failed for ID ${id}`);\n  } catch (error) {\n    console.error(`Error updating supplier with ID ${id}:`, error);\n    throw error;\n  }\n};\nexport const deleteSupplier = async id => {\n  try {\n    // List of possible endpoints to try\n    const endpoints = [`/suppliers/${id}/`, `/api/suppliers/${id}/`, `/api/v1/suppliers/${id}/`, `/supplier/${id}/`, `/api/supplier/${id}/`, `/api/v1/supplier/${id}/`];\n\n    // Try each endpoint\n    for (const endpoint of endpoints) {\n      try {\n        console.log(`Trying to delete supplier at ${endpoint}`);\n        await api.delete(endpoint);\n        console.log(`Supplier deleted successfully at ${endpoint}`);\n        return true;\n      } catch (error) {\n        console.warn(`Error with ${endpoint} endpoint:`, error.message);\n        // Continue to the next endpoint\n      }\n    }\n\n    // If we get here, all endpoints failed\n    throw new Error(`All supplier deletion endpoints failed for ID ${id}`);\n  } catch (error) {\n    console.error(`Error deleting supplier with ID ${id}:`, error);\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["api", "prepareDropdownData", "getSuppliers", "params", "includeInactive", "is_active", "console", "log", "response", "get", "data", "suppliersData", "Array", "isArray", "results", "warn", "error", "getSupplier", "id", "endpoints", "endpoint", "message", "Error", "createSupplier", "post", "updateSupplier", "put", "deleteSupplier", "delete"], "sources": ["C:/Users/<USER>/Desktop/project ai/Store Management System/asset management/frontend/src/services/suppliers.js"], "sourcesContent": ["import api from '../utils/axios';\nimport { prepareDropdownData } from '../utils/filters';\n\n// Suppliers\nexport const getSuppliers = async (params = {}, includeInactive = false) => {\n  try {\n    // If not explicitly requesting inactive items, only get active ones\n    if (!includeInactive && !params.is_active) {\n      params = { ...params, is_active: true };\n    }\n\n    console.log('Fetching suppliers with params:', params);\n    const response = await api.get('/suppliers/', { params });\n    console.log('Suppliers fetched successfully:', response.data);\n\n    // Handle different response formats\n    let suppliersData;\n    if (Array.isArray(response.data)) {\n      suppliersData = response.data;\n    } else if (response.data.results && Array.isArray(response.data.results)) {\n      suppliersData = response.data.results;\n    } else if (response.data.data && Array.isArray(response.data.data)) {\n      suppliersData = response.data.data;\n    } else {\n      console.warn('Unexpected data format:', response.data);\n      suppliersData = [];\n    }\n\n    console.log('Processed suppliers data:', suppliersData);\n    return prepareDropdownData(suppliersData, includeInactive);\n  } catch (error) {\n    console.error('Failed to fetch suppliers:', error);\n    return []; // Return empty array instead of throwing to prevent UI errors\n  }\n};\n\nexport const getSupplier = async (id) => {\n  try {\n    // List of possible endpoints to try\n    const endpoints = [\n      `/suppliers/${id}/`,\n      `/api/suppliers/${id}/`,\n      `/api/v1/suppliers/${id}/`,\n      `/supplier/${id}/`,\n      `/api/supplier/${id}/`,\n      `/api/v1/supplier/${id}/`\n    ];\n\n    // Try each endpoint\n    for (const endpoint of endpoints) {\n      try {\n        console.log(`Trying to fetch supplier from ${endpoint}`);\n        const response = await api.get(endpoint);\n        console.log(`Supplier fetched successfully from ${endpoint}:`, response.data);\n        return response.data;\n      } catch (error) {\n        console.warn(`Error with ${endpoint} endpoint:`, error.message);\n        // Continue to the next endpoint\n      }\n    }\n\n    // If we get here, all endpoints failed\n    throw new Error(`All supplier endpoints failed for ID ${id}`);\n  } catch (error) {\n    console.error(`Error fetching supplier with ID ${id}:`, error);\n    throw error;\n  }\n};\n\nexport const createSupplier = async (data) => {\n  try {\n    // List of possible endpoints to try\n    const endpoints = [\n      '/suppliers/',\n      '/api/suppliers/',\n      '/api/v1/suppliers/',\n      '/supplier/',\n      '/api/supplier/',\n      '/api/v1/supplier/'\n    ];\n\n    // Try each endpoint\n    for (const endpoint of endpoints) {\n      try {\n        console.log(`Trying to create supplier at ${endpoint}`);\n        const response = await api.post(endpoint, data);\n        console.log(`Supplier created successfully at ${endpoint}:`, response.data);\n        return response.data;\n      } catch (error) {\n        console.warn(`Error with ${endpoint} endpoint:`, error.message);\n        // Continue to the next endpoint\n      }\n    }\n\n    // If we get here, all endpoints failed\n    throw new Error('All supplier creation endpoints failed');\n  } catch (error) {\n    console.error('Error creating supplier:', error);\n    throw error;\n  }\n};\n\nexport const updateSupplier = async (id, data) => {\n  try {\n    // List of possible endpoints to try\n    const endpoints = [\n      `/suppliers/${id}/`,\n      `/api/suppliers/${id}/`,\n      `/api/v1/suppliers/${id}/`,\n      `/supplier/${id}/`,\n      `/api/supplier/${id}/`,\n      `/api/v1/supplier/${id}/`\n    ];\n\n    // Try each endpoint\n    for (const endpoint of endpoints) {\n      try {\n        console.log(`Trying to update supplier at ${endpoint}`);\n        const response = await api.put(endpoint, data);\n        console.log(`Supplier updated successfully at ${endpoint}:`, response.data);\n        return response.data;\n      } catch (error) {\n        console.warn(`Error with ${endpoint} endpoint:`, error.message);\n        // Continue to the next endpoint\n      }\n    }\n\n    // If we get here, all endpoints failed\n    throw new Error(`All supplier update endpoints failed for ID ${id}`);\n  } catch (error) {\n    console.error(`Error updating supplier with ID ${id}:`, error);\n    throw error;\n  }\n};\n\nexport const deleteSupplier = async (id) => {\n  try {\n    // List of possible endpoints to try\n    const endpoints = [\n      `/suppliers/${id}/`,\n      `/api/suppliers/${id}/`,\n      `/api/v1/suppliers/${id}/`,\n      `/supplier/${id}/`,\n      `/api/supplier/${id}/`,\n      `/api/v1/supplier/${id}/`\n    ];\n\n    // Try each endpoint\n    for (const endpoint of endpoints) {\n      try {\n        console.log(`Trying to delete supplier at ${endpoint}`);\n        await api.delete(endpoint);\n        console.log(`Supplier deleted successfully at ${endpoint}`);\n        return true;\n      } catch (error) {\n        console.warn(`Error with ${endpoint} endpoint:`, error.message);\n        // Continue to the next endpoint\n      }\n    }\n\n    // If we get here, all endpoints failed\n    throw new Error(`All supplier deletion endpoints failed for ID ${id}`);\n  } catch (error) {\n    console.error(`Error deleting supplier with ID ${id}:`, error);\n    throw error;\n  }\n};\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,gBAAgB;AAChC,SAASC,mBAAmB,QAAQ,kBAAkB;;AAEtD;AACA,OAAO,MAAMC,YAAY,GAAG,MAAAA,CAAOC,MAAM,GAAG,CAAC,CAAC,EAAEC,eAAe,GAAG,KAAK,KAAK;EAC1E,IAAI;IACF;IACA,IAAI,CAACA,eAAe,IAAI,CAACD,MAAM,CAACE,SAAS,EAAE;MACzCF,MAAM,GAAG;QAAE,GAAGA,MAAM;QAAEE,SAAS,EAAE;MAAK,CAAC;IACzC;IAEAC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEJ,MAAM,CAAC;IACtD,MAAMK,QAAQ,GAAG,MAAMR,GAAG,CAACS,GAAG,CAAC,aAAa,EAAE;MAAEN;IAAO,CAAC,CAAC;IACzDG,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEC,QAAQ,CAACE,IAAI,CAAC;;IAE7D;IACA,IAAIC,aAAa;IACjB,IAAIC,KAAK,CAACC,OAAO,CAACL,QAAQ,CAACE,IAAI,CAAC,EAAE;MAChCC,aAAa,GAAGH,QAAQ,CAACE,IAAI;IAC/B,CAAC,MAAM,IAAIF,QAAQ,CAACE,IAAI,CAACI,OAAO,IAAIF,KAAK,CAACC,OAAO,CAACL,QAAQ,CAACE,IAAI,CAACI,OAAO,CAAC,EAAE;MACxEH,aAAa,GAAGH,QAAQ,CAACE,IAAI,CAACI,OAAO;IACvC,CAAC,MAAM,IAAIN,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAIE,KAAK,CAACC,OAAO,CAACL,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC,EAAE;MAClEC,aAAa,GAAGH,QAAQ,CAACE,IAAI,CAACA,IAAI;IACpC,CAAC,MAAM;MACLJ,OAAO,CAACS,IAAI,CAAC,yBAAyB,EAAEP,QAAQ,CAACE,IAAI,CAAC;MACtDC,aAAa,GAAG,EAAE;IACpB;IAEAL,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEI,aAAa,CAAC;IACvD,OAAOV,mBAAmB,CAACU,aAAa,EAAEP,eAAe,CAAC;EAC5D,CAAC,CAAC,OAAOY,KAAK,EAAE;IACdV,OAAO,CAACU,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IAClD,OAAO,EAAE,CAAC,CAAC;EACb;AACF,CAAC;AAED,OAAO,MAAMC,WAAW,GAAG,MAAOC,EAAE,IAAK;EACvC,IAAI;IACF;IACA,MAAMC,SAAS,GAAG,CAChB,cAAcD,EAAE,GAAG,EACnB,kBAAkBA,EAAE,GAAG,EACvB,qBAAqBA,EAAE,GAAG,EAC1B,aAAaA,EAAE,GAAG,EAClB,iBAAiBA,EAAE,GAAG,EACtB,oBAAoBA,EAAE,GAAG,CAC1B;;IAED;IACA,KAAK,MAAME,QAAQ,IAAID,SAAS,EAAE;MAChC,IAAI;QACFb,OAAO,CAACC,GAAG,CAAC,iCAAiCa,QAAQ,EAAE,CAAC;QACxD,MAAMZ,QAAQ,GAAG,MAAMR,GAAG,CAACS,GAAG,CAACW,QAAQ,CAAC;QACxCd,OAAO,CAACC,GAAG,CAAC,sCAAsCa,QAAQ,GAAG,EAAEZ,QAAQ,CAACE,IAAI,CAAC;QAC7E,OAAOF,QAAQ,CAACE,IAAI;MACtB,CAAC,CAAC,OAAOM,KAAK,EAAE;QACdV,OAAO,CAACS,IAAI,CAAC,cAAcK,QAAQ,YAAY,EAAEJ,KAAK,CAACK,OAAO,CAAC;QAC/D;MACF;IACF;;IAEA;IACA,MAAM,IAAIC,KAAK,CAAC,wCAAwCJ,EAAE,EAAE,CAAC;EAC/D,CAAC,CAAC,OAAOF,KAAK,EAAE;IACdV,OAAO,CAACU,KAAK,CAAC,mCAAmCE,EAAE,GAAG,EAAEF,KAAK,CAAC;IAC9D,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMO,cAAc,GAAG,MAAOb,IAAI,IAAK;EAC5C,IAAI;IACF;IACA,MAAMS,SAAS,GAAG,CAChB,aAAa,EACb,iBAAiB,EACjB,oBAAoB,EACpB,YAAY,EACZ,gBAAgB,EAChB,mBAAmB,CACpB;;IAED;IACA,KAAK,MAAMC,QAAQ,IAAID,SAAS,EAAE;MAChC,IAAI;QACFb,OAAO,CAACC,GAAG,CAAC,gCAAgCa,QAAQ,EAAE,CAAC;QACvD,MAAMZ,QAAQ,GAAG,MAAMR,GAAG,CAACwB,IAAI,CAACJ,QAAQ,EAAEV,IAAI,CAAC;QAC/CJ,OAAO,CAACC,GAAG,CAAC,oCAAoCa,QAAQ,GAAG,EAAEZ,QAAQ,CAACE,IAAI,CAAC;QAC3E,OAAOF,QAAQ,CAACE,IAAI;MACtB,CAAC,CAAC,OAAOM,KAAK,EAAE;QACdV,OAAO,CAACS,IAAI,CAAC,cAAcK,QAAQ,YAAY,EAAEJ,KAAK,CAACK,OAAO,CAAC;QAC/D;MACF;IACF;;IAEA;IACA,MAAM,IAAIC,KAAK,CAAC,wCAAwC,CAAC;EAC3D,CAAC,CAAC,OAAON,KAAK,EAAE;IACdV,OAAO,CAACU,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAChD,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMS,cAAc,GAAG,MAAAA,CAAOP,EAAE,EAAER,IAAI,KAAK;EAChD,IAAI;IACF;IACA,MAAMS,SAAS,GAAG,CAChB,cAAcD,EAAE,GAAG,EACnB,kBAAkBA,EAAE,GAAG,EACvB,qBAAqBA,EAAE,GAAG,EAC1B,aAAaA,EAAE,GAAG,EAClB,iBAAiBA,EAAE,GAAG,EACtB,oBAAoBA,EAAE,GAAG,CAC1B;;IAED;IACA,KAAK,MAAME,QAAQ,IAAID,SAAS,EAAE;MAChC,IAAI;QACFb,OAAO,CAACC,GAAG,CAAC,gCAAgCa,QAAQ,EAAE,CAAC;QACvD,MAAMZ,QAAQ,GAAG,MAAMR,GAAG,CAAC0B,GAAG,CAACN,QAAQ,EAAEV,IAAI,CAAC;QAC9CJ,OAAO,CAACC,GAAG,CAAC,oCAAoCa,QAAQ,GAAG,EAAEZ,QAAQ,CAACE,IAAI,CAAC;QAC3E,OAAOF,QAAQ,CAACE,IAAI;MACtB,CAAC,CAAC,OAAOM,KAAK,EAAE;QACdV,OAAO,CAACS,IAAI,CAAC,cAAcK,QAAQ,YAAY,EAAEJ,KAAK,CAACK,OAAO,CAAC;QAC/D;MACF;IACF;;IAEA;IACA,MAAM,IAAIC,KAAK,CAAC,+CAA+CJ,EAAE,EAAE,CAAC;EACtE,CAAC,CAAC,OAAOF,KAAK,EAAE;IACdV,OAAO,CAACU,KAAK,CAAC,mCAAmCE,EAAE,GAAG,EAAEF,KAAK,CAAC;IAC9D,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMW,cAAc,GAAG,MAAOT,EAAE,IAAK;EAC1C,IAAI;IACF;IACA,MAAMC,SAAS,GAAG,CAChB,cAAcD,EAAE,GAAG,EACnB,kBAAkBA,EAAE,GAAG,EACvB,qBAAqBA,EAAE,GAAG,EAC1B,aAAaA,EAAE,GAAG,EAClB,iBAAiBA,EAAE,GAAG,EACtB,oBAAoBA,EAAE,GAAG,CAC1B;;IAED;IACA,KAAK,MAAME,QAAQ,IAAID,SAAS,EAAE;MAChC,IAAI;QACFb,OAAO,CAACC,GAAG,CAAC,gCAAgCa,QAAQ,EAAE,CAAC;QACvD,MAAMpB,GAAG,CAAC4B,MAAM,CAACR,QAAQ,CAAC;QAC1Bd,OAAO,CAACC,GAAG,CAAC,oCAAoCa,QAAQ,EAAE,CAAC;QAC3D,OAAO,IAAI;MACb,CAAC,CAAC,OAAOJ,KAAK,EAAE;QACdV,OAAO,CAACS,IAAI,CAAC,cAAcK,QAAQ,YAAY,EAAEJ,KAAK,CAACK,OAAO,CAAC;QAC/D;MACF;IACF;;IAEA;IACA,MAAM,IAAIC,KAAK,CAAC,iDAAiDJ,EAAE,EAAE,CAAC;EACxE,CAAC,CAAC,OAAOF,KAAK,EAAE;IACdV,OAAO,CAACU,KAAK,CAAC,mCAAmCE,EAAE,GAAG,EAAEF,KAAK,CAAC;IAC9D,MAAMA,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}