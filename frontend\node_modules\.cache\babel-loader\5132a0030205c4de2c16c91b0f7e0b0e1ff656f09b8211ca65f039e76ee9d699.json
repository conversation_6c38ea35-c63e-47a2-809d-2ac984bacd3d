{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project ai\\\\Store Management System\\\\asset management\\\\frontend\\\\src\\\\components\\\\DashboardBanner.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Grid, Chip, useTheme, alpha, Stack } from '@mui/material';\nimport { Inventory as InventoryIcon, Business as BusinessIcon, TrendingUp as TrendingUpIcon, Security as SecurityIcon, School as SchoolIcon, Phone as PhoneIcon, Email as EmailIcon, Language as LanguageIcon } from '@mui/icons-material';\nimport { getMainOrganization } from '../services/organizations';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DashboardBanner = () => {\n  _s();\n  const theme = useTheme();\n  const [organization, setOrganization] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Get user data from localStorage instead of Redux to avoid context issues\n  const getUserData = () => {\n    try {\n      const userData = localStorage.getItem('user');\n      return userData ? JSON.parse(userData) : null;\n    } catch (error) {\n      console.error('Error parsing user data:', error);\n      return null;\n    }\n  };\n  const user = getUserData();\n\n  // Fetch organization data\n  useEffect(() => {\n    const fetchOrganization = async () => {\n      try {\n        setLoading(true);\n        const orgData = await getMainOrganization();\n        setOrganization(orgData);\n      } catch (error) {\n        console.error('Error fetching organization:', error);\n        // Fallback to University of Gondar data if API fails\n        setOrganization({\n          name: 'University of Gondar',\n          motto: 'Stock Management System',\n          logo_url: '/assets/images/uog-logo.png',\n          website: '',\n          phone: '',\n          email: ''\n        });\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchOrganization();\n  }, []);\n  const bannerStyle = {\n    background: `linear-gradient(135deg,\n      ${theme.palette.primary.main} 0%,\n      ${theme.palette.primary.dark} 50%,\n      ${theme.palette.secondary.main} 100%)`,\n    color: 'white',\n    borderRadius: theme.spacing(2),\n    overflow: 'hidden',\n    position: 'relative',\n    minHeight: '200px',\n    '&::before': {\n      content: '\"\"',\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      background: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`\n    }\n  };\n  const logoStyle = {\n    width: 'auto',\n    height: 80,\n    maxWidth: 120,\n    borderRadius: 0,\n    // Remove circular shape\n    border: 'none',\n    // Remove border\n    backgroundColor: 'transparent',\n    // Make background transparent\n    backdropFilter: 'none',\n    objectFit: 'contain' // Maintain aspect ratio\n  };\n  const statsCardStyle = {\n    backgroundColor: alpha('#ffffff', 0.15),\n    backdropFilter: 'blur(10px)',\n    border: `1px solid ${alpha('#ffffff', 0.2)}`,\n    borderRadius: theme.spacing(1.5),\n    padding: theme.spacing(2),\n    textAlign: 'center',\n    transition: 'all 0.3s ease',\n    '&:hover': {\n      backgroundColor: alpha('#ffffff', 0.25),\n      transform: 'translateY(-2px)'\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Paper, {\n    sx: bannerStyle,\n    elevation: 0,\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'relative',\n        zIndex: 1,\n        p: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        alignItems: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 8,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              component: \"img\",\n              src: (organization === null || organization === void 0 ? void 0 : organization.logo_url) || '/assets/images/uog-logo.png',\n              alt: (organization === null || organization === void 0 ? void 0 : organization.name) || 'Organization',\n              sx: logoStyle,\n              onError: e => {\n                // Fallback to icon if image fails to load\n                e.target.style.display = 'none';\n                e.target.nextSibling.style.display = 'block';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(BusinessIcon, {\n              sx: {\n                fontSize: 60,\n                color: alpha('#ffffff', 0.8),\n                display: 'none' // Hidden by default, shown if image fails\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                ml: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                component: \"h1\",\n                sx: {\n                  fontWeight: 700,\n                  mb: 0.5,\n                  textShadow: '0 2px 4px rgba(0,0,0,0.3)',\n                  fontSize: {\n                    xs: '1.5rem',\n                    md: '2.125rem'\n                  }\n                },\n                children: (organization === null || organization === void 0 ? void 0 : organization.name) || 'University of Gondar'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 500,\n                  opacity: 0.9,\n                  fontSize: {\n                    xs: '1rem',\n                    md: '1.25rem'\n                  }\n                },\n                children: (organization === null || organization === void 0 ? void 0 : organization.motto) || 'Stock Management System'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mt: 1,\n                  display: 'flex',\n                  flexWrap: 'wrap',\n                  gap: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Chip, {\n                  label: \"Professional\",\n                  size: \"small\",\n                  sx: {\n                    backgroundColor: alpha('#ffffff', 0.2),\n                    color: 'white',\n                    fontWeight: 500\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: \"Secure\",\n                  size: \"small\",\n                  sx: {\n                    backgroundColor: alpha('#ffffff', 0.2),\n                    color: 'white',\n                    fontWeight: 500\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: \"Efficient\",\n                  size: \"small\",\n                  sx: {\n                    backgroundColor: alpha('#ffffff', 0.2),\n                    color: 'white',\n                    fontWeight: 500\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                fontWeight: 500,\n                mb: 0.5\n              },\n              children: [\"Welcome back, \", (user === null || user === void 0 ? void 0 : user.first_name) || (user === null || user === void 0 ? void 0 : user.username) || 'User', \"!\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              sx: {\n                opacity: 0.8,\n                mb: 2\n              },\n              children: \"Manage your inventory with confidence and precision\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this), organization && (organization.website || organization.phone || organization.email) && /*#__PURE__*/_jsxDEV(Stack, {\n              direction: \"row\",\n              spacing: 3,\n              sx: {\n                flexWrap: 'wrap',\n                gap: 1\n              },\n              children: [organization.website && /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(LanguageIcon, {\n                  sx: {\n                    fontSize: 18,\n                    opacity: 0.8\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  component: \"a\",\n                  href: organization.website,\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  sx: {\n                    color: 'white',\n                    textDecoration: 'underline',\n                    fontSize: '0.875rem',\n                    opacity: 0.9,\n                    '&:hover': {\n                      opacity: 1\n                    }\n                  },\n                  children: organization.website\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 21\n              }, this), organization.phone && /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(PhoneIcon, {\n                  sx: {\n                    fontSize: 18,\n                    opacity: 0.8\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  sx: {\n                    fontSize: '0.875rem',\n                    opacity: 0.9\n                  },\n                  children: organization.phone\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 21\n              }, this), organization.email && /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(EmailIcon, {\n                  sx: {\n                    fontSize: 18,\n                    opacity: 0.8\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  component: \"a\",\n                  href: `mailto:${organization.email}`,\n                  sx: {\n                    color: 'white',\n                    textDecoration: 'underline',\n                    fontSize: '0.875rem',\n                    opacity: 0.9,\n                    '&:hover': {\n                      opacity: 1\n                    }\n                  },\n                  children: organization.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 6,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: statsCardStyle,\n                children: [/*#__PURE__*/_jsxDEV(InventoryIcon, {\n                  sx: {\n                    fontSize: 32,\n                    mb: 1,\n                    opacity: 0.9\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 0.5\n                  },\n                  children: \"Active\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    opacity: 0.8\n                  },\n                  children: \"Inventory\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 6,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: statsCardStyle,\n                children: [/*#__PURE__*/_jsxDEV(SecurityIcon, {\n                  sx: {\n                    fontSize: 32,\n                    mb: 1,\n                    opacity: 0.9\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 0.5\n                  },\n                  children: \"Secure\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    opacity: 0.8\n                  },\n                  children: \"System\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 6,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: statsCardStyle,\n                children: [/*#__PURE__*/_jsxDEV(TrendingUpIcon, {\n                  sx: {\n                    fontSize: 32,\n                    mb: 1,\n                    opacity: 0.9\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 0.5\n                  },\n                  children: \"Growth\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    opacity: 0.8\n                  },\n                  children: \"Tracking\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 6,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: statsCardStyle,\n                children: [/*#__PURE__*/_jsxDEV(BusinessIcon, {\n                  sx: {\n                    fontSize: 32,\n                    mb: 1,\n                    opacity: 0.9\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 0.5\n                  },\n                  children: \"Enterprise\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    opacity: 0.8\n                  },\n                  children: \"Grade\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 115,\n    columnNumber: 5\n  }, this);\n};\n_s(DashboardBanner, \"vpisiGziSPtiEOvxr/sft50M7RY=\", false, function () {\n  return [useTheme];\n});\n_c = DashboardBanner;\nexport default DashboardBanner;\nvar _c;\n$RefreshReg$(_c, \"DashboardBanner\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "Grid", "Chip", "useTheme", "alpha", "<PERSON><PERSON>", "Inventory", "InventoryIcon", "Business", "BusinessIcon", "TrendingUp", "TrendingUpIcon", "Security", "SecurityIcon", "School", "SchoolIcon", "Phone", "PhoneIcon", "Email", "EmailIcon", "Language", "LanguageIcon", "getMainOrganization", "jsxDEV", "_jsxDEV", "DashboardBanner", "_s", "theme", "organization", "setOrganization", "loading", "setLoading", "getUserData", "userData", "localStorage", "getItem", "JSON", "parse", "error", "console", "user", "fetchOrganization", "orgData", "name", "motto", "logo_url", "website", "phone", "email", "bannerStyle", "background", "palette", "primary", "main", "dark", "secondary", "color", "borderRadius", "spacing", "overflow", "position", "minHeight", "content", "top", "left", "right", "bottom", "logoStyle", "width", "height", "max<PERSON><PERSON><PERSON>", "border", "backgroundColor", "<PERSON><PERSON>ilter", "objectFit", "statsCardStyle", "padding", "textAlign", "transition", "transform", "sx", "elevation", "children", "zIndex", "p", "container", "alignItems", "item", "xs", "md", "display", "mb", "component", "src", "alt", "onError", "e", "target", "style", "nextS<PERSON>ling", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "ml", "variant", "fontWeight", "textShadow", "opacity", "mt", "flexWrap", "gap", "label", "size", "first_name", "username", "direction", "href", "rel", "textDecoration", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/project ai/Store Management System/asset management/frontend/src/components/DashboardBanner.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Grid,\n  Chip,\n  useTheme,\n  alpha,\n  Stack,\n} from '@mui/material';\nimport {\n  Inventory as InventoryIcon,\n  Business as BusinessIcon,\n  TrendingUp as TrendingUpIcon,\n  Security as SecurityIcon,\n  School as SchoolIcon,\n  Phone as PhoneIcon,\n  Email as EmailIcon,\n  Language as LanguageIcon,\n} from '@mui/icons-material';\nimport { getMainOrganization } from '../services/organizations';\n\nconst DashboardBanner = () => {\n  const theme = useTheme();\n  const [organization, setOrganization] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Get user data from localStorage instead of Redux to avoid context issues\n  const getUserData = () => {\n    try {\n      const userData = localStorage.getItem('user');\n      return userData ? JSON.parse(userData) : null;\n    } catch (error) {\n      console.error('Error parsing user data:', error);\n      return null;\n    }\n  };\n\n  const user = getUserData();\n\n  // Fetch organization data\n  useEffect(() => {\n    const fetchOrganization = async () => {\n      try {\n        setLoading(true);\n        const orgData = await getMainOrganization();\n        setOrganization(orgData);\n      } catch (error) {\n        console.error('Error fetching organization:', error);\n        // Fallback to University of Gondar data if API fails\n        setOrganization({\n          name: 'University of Gondar',\n          motto: 'Stock Management System',\n          logo_url: '/assets/images/uog-logo.png',\n          website: '',\n          phone: '',\n          email: ''\n        });\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchOrganization();\n  }, []);\n\n  const bannerStyle = {\n    background: `linear-gradient(135deg,\n      ${theme.palette.primary.main} 0%,\n      ${theme.palette.primary.dark} 50%,\n      ${theme.palette.secondary.main} 100%)`,\n    color: 'white',\n    borderRadius: theme.spacing(2),\n    overflow: 'hidden',\n    position: 'relative',\n    minHeight: '200px',\n    '&::before': {\n      content: '\"\"',\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      background: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`,\n    },\n  };\n\n  const logoStyle = {\n    width: 'auto',\n    height: 80,\n    maxWidth: 120,\n    borderRadius: 0, // Remove circular shape\n    border: 'none', // Remove border\n    backgroundColor: 'transparent', // Make background transparent\n    backdropFilter: 'none',\n    objectFit: 'contain', // Maintain aspect ratio\n  };\n\n  const statsCardStyle = {\n    backgroundColor: alpha('#ffffff', 0.15),\n    backdropFilter: 'blur(10px)',\n    border: `1px solid ${alpha('#ffffff', 0.2)}`,\n    borderRadius: theme.spacing(1.5),\n    padding: theme.spacing(2),\n    textAlign: 'center',\n    transition: 'all 0.3s ease',\n    '&:hover': {\n      backgroundColor: alpha('#ffffff', 0.25),\n      transform: 'translateY(-2px)',\n    },\n  };\n\n  return (\n    <Paper sx={bannerStyle} elevation={0}>\n      <Box sx={{ position: 'relative', zIndex: 1, p: 4 }}>\n        <Grid container spacing={3} alignItems=\"center\">\n          {/* Logo and Title Section */}\n          <Grid item xs={12} md={8}>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n              <Box\n                component=\"img\"\n                src={organization?.logo_url || '/assets/images/uog-logo.png'}\n                alt={organization?.name || 'Organization'}\n                sx={logoStyle}\n                onError={(e) => {\n                  // Fallback to icon if image fails to load\n                  e.target.style.display = 'none';\n                  e.target.nextSibling.style.display = 'block';\n                }}\n              />\n              <BusinessIcon\n                sx={{\n                  fontSize: 60,\n                  color: alpha('#ffffff', 0.8),\n                  display: 'none' // Hidden by default, shown if image fails\n                }}\n              />\n              <Box sx={{ ml: 3 }}>\n                <Typography\n                  variant=\"h4\"\n                  component=\"h1\"\n                  sx={{\n                    fontWeight: 700,\n                    mb: 0.5,\n                    textShadow: '0 2px 4px rgba(0,0,0,0.3)',\n                    fontSize: { xs: '1.5rem', md: '2.125rem' },\n                  }}\n                >\n                  {organization?.name || 'University of Gondar'}\n                </Typography>\n                <Typography\n                  variant=\"h6\"\n                  sx={{\n                    fontWeight: 500,\n                    opacity: 0.9,\n                    fontSize: { xs: '1rem', md: '1.25rem' },\n                  }}\n                >\n                  {organization?.motto || 'Stock Management System'}\n                </Typography>\n                <Box sx={{ mt: 1, display: 'flex', flexWrap: 'wrap', gap: 1 }}>\n                  <Chip\n                    label=\"Professional\"\n                    size=\"small\"\n                    sx={{\n                      backgroundColor: alpha('#ffffff', 0.2),\n                      color: 'white',\n                      fontWeight: 500,\n                    }}\n                  />\n                  <Chip\n                    label=\"Secure\"\n                    size=\"small\"\n                    sx={{\n                      backgroundColor: alpha('#ffffff', 0.2),\n                      color: 'white',\n                      fontWeight: 500,\n                    }}\n                  />\n                  <Chip\n                    label=\"Efficient\"\n                    size=\"small\"\n                    sx={{\n                      backgroundColor: alpha('#ffffff', 0.2),\n                      color: 'white',\n                      fontWeight: 500,\n                    }}\n                  />\n                </Box>\n              </Box>\n            </Box>\n\n            {/* Welcome Message */}\n            <Box sx={{ mt: 2 }}>\n              <Typography variant=\"h6\" sx={{ fontWeight: 500, mb: 0.5 }}>\n                Welcome back, {user?.first_name || user?.username || 'User'}!\n              </Typography>\n              <Typography variant=\"body1\" sx={{ opacity: 0.8, mb: 2 }}>\n                Manage your inventory with confidence and precision\n              </Typography>\n\n              {/* Organization Contact Information */}\n              {organization && (organization.website || organization.phone || organization.email) && (\n                <Stack direction=\"row\" spacing={3} sx={{ flexWrap: 'wrap', gap: 1 }}>\n                  {organization.website && (\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                      <LanguageIcon sx={{ fontSize: 18, opacity: 0.8 }} />\n                      <Typography\n                        component=\"a\"\n                        href={organization.website}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        sx={{\n                          color: 'white',\n                          textDecoration: 'underline',\n                          fontSize: '0.875rem',\n                          opacity: 0.9,\n                          '&:hover': { opacity: 1 }\n                        }}\n                      >\n                        {organization.website}\n                      </Typography>\n                    </Box>\n                  )}\n                  {organization.phone && (\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                      <PhoneIcon sx={{ fontSize: 18, opacity: 0.8 }} />\n                      <Typography sx={{ fontSize: '0.875rem', opacity: 0.9 }}>\n                        {organization.phone}\n                      </Typography>\n                    </Box>\n                  )}\n                  {organization.email && (\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                      <EmailIcon sx={{ fontSize: 18, opacity: 0.8 }} />\n                      <Typography\n                        component=\"a\"\n                        href={`mailto:${organization.email}`}\n                        sx={{\n                          color: 'white',\n                          textDecoration: 'underline',\n                          fontSize: '0.875rem',\n                          opacity: 0.9,\n                          '&:hover': { opacity: 1 }\n                        }}\n                      >\n                        {organization.email}\n                      </Typography>\n                    </Box>\n                  )}\n                </Stack>\n              )}\n            </Box>\n          </Grid>\n\n          {/* Quick Stats Section */}\n          <Grid item xs={12} md={4}>\n            <Grid container spacing={2}>\n              <Grid item xs={6}>\n                <Box sx={statsCardStyle}>\n                  <InventoryIcon sx={{ fontSize: 32, mb: 1, opacity: 0.9 }} />\n                  <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 0.5 }}>\n                    Active\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ opacity: 0.8 }}>\n                    Inventory\n                  </Typography>\n                </Box>\n              </Grid>\n              <Grid item xs={6}>\n                <Box sx={statsCardStyle}>\n                  <SecurityIcon sx={{ fontSize: 32, mb: 1, opacity: 0.9 }} />\n                  <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 0.5 }}>\n                    Secure\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ opacity: 0.8 }}>\n                    System\n                  </Typography>\n                </Box>\n              </Grid>\n              <Grid item xs={6}>\n                <Box sx={statsCardStyle}>\n                  <TrendingUpIcon sx={{ fontSize: 32, mb: 1, opacity: 0.9 }} />\n                  <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 0.5 }}>\n                    Growth\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ opacity: 0.8 }}>\n                    Tracking\n                  </Typography>\n                </Box>\n              </Grid>\n              <Grid item xs={6}>\n                <Box sx={statsCardStyle}>\n                  <BusinessIcon sx={{ fontSize: 32, mb: 1, opacity: 0.9 }} />\n                  <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 0.5 }}>\n                    Enterprise\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ opacity: 0.8 }}>\n                    Grade\n                  </Typography>\n                </Box>\n              </Grid>\n            </Grid>\n          </Grid>\n        </Grid>\n      </Box>\n    </Paper>\n  );\n};\n\nexport default DashboardBanner;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,KAAK,EACLC,KAAK,QACA,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,EAC5BC,QAAQ,IAAIC,YAAY,EACxBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAC5B,SAASC,mBAAmB,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,KAAK,GAAGxB,QAAQ,CAAC,CAAC;EACxB,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAMoC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI;MACF,MAAMC,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC7C,OAAOF,QAAQ,GAAGG,IAAI,CAACC,KAAK,CAACJ,QAAQ,CAAC,GAAG,IAAI;IAC/C,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO,IAAI;IACb;EACF,CAAC;EAED,MAAME,IAAI,GAAGR,WAAW,CAAC,CAAC;;EAE1B;EACAnC,SAAS,CAAC,MAAM;IACd,MAAM4C,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACpC,IAAI;QACFV,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMW,OAAO,GAAG,MAAMpB,mBAAmB,CAAC,CAAC;QAC3CO,eAAe,CAACa,OAAO,CAAC;MAC1B,CAAC,CAAC,OAAOJ,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD;QACAT,eAAe,CAAC;UACdc,IAAI,EAAE,sBAAsB;UAC5BC,KAAK,EAAE,yBAAyB;UAChCC,QAAQ,EAAE,6BAA6B;UACvCC,OAAO,EAAE,EAAE;UACXC,KAAK,EAAE,EAAE;UACTC,KAAK,EAAE;QACT,CAAC,CAAC;MACJ,CAAC,SAAS;QACRjB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDU,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,WAAW,GAAG;IAClBC,UAAU,EAAE;AAChB,QAAQvB,KAAK,CAACwB,OAAO,CAACC,OAAO,CAACC,IAAI;AAClC,QAAQ1B,KAAK,CAACwB,OAAO,CAACC,OAAO,CAACE,IAAI;AAClC,QAAQ3B,KAAK,CAACwB,OAAO,CAACI,SAAS,CAACF,IAAI,QAAQ;IACxCG,KAAK,EAAE,OAAO;IACdC,YAAY,EAAE9B,KAAK,CAAC+B,OAAO,CAAC,CAAC,CAAC;IAC9BC,QAAQ,EAAE,QAAQ;IAClBC,QAAQ,EAAE,UAAU;IACpBC,SAAS,EAAE,OAAO;IAClB,WAAW,EAAE;MACXC,OAAO,EAAE,IAAI;MACbF,QAAQ,EAAE,UAAU;MACpBG,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACThB,UAAU,EAAE;IACd;EACF,CAAC;EAED,MAAMiB,SAAS,GAAG;IAChBC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,GAAG;IACbb,YAAY,EAAE,CAAC;IAAE;IACjBc,MAAM,EAAE,MAAM;IAAE;IAChBC,eAAe,EAAE,aAAa;IAAE;IAChCC,cAAc,EAAE,MAAM;IACtBC,SAAS,EAAE,SAAS,CAAE;EACxB,CAAC;EAED,MAAMC,cAAc,GAAG;IACrBH,eAAe,EAAEpE,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC;IACvCqE,cAAc,EAAE,YAAY;IAC5BF,MAAM,EAAE,aAAanE,KAAK,CAAC,SAAS,EAAE,GAAG,CAAC,EAAE;IAC5CqD,YAAY,EAAE9B,KAAK,CAAC+B,OAAO,CAAC,GAAG,CAAC;IAChCkB,OAAO,EAAEjD,KAAK,CAAC+B,OAAO,CAAC,CAAC,CAAC;IACzBmB,SAAS,EAAE,QAAQ;IACnBC,UAAU,EAAE,eAAe;IAC3B,SAAS,EAAE;MACTN,eAAe,EAAEpE,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC;MACvC2E,SAAS,EAAE;IACb;EACF,CAAC;EAED,oBACEvD,OAAA,CAACxB,KAAK;IAACgF,EAAE,EAAE/B,WAAY;IAACgC,SAAS,EAAE,CAAE;IAAAC,QAAA,eACnC1D,OAAA,CAAC1B,GAAG;MAACkF,EAAE,EAAE;QAAEpB,QAAQ,EAAE,UAAU;QAAEuB,MAAM,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAF,QAAA,eACjD1D,OAAA,CAACvB,IAAI;QAACoF,SAAS;QAAC3B,OAAO,EAAE,CAAE;QAAC4B,UAAU,EAAC,QAAQ;QAAAJ,QAAA,gBAE7C1D,OAAA,CAACvB,IAAI;UAACsF,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAP,QAAA,gBACvB1D,OAAA,CAAC1B,GAAG;YAACkF,EAAE,EAAE;cAAEU,OAAO,EAAE,MAAM;cAAEJ,UAAU,EAAE,QAAQ;cAAEK,EAAE,EAAE;YAAE,CAAE;YAAAT,QAAA,gBACxD1D,OAAA,CAAC1B,GAAG;cACF8F,SAAS,EAAC,KAAK;cACfC,GAAG,EAAE,CAAAjE,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEiB,QAAQ,KAAI,6BAA8B;cAC7DiD,GAAG,EAAE,CAAAlE,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEe,IAAI,KAAI,cAAe;cAC1CqC,EAAE,EAAEb,SAAU;cACd4B,OAAO,EAAGC,CAAC,IAAK;gBACd;gBACAA,CAAC,CAACC,MAAM,CAACC,KAAK,CAACR,OAAO,GAAG,MAAM;gBAC/BM,CAAC,CAACC,MAAM,CAACE,WAAW,CAACD,KAAK,CAACR,OAAO,GAAG,OAAO;cAC9C;YAAE;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF/E,OAAA,CAACf,YAAY;cACXuE,EAAE,EAAE;gBACFwB,QAAQ,EAAE,EAAE;gBACZhD,KAAK,EAAEpD,KAAK,CAAC,SAAS,EAAE,GAAG,CAAC;gBAC5BsF,OAAO,EAAE,MAAM,CAAC;cAClB;YAAE;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF/E,OAAA,CAAC1B,GAAG;cAACkF,EAAE,EAAE;gBAAEyB,EAAE,EAAE;cAAE,CAAE;cAAAvB,QAAA,gBACjB1D,OAAA,CAACzB,UAAU;gBACT2G,OAAO,EAAC,IAAI;gBACZd,SAAS,EAAC,IAAI;gBACdZ,EAAE,EAAE;kBACF2B,UAAU,EAAE,GAAG;kBACfhB,EAAE,EAAE,GAAG;kBACPiB,UAAU,EAAE,2BAA2B;kBACvCJ,QAAQ,EAAE;oBAAEhB,EAAE,EAAE,QAAQ;oBAAEC,EAAE,EAAE;kBAAW;gBAC3C,CAAE;gBAAAP,QAAA,EAED,CAAAtD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEe,IAAI,KAAI;cAAsB;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACb/E,OAAA,CAACzB,UAAU;gBACT2G,OAAO,EAAC,IAAI;gBACZ1B,EAAE,EAAE;kBACF2B,UAAU,EAAE,GAAG;kBACfE,OAAO,EAAE,GAAG;kBACZL,QAAQ,EAAE;oBAAEhB,EAAE,EAAE,MAAM;oBAAEC,EAAE,EAAE;kBAAU;gBACxC,CAAE;gBAAAP,QAAA,EAED,CAAAtD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEgB,KAAK,KAAI;cAAyB;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eACb/E,OAAA,CAAC1B,GAAG;gBAACkF,EAAE,EAAE;kBAAE8B,EAAE,EAAE,CAAC;kBAAEpB,OAAO,EAAE,MAAM;kBAAEqB,QAAQ,EAAE,MAAM;kBAAEC,GAAG,EAAE;gBAAE,CAAE;gBAAA9B,QAAA,gBAC5D1D,OAAA,CAACtB,IAAI;kBACH+G,KAAK,EAAC,cAAc;kBACpBC,IAAI,EAAC,OAAO;kBACZlC,EAAE,EAAE;oBACFR,eAAe,EAAEpE,KAAK,CAAC,SAAS,EAAE,GAAG,CAAC;oBACtCoD,KAAK,EAAE,OAAO;oBACdmD,UAAU,EAAE;kBACd;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACF/E,OAAA,CAACtB,IAAI;kBACH+G,KAAK,EAAC,QAAQ;kBACdC,IAAI,EAAC,OAAO;kBACZlC,EAAE,EAAE;oBACFR,eAAe,EAAEpE,KAAK,CAAC,SAAS,EAAE,GAAG,CAAC;oBACtCoD,KAAK,EAAE,OAAO;oBACdmD,UAAU,EAAE;kBACd;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACF/E,OAAA,CAACtB,IAAI;kBACH+G,KAAK,EAAC,WAAW;kBACjBC,IAAI,EAAC,OAAO;kBACZlC,EAAE,EAAE;oBACFR,eAAe,EAAEpE,KAAK,CAAC,SAAS,EAAE,GAAG,CAAC;oBACtCoD,KAAK,EAAE,OAAO;oBACdmD,UAAU,EAAE;kBACd;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN/E,OAAA,CAAC1B,GAAG;YAACkF,EAAE,EAAE;cAAE8B,EAAE,EAAE;YAAE,CAAE;YAAA5B,QAAA,gBACjB1D,OAAA,CAACzB,UAAU;cAAC2G,OAAO,EAAC,IAAI;cAAC1B,EAAE,EAAE;gBAAE2B,UAAU,EAAE,GAAG;gBAAEhB,EAAE,EAAE;cAAI,CAAE;cAAAT,QAAA,GAAC,gBAC3C,EAAC,CAAA1C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2E,UAAU,MAAI3E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4E,QAAQ,KAAI,MAAM,EAAC,GAC9D;YAAA;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb/E,OAAA,CAACzB,UAAU;cAAC2G,OAAO,EAAC,OAAO;cAAC1B,EAAE,EAAE;gBAAE6B,OAAO,EAAE,GAAG;gBAAElB,EAAE,EAAE;cAAE,CAAE;cAAAT,QAAA,EAAC;YAEzD;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EAGZ3E,YAAY,KAAKA,YAAY,CAACkB,OAAO,IAAIlB,YAAY,CAACmB,KAAK,IAAInB,YAAY,CAACoB,KAAK,CAAC,iBACjFxB,OAAA,CAACnB,KAAK;cAACgH,SAAS,EAAC,KAAK;cAAC3D,OAAO,EAAE,CAAE;cAACsB,EAAE,EAAE;gBAAE+B,QAAQ,EAAE,MAAM;gBAAEC,GAAG,EAAE;cAAE,CAAE;cAAA9B,QAAA,GACjEtD,YAAY,CAACkB,OAAO,iBACnBtB,OAAA,CAAC1B,GAAG;gBAACkF,EAAE,EAAE;kBAAEU,OAAO,EAAE,MAAM;kBAAEJ,UAAU,EAAE,QAAQ;kBAAE0B,GAAG,EAAE;gBAAE,CAAE;gBAAA9B,QAAA,gBACzD1D,OAAA,CAACH,YAAY;kBAAC2D,EAAE,EAAE;oBAAEwB,QAAQ,EAAE,EAAE;oBAAEK,OAAO,EAAE;kBAAI;gBAAE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpD/E,OAAA,CAACzB,UAAU;kBACT6F,SAAS,EAAC,GAAG;kBACb0B,IAAI,EAAE1F,YAAY,CAACkB,OAAQ;kBAC3BmD,MAAM,EAAC,QAAQ;kBACfsB,GAAG,EAAC,qBAAqB;kBACzBvC,EAAE,EAAE;oBACFxB,KAAK,EAAE,OAAO;oBACdgE,cAAc,EAAE,WAAW;oBAC3BhB,QAAQ,EAAE,UAAU;oBACpBK,OAAO,EAAE,GAAG;oBACZ,SAAS,EAAE;sBAAEA,OAAO,EAAE;oBAAE;kBAC1B,CAAE;kBAAA3B,QAAA,EAEDtD,YAAY,CAACkB;gBAAO;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CACN,EACA3E,YAAY,CAACmB,KAAK,iBACjBvB,OAAA,CAAC1B,GAAG;gBAACkF,EAAE,EAAE;kBAAEU,OAAO,EAAE,MAAM;kBAAEJ,UAAU,EAAE,QAAQ;kBAAE0B,GAAG,EAAE;gBAAE,CAAE;gBAAA9B,QAAA,gBACzD1D,OAAA,CAACP,SAAS;kBAAC+D,EAAE,EAAE;oBAAEwB,QAAQ,EAAE,EAAE;oBAAEK,OAAO,EAAE;kBAAI;gBAAE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjD/E,OAAA,CAACzB,UAAU;kBAACiF,EAAE,EAAE;oBAAEwB,QAAQ,EAAE,UAAU;oBAAEK,OAAO,EAAE;kBAAI,CAAE;kBAAA3B,QAAA,EACpDtD,YAAY,CAACmB;gBAAK;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CACN,EACA3E,YAAY,CAACoB,KAAK,iBACjBxB,OAAA,CAAC1B,GAAG;gBAACkF,EAAE,EAAE;kBAAEU,OAAO,EAAE,MAAM;kBAAEJ,UAAU,EAAE,QAAQ;kBAAE0B,GAAG,EAAE;gBAAE,CAAE;gBAAA9B,QAAA,gBACzD1D,OAAA,CAACL,SAAS;kBAAC6D,EAAE,EAAE;oBAAEwB,QAAQ,EAAE,EAAE;oBAAEK,OAAO,EAAE;kBAAI;gBAAE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjD/E,OAAA,CAACzB,UAAU;kBACT6F,SAAS,EAAC,GAAG;kBACb0B,IAAI,EAAE,UAAU1F,YAAY,CAACoB,KAAK,EAAG;kBACrCgC,EAAE,EAAE;oBACFxB,KAAK,EAAE,OAAO;oBACdgE,cAAc,EAAE,WAAW;oBAC3BhB,QAAQ,EAAE,UAAU;oBACpBK,OAAO,EAAE,GAAG;oBACZ,SAAS,EAAE;sBAAEA,OAAO,EAAE;oBAAE;kBAC1B,CAAE;kBAAA3B,QAAA,EAEDtD,YAAY,CAACoB;gBAAK;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGP/E,OAAA,CAACvB,IAAI;UAACsF,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAP,QAAA,eACvB1D,OAAA,CAACvB,IAAI;YAACoF,SAAS;YAAC3B,OAAO,EAAE,CAAE;YAAAwB,QAAA,gBACzB1D,OAAA,CAACvB,IAAI;cAACsF,IAAI;cAACC,EAAE,EAAE,CAAE;cAAAN,QAAA,eACf1D,OAAA,CAAC1B,GAAG;gBAACkF,EAAE,EAAEL,cAAe;gBAAAO,QAAA,gBACtB1D,OAAA,CAACjB,aAAa;kBAACyE,EAAE,EAAE;oBAAEwB,QAAQ,EAAE,EAAE;oBAAEb,EAAE,EAAE,CAAC;oBAAEkB,OAAO,EAAE;kBAAI;gBAAE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5D/E,OAAA,CAACzB,UAAU;kBAAC2G,OAAO,EAAC,IAAI;kBAAC1B,EAAE,EAAE;oBAAE2B,UAAU,EAAE,GAAG;oBAAEhB,EAAE,EAAE;kBAAI,CAAE;kBAAAT,QAAA,EAAC;gBAE3D;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb/E,OAAA,CAACzB,UAAU;kBAAC2G,OAAO,EAAC,OAAO;kBAAC1B,EAAE,EAAE;oBAAE6B,OAAO,EAAE;kBAAI,CAAE;kBAAA3B,QAAA,EAAC;gBAElD;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACP/E,OAAA,CAACvB,IAAI;cAACsF,IAAI;cAACC,EAAE,EAAE,CAAE;cAAAN,QAAA,eACf1D,OAAA,CAAC1B,GAAG;gBAACkF,EAAE,EAAEL,cAAe;gBAAAO,QAAA,gBACtB1D,OAAA,CAACX,YAAY;kBAACmE,EAAE,EAAE;oBAAEwB,QAAQ,EAAE,EAAE;oBAAEb,EAAE,EAAE,CAAC;oBAAEkB,OAAO,EAAE;kBAAI;gBAAE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC3D/E,OAAA,CAACzB,UAAU;kBAAC2G,OAAO,EAAC,IAAI;kBAAC1B,EAAE,EAAE;oBAAE2B,UAAU,EAAE,GAAG;oBAAEhB,EAAE,EAAE;kBAAI,CAAE;kBAAAT,QAAA,EAAC;gBAE3D;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb/E,OAAA,CAACzB,UAAU;kBAAC2G,OAAO,EAAC,OAAO;kBAAC1B,EAAE,EAAE;oBAAE6B,OAAO,EAAE;kBAAI,CAAE;kBAAA3B,QAAA,EAAC;gBAElD;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACP/E,OAAA,CAACvB,IAAI;cAACsF,IAAI;cAACC,EAAE,EAAE,CAAE;cAAAN,QAAA,eACf1D,OAAA,CAAC1B,GAAG;gBAACkF,EAAE,EAAEL,cAAe;gBAAAO,QAAA,gBACtB1D,OAAA,CAACb,cAAc;kBAACqE,EAAE,EAAE;oBAAEwB,QAAQ,EAAE,EAAE;oBAAEb,EAAE,EAAE,CAAC;oBAAEkB,OAAO,EAAE;kBAAI;gBAAE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC7D/E,OAAA,CAACzB,UAAU;kBAAC2G,OAAO,EAAC,IAAI;kBAAC1B,EAAE,EAAE;oBAAE2B,UAAU,EAAE,GAAG;oBAAEhB,EAAE,EAAE;kBAAI,CAAE;kBAAAT,QAAA,EAAC;gBAE3D;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb/E,OAAA,CAACzB,UAAU;kBAAC2G,OAAO,EAAC,OAAO;kBAAC1B,EAAE,EAAE;oBAAE6B,OAAO,EAAE;kBAAI,CAAE;kBAAA3B,QAAA,EAAC;gBAElD;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACP/E,OAAA,CAACvB,IAAI;cAACsF,IAAI;cAACC,EAAE,EAAE,CAAE;cAAAN,QAAA,eACf1D,OAAA,CAAC1B,GAAG;gBAACkF,EAAE,EAAEL,cAAe;gBAAAO,QAAA,gBACtB1D,OAAA,CAACf,YAAY;kBAACuE,EAAE,EAAE;oBAAEwB,QAAQ,EAAE,EAAE;oBAAEb,EAAE,EAAE,CAAC;oBAAEkB,OAAO,EAAE;kBAAI;gBAAE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC3D/E,OAAA,CAACzB,UAAU;kBAAC2G,OAAO,EAAC,IAAI;kBAAC1B,EAAE,EAAE;oBAAE2B,UAAU,EAAE,GAAG;oBAAEhB,EAAE,EAAE;kBAAI,CAAE;kBAAAT,QAAA,EAAC;gBAE3D;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb/E,OAAA,CAACzB,UAAU;kBAAC2G,OAAO,EAAC,OAAO;kBAAC1B,EAAE,EAAE;oBAAE6B,OAAO,EAAE;kBAAI,CAAE;kBAAA3B,QAAA,EAAC;gBAElD;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEZ,CAAC;AAAC7E,EAAA,CA9RID,eAAe;EAAA,QACLtB,QAAQ;AAAA;AAAAsH,EAAA,GADlBhG,eAAe;AAgSrB,eAAeA,eAAe;AAAC,IAAAgG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}