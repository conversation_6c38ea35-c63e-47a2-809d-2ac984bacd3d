{"ast": null, "code": "import axios from 'axios';\n\n// Create a base API instance with the /api/v1 prefix\nconst api = axios.create({\n  baseURL: 'http://127.0.0.1:8000/api/v1',\n  headers: {\n    'Content-Type': 'application/json'\n  },\n  withCredentials: true,\n  withXSRFToken: true,\n  xsrfCookieName: 'csrftoken',\n  xsrfHeaderName: 'X-CSRFToken'\n});\n\n// Request interceptor\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Add an interceptor to include the CSRF token\napi.interceptors.request.use(config => {\n  var _document$cookie$spli;\n  const csrfToken = (_document$cookie$spli = document.cookie.split('; ').find(row => row.startsWith('csrftoken='))) === null || _document$cookie$spli === void 0 ? void 0 : _document$cookie$spli.split('=')[1];\n  if (csrfToken) {\n    config.headers['X-CSRFToken'] = csrfToken;\n  }\n  return config;\n});\n\n// Response interceptor\napi.interceptors.response.use(response => response, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    // Handle unauthorized access\n    localStorage.removeItem('token');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\nexport default api;", "map": {"version": 3, "names": ["axios", "api", "create", "baseURL", "headers", "withCredentials", "withXSRFToken", "xsrfCookieName", "xsrfHeaderName", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "_document$cookie$spli", "csrfToken", "document", "cookie", "split", "find", "row", "startsWith", "response", "_error$response", "status", "removeItem", "window", "location", "href"], "sources": ["C:/Users/<USER>/Desktop/project ai/Store Management System/asset management/frontend/src/utils/axios.js"], "sourcesContent": ["import axios from 'axios';\n\n// Create a base API instance with the /api/v1 prefix\nconst api = axios.create({\n  baseURL: 'http://127.0.0.1:8000/api/v1',\n  headers: {\n    'Content-Type': 'application/json',\n  },\n  withCredentials: true,\n  withXSRFToken: true,\n  xsrfCookieName: 'csrftoken',\n  xsrfHeaderName: 'X-CSRFToken'\n});\n\n// Request interceptor\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Add an interceptor to include the CSRF token\napi.interceptors.request.use(config => {\n  const csrfToken = document.cookie.split('; ')\n    .find(row => row.startsWith('csrftoken='))\n    ?.split('=')[1];\n\n  if (csrfToken) {\n    config.headers['X-CSRFToken'] = csrfToken;\n  }\n  return config;\n});\n\n// Response interceptor\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401) {\n      // Handle unauthorized access\n      localStorage.removeItem('token');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\nexport default api;\n\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,GAAG,GAAGD,KAAK,CAACE,MAAM,CAAC;EACvBC,OAAO,EAAE,8BAA8B;EACvCC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB,CAAC;EACDC,eAAe,EAAE,IAAI;EACrBC,aAAa,EAAE,IAAI;EACnBC,cAAc,EAAE,WAAW;EAC3BC,cAAc,EAAE;AAClB,CAAC,CAAC;;AAEF;AACAP,GAAG,CAACQ,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACR,OAAO,CAACY,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAhB,GAAG,CAACQ,YAAY,CAACC,OAAO,CAACC,GAAG,CAACC,MAAM,IAAI;EAAA,IAAAQ,qBAAA;EACrC,MAAMC,SAAS,IAAAD,qBAAA,GAAGE,QAAQ,CAACC,MAAM,CAACC,KAAK,CAAC,IAAI,CAAC,CAC1CC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,UAAU,CAAC,YAAY,CAAC,CAAC,cAAAP,qBAAA,uBAD1BA,qBAAA,CAEdI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAEjB,IAAIH,SAAS,EAAE;IACbT,MAAM,CAACR,OAAO,CAAC,aAAa,CAAC,GAAGiB,SAAS;EAC3C;EACA,OAAOT,MAAM;AACf,CAAC,CAAC;;AAEF;AACAX,GAAG,CAACQ,YAAY,CAACmB,QAAQ,CAACjB,GAAG,CAC1BiB,QAAQ,IAAKA,QAAQ,EACrBX,KAAK,IAAK;EAAA,IAAAY,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAZ,KAAK,CAACW,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClC;IACAhB,YAAY,CAACiB,UAAU,CAAC,OAAO,CAAC;IAChCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOhB,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAehB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}