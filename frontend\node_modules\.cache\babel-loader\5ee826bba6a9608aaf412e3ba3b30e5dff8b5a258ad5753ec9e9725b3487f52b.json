{"ast": null, "code": "import api from '../utils/axios';\nimport { prepareDropdownData } from '../utils/filters';\n\n// Suppliers\nexport const getSuppliers = async (params = {}, includeInactive = false) => {\n  try {\n    // If not explicitly requesting inactive items, only get active ones\n    if (!includeInactive && !params.is_active) {\n      params = {\n        ...params,\n        is_active: true\n      };\n    }\n    console.log('Fetching suppliers with params:', params);\n    const response = await api.get('/suppliers/', {\n      params\n    });\n    console.log('Suppliers fetched successfully:', response.data);\n\n    // Handle different response formats\n    let suppliersData;\n    if (Array.isArray(response.data)) {\n      suppliersData = response.data;\n    } else if (response.data.results && Array.isArray(response.data.results)) {\n      suppliersData = response.data.results;\n    } else if (response.data.data && Array.isArray(response.data.data)) {\n      suppliersData = response.data.data;\n    } else {\n      console.warn('Unexpected data format:', response.data);\n      suppliersData = [];\n    }\n    console.log('Processed suppliers data:', suppliersData);\n    return prepareDropdownData(suppliersData, includeInactive);\n  } catch (error) {\n    console.error('Failed to fetch suppliers:', error);\n    return []; // Return empty array instead of throwing to prevent UI errors\n  }\n};\nexport const getSupplier = async id => {\n  try {\n    const response = await api.get(`/suppliers/${id}/`);\n    return response.data;\n  } catch (error) {\n    console.error(`Error fetching supplier with ID ${id}:`, error);\n    throw error;\n  }\n};\nexport const createSupplier = async data => {\n  try {\n    const response = await api.post('/suppliers/', data);\n    return response.data;\n  } catch (error) {\n    console.error('Error creating supplier:', error);\n    throw error;\n  }\n};\nexport const updateSupplier = async (id, data) => {\n  try {\n    const response = await api.put(`/suppliers/${id}/`, data);\n    return response.data;\n  } catch (error) {\n    console.error(`Error updating supplier with ID ${id}:`, error);\n    throw error;\n  }\n};\nexport const deleteSupplier = async id => {\n  try {\n    await api.delete(`/suppliers/${id}/`);\n    return true;\n  } catch (error) {\n    console.error(`Error deleting supplier with ID ${id}:`, error);\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["api", "prepareDropdownData", "getSuppliers", "params", "includeInactive", "is_active", "console", "log", "response", "get", "data", "suppliersData", "Array", "isArray", "results", "warn", "error", "getSupplier", "id", "createSupplier", "post", "updateSupplier", "put", "deleteSupplier", "delete"], "sources": ["C:/Users/<USER>/Desktop/project ai/Store Management System/asset management/frontend/src/services/suppliers.js"], "sourcesContent": ["import api from '../utils/axios';\nimport { prepareDropdownData } from '../utils/filters';\n\n// Suppliers\nexport const getSuppliers = async (params = {}, includeInactive = false) => {\n  try {\n    // If not explicitly requesting inactive items, only get active ones\n    if (!includeInactive && !params.is_active) {\n      params = { ...params, is_active: true };\n    }\n\n    console.log('Fetching suppliers with params:', params);\n    const response = await api.get('/suppliers/', { params });\n    console.log('Suppliers fetched successfully:', response.data);\n\n    // Handle different response formats\n    let suppliersData;\n    if (Array.isArray(response.data)) {\n      suppliersData = response.data;\n    } else if (response.data.results && Array.isArray(response.data.results)) {\n      suppliersData = response.data.results;\n    } else if (response.data.data && Array.isArray(response.data.data)) {\n      suppliersData = response.data.data;\n    } else {\n      console.warn('Unexpected data format:', response.data);\n      suppliersData = [];\n    }\n\n    console.log('Processed suppliers data:', suppliersData);\n    return prepareDropdownData(suppliersData, includeInactive);\n  } catch (error) {\n    console.error('Failed to fetch suppliers:', error);\n    return []; // Return empty array instead of throwing to prevent UI errors\n  }\n};\n\nexport const getSupplier = async (id) => {\n  try {\n    const response = await api.get(`/suppliers/${id}/`);\n    return response.data;\n  } catch (error) {\n    console.error(`Error fetching supplier with ID ${id}:`, error);\n    throw error;\n  }\n};\n\nexport const createSupplier = async (data) => {\n  try {\n    const response = await api.post('/suppliers/', data);\n    return response.data;\n  } catch (error) {\n    console.error('Error creating supplier:', error);\n    throw error;\n  }\n};\n\nexport const updateSupplier = async (id, data) => {\n  try {\n    const response = await api.put(`/suppliers/${id}/`, data);\n    return response.data;\n  } catch (error) {\n    console.error(`Error updating supplier with ID ${id}:`, error);\n    throw error;\n  }\n};\n\nexport const deleteSupplier = async (id) => {\n  try {\n    await api.delete(`/suppliers/${id}/`);\n    return true;\n  } catch (error) {\n    console.error(`Error deleting supplier with ID ${id}:`, error);\n    throw error;\n  }\n};\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,gBAAgB;AAChC,SAASC,mBAAmB,QAAQ,kBAAkB;;AAEtD;AACA,OAAO,MAAMC,YAAY,GAAG,MAAAA,CAAOC,MAAM,GAAG,CAAC,CAAC,EAAEC,eAAe,GAAG,KAAK,KAAK;EAC1E,IAAI;IACF;IACA,IAAI,CAACA,eAAe,IAAI,CAACD,MAAM,CAACE,SAAS,EAAE;MACzCF,MAAM,GAAG;QAAE,GAAGA,MAAM;QAAEE,SAAS,EAAE;MAAK,CAAC;IACzC;IAEAC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEJ,MAAM,CAAC;IACtD,MAAMK,QAAQ,GAAG,MAAMR,GAAG,CAACS,GAAG,CAAC,aAAa,EAAE;MAAEN;IAAO,CAAC,CAAC;IACzDG,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEC,QAAQ,CAACE,IAAI,CAAC;;IAE7D;IACA,IAAIC,aAAa;IACjB,IAAIC,KAAK,CAACC,OAAO,CAACL,QAAQ,CAACE,IAAI,CAAC,EAAE;MAChCC,aAAa,GAAGH,QAAQ,CAACE,IAAI;IAC/B,CAAC,MAAM,IAAIF,QAAQ,CAACE,IAAI,CAACI,OAAO,IAAIF,KAAK,CAACC,OAAO,CAACL,QAAQ,CAACE,IAAI,CAACI,OAAO,CAAC,EAAE;MACxEH,aAAa,GAAGH,QAAQ,CAACE,IAAI,CAACI,OAAO;IACvC,CAAC,MAAM,IAAIN,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAIE,KAAK,CAACC,OAAO,CAACL,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC,EAAE;MAClEC,aAAa,GAAGH,QAAQ,CAACE,IAAI,CAACA,IAAI;IACpC,CAAC,MAAM;MACLJ,OAAO,CAACS,IAAI,CAAC,yBAAyB,EAAEP,QAAQ,CAACE,IAAI,CAAC;MACtDC,aAAa,GAAG,EAAE;IACpB;IAEAL,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEI,aAAa,CAAC;IACvD,OAAOV,mBAAmB,CAACU,aAAa,EAAEP,eAAe,CAAC;EAC5D,CAAC,CAAC,OAAOY,KAAK,EAAE;IACdV,OAAO,CAACU,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IAClD,OAAO,EAAE,CAAC,CAAC;EACb;AACF,CAAC;AAED,OAAO,MAAMC,WAAW,GAAG,MAAOC,EAAE,IAAK;EACvC,IAAI;IACF,MAAMV,QAAQ,GAAG,MAAMR,GAAG,CAACS,GAAG,CAAC,cAAcS,EAAE,GAAG,CAAC;IACnD,OAAOV,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOM,KAAK,EAAE;IACdV,OAAO,CAACU,KAAK,CAAC,mCAAmCE,EAAE,GAAG,EAAEF,KAAK,CAAC;IAC9D,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMG,cAAc,GAAG,MAAOT,IAAI,IAAK;EAC5C,IAAI;IACF,MAAMF,QAAQ,GAAG,MAAMR,GAAG,CAACoB,IAAI,CAAC,aAAa,EAAEV,IAAI,CAAC;IACpD,OAAOF,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOM,KAAK,EAAE;IACdV,OAAO,CAACU,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAChD,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMK,cAAc,GAAG,MAAAA,CAAOH,EAAE,EAAER,IAAI,KAAK;EAChD,IAAI;IACF,MAAMF,QAAQ,GAAG,MAAMR,GAAG,CAACsB,GAAG,CAAC,cAAcJ,EAAE,GAAG,EAAER,IAAI,CAAC;IACzD,OAAOF,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOM,KAAK,EAAE;IACdV,OAAO,CAACU,KAAK,CAAC,mCAAmCE,EAAE,GAAG,EAAEF,KAAK,CAAC;IAC9D,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMO,cAAc,GAAG,MAAOL,EAAE,IAAK;EAC1C,IAAI;IACF,MAAMlB,GAAG,CAACwB,MAAM,CAAC,cAAcN,EAAE,GAAG,CAAC;IACrC,OAAO,IAAI;EACb,CAAC,CAAC,OAAOF,KAAK,EAAE;IACdV,OAAO,CAACU,KAAK,CAAC,mCAAmCE,EAAE,GAAG,EAAEF,KAAK,CAAC;IAC9D,MAAMA,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}