{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project ai\\\\Store Management System\\\\asset management\\\\frontend\\\\src\\\\features\\\\dashboard\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Grid, Card, CardContent, CardHeader, Avatar, Divider, Chip, Stack, Button, CircularProgress, useTheme, alpha, LinearProgress, IconButton, Tooltip, Menu, MenuItem, ListItemIcon, ListItemText } from '@mui/material';\nimport DashboardBanner from '../../components/DashboardBanner';\nimport { Business as BusinessIcon, Category as CategoryIcon, LocationOn as LocationIcon, Assignment as AssignmentIcon, Inventory as InventoryIcon, People as PeopleIcon, Store as StoreIcon, Dashboard as DashboardIcon, MoreVert as MoreVertIcon, Refresh as RefreshIcon, TrendingUp as TrendingUpIcon, TrendingDown as TrendingDownIcon, Warning as WarningIcon, CheckCircle as CheckCircleIcon, Error as ErrorIcon, Info as InfoIcon, Settings as SettingsIcon, BarChart as BarChartIcon, PieChart as PieChartIcon, Timeline as TimelineIcon, CalendarToday as CalendarTodayIcon, Language as LanguageIcon, Phone as PhoneIcon, Email as EmailIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { getRequisitions } from '../../services/requisitions';\nimport { authService } from '../../services/auth';\nimport api from '../../utils/axios';\nimport { useSnackbar } from 'notistack';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const [organization, setOrganization] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [refreshing, setRefreshing] = useState(false);\n  const [menuAnchorEl, setMenuAnchorEl] = useState(null);\n  const [currentUser, setCurrentUser] = useState(null);\n  const [userRequisitions, setUserRequisitions] = useState([]);\n  const [stats, setStats] = useState({\n    organizations: 0,\n    offices: 0,\n    items: 0,\n    gatePasses: 0,\n    activeGatePasses: 0,\n    overdueGatePasses: 0,\n    pendingRequisitions: 0,\n    approvedRequisitions: 0,\n    rejectedRequisitions: 0\n  });\n  const {\n    enqueueSnackbar\n  } = useSnackbar();\n  const navigate = useNavigate();\n  const theme = useTheme();\n  const handleMenuOpen = event => {\n    setMenuAnchorEl(event.currentTarget);\n  };\n  const handleMenuClose = () => {\n    setMenuAnchorEl(null);\n  };\n  const refreshData = async () => {\n    setRefreshing(true);\n    try {\n      await fetchOrganization();\n      await fetchStats();\n      await fetchUserRequisitions();\n      enqueueSnackbar('Dashboard data refreshed', {\n        variant: 'success'\n      });\n    } catch (error) {\n      console.error('Error refreshing data:', error);\n      enqueueSnackbar('Failed to refresh data', {\n        variant: 'error'\n      });\n    } finally {\n      setRefreshing(false);\n    }\n  };\n  const fetchUserRequisitions = async () => {\n    try {\n      // Get current user if not already loaded\n      if (!currentUser) {\n        const user = await authService.getCurrentUser();\n        setCurrentUser(user);\n      }\n\n      // Fetch user's requisitions\n      const response = await getRequisitions();\n      if (response && response.results) {\n        setUserRequisitions(response.results);\n\n        // Update requisition stats\n        const pendingCount = response.results.filter(req => {\n          var _req$status_name;\n          return (_req$status_name = req.status_name) === null || _req$status_name === void 0 ? void 0 : _req$status_name.toLowerCase().includes('pending');\n        }).length;\n        const approvedCount = response.results.filter(req => {\n          var _req$status_name2, _req$status_name3;\n          return ((_req$status_name2 = req.status_name) === null || _req$status_name2 === void 0 ? void 0 : _req$status_name2.toLowerCase()) === 'approved' || ((_req$status_name3 = req.status_name) === null || _req$status_name3 === void 0 ? void 0 : _req$status_name3.toLowerCase()) === 'handed over';\n        }).length;\n        const rejectedCount = response.results.filter(req => {\n          var _req$status_name4;\n          return ((_req$status_name4 = req.status_name) === null || _req$status_name4 === void 0 ? void 0 : _req$status_name4.toLowerCase()) === 'rejected';\n        }).length;\n        setStats(prev => ({\n          ...prev,\n          pendingRequisitions: pendingCount,\n          approvedRequisitions: approvedCount,\n          rejectedRequisitions: rejectedCount\n        }));\n      }\n    } catch (error) {\n      console.error('Error fetching user requisitions:', error);\n    }\n  };\n  const fetchOrganization = async () => {\n    try {\n      // First try to get the main organization\n      try {\n        const mainResponse = await api.get('/organizations/main/');\n        if (mainResponse.data) {\n          setOrganization(mainResponse.data);\n          return; // Exit if we found the main organization\n        }\n      } catch (mainError) {\n        console.log('No main organization set, falling back to first organization');\n      }\n\n      // Fallback to getting the first organization if no main is set\n      const response = await api.get('/organizations/');\n      if (Array.isArray(response.data) && response.data.length > 0) {\n        setOrganization(response.data[0]);\n      } else if (response.data && response.data.results && Array.isArray(response.data.results) && response.data.results.length > 0) {\n        setOrganization(response.data.results[0]);\n      } else {\n        console.error('No organizations found');\n      }\n    } catch (error) {\n      console.error('Error fetching organization:', error);\n      throw error;\n    }\n  };\n  const fetchStats = async () => {\n    try {\n      // Fetch counts for various entities\n      const [orgsResponse, officesResponse, gatePasses, activeGatePasses, overdueGatePasses] = await Promise.all([api.get('/organizations/'), api.get('/offices/'), api.get('/gate-passes/'), api.get('/gate-passes/active/'), api.get('/gate-passes/overdue/')]);\n      setStats({\n        organizations: Array.isArray(orgsResponse.data) ? orgsResponse.data.length : 0,\n        offices: Array.isArray(officesResponse.data) ? officesResponse.data.length : 0,\n        gatePasses: Array.isArray(gatePasses.data) ? gatePasses.data.length : 0,\n        activeGatePasses: Array.isArray(activeGatePasses.data) ? activeGatePasses.data.length : 0,\n        overdueGatePasses: Array.isArray(overdueGatePasses.data) ? overdueGatePasses.data.length : 0\n      });\n    } catch (error) {\n      console.error('Error fetching stats:', error);\n      throw error;\n    }\n  };\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        await fetchOrganization();\n        await fetchStats();\n        await fetchUserRequisitions();\n      } catch (error) {\n        enqueueSnackbar('Failed to fetch dashboard information', {\n          variant: 'error'\n        });\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadData();\n  }, [enqueueSnackbar]);\n  const StatCard = ({\n    title,\n    value,\n    icon,\n    color,\n    onClick,\n    trend,\n    trendValue,\n    subtitle\n  }) => /*#__PURE__*/_jsxDEV(Card, {\n    sx: {\n      height: '100%',\n      cursor: onClick ? 'pointer' : 'default',\n      transition: 'all 0.3s ease',\n      borderRadius: 3,\n      overflow: 'hidden',\n      position: 'relative',\n      border: '1px solid rgba(0, 0, 0, 0.05)',\n      background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.6) 100%)',\n      backdropFilter: 'blur(10px)',\n      '&:hover': onClick ? {\n        transform: 'translateY(-5px)',\n        boxShadow: `0 15px 30px ${alpha(theme.palette.common.black, 0.1)}`\n      } : {},\n      '&::after': {\n        content: '\"\"',\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        width: '100%',\n        height: '4px',\n        background: `linear-gradient(90deg, ${color} 0%, ${alpha(color, 0.4)} 100%)`\n      }\n    },\n    onClick: onClick,\n    elevation: 0,\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      sx: {\n        p: {\n          xs: 2,\n          sm: 3\n        },\n        overflow: 'hidden'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 2,\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'flex-start'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          sx: {\n            bgcolor: alpha(color, 0.1),\n            color: color,\n            width: 56,\n            height: 56,\n            borderRadius: 2,\n            boxShadow: `0 4px 12px ${alpha(color, 0.2)}`,\n            '& .MuiSvgIcon-root': {\n              fontSize: '1.75rem'\n            }\n          },\n          children: icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 13\n        }, this), trend && /*#__PURE__*/_jsxDEV(Chip, {\n          icon: trend === 'up' ? /*#__PURE__*/_jsxDEV(TrendingUpIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 40\n          }, this) : /*#__PURE__*/_jsxDEV(TrendingDownIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 61\n          }, this),\n          label: `${trendValue}%`,\n          size: \"small\",\n          color: trend === 'up' ? 'success' : 'error',\n          variant: \"filled\",\n          sx: {\n            fontWeight: 'bold',\n            borderRadius: '20px',\n            '& .MuiChip-icon': {\n              fontSize: '1rem'\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h3\",\n        component: \"div\",\n        sx: {\n          fontWeight: 800,\n          mb: 0.5,\n          background: `linear-gradient(90deg, ${color} 0%, ${alpha(color, 0.7)} 100%)`,\n          WebkitBackgroundClip: 'text',\n          WebkitTextFillColor: 'transparent',\n          letterSpacing: '-0.025em',\n          fontSize: {\n            xs: '1.75rem',\n            sm: '2.25rem',\n            md: '2.5rem'\n          },\n          overflow: 'hidden',\n          textOverflow: 'ellipsis',\n          whiteSpace: 'nowrap'\n        },\n        children: value\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        sx: {\n          fontWeight: 600,\n          color: theme.palette.text.primary,\n          mb: 0.5,\n          overflow: 'hidden',\n          textOverflow: 'ellipsis',\n          whiteSpace: 'nowrap'\n        },\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 11\n      }, this), subtitle && /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        sx: {\n          color: theme.palette.text.secondary,\n          display: 'block',\n          fontWeight: 500,\n          opacity: 0.8,\n          overflow: 'hidden',\n          textOverflow: 'ellipsis',\n          whiteSpace: 'nowrap'\n        },\n        children: subtitle\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 212,\n    columnNumber: 5\n  }, this);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexDirection: 'column',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '80vh'\n      },\n      children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 60,\n        thickness: 4,\n        sx: {\n          mb: 3\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"Loading dashboard...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 320,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Dashboard menu\n  const dashboardMenu = /*#__PURE__*/_jsxDEV(Menu, {\n    anchorEl: menuAnchorEl,\n    open: Boolean(menuAnchorEl),\n    onClose: handleMenuClose,\n    PaperProps: {\n      elevation: 3,\n      sx: {\n        overflow: 'visible',\n        filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.1))',\n        mt: 1.5,\n        '&:before': {\n          content: '\"\"',\n          display: 'block',\n          position: 'absolute',\n          top: 0,\n          right: 14,\n          width: 10,\n          height: 10,\n          bgcolor: 'background.paper',\n          transform: 'translateY(-50%) rotate(45deg)',\n          zIndex: 0\n        }\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n      onClick: refreshData,\n      children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n        children: /*#__PURE__*/_jsxDEV(RefreshIcon, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n        children: \"Refresh Data\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 356,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n      onClick: () => navigate('/organizations'),\n      children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n        children: /*#__PURE__*/_jsxDEV(BusinessIcon, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n        children: \"Manage Organizations\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n      onClick: () => navigate('/settings'),\n      children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n        children: /*#__PURE__*/_jsxDEV(SettingsIcon, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n        children: \"Dashboard Settings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 331,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      width: '100%',\n      maxWidth: '100%',\n      overflow: 'hidden'\n    },\n    children: [refreshing && /*#__PURE__*/_jsxDEV(LinearProgress, {\n      sx: {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        zIndex: 9999,\n        height: 3\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 380,\n      columnNumber: 9\n    }, this), organization && /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 3,\n      sx: {\n        p: {\n          xs: 3,\n          sm: 4\n        },\n        mb: 5,\n        borderRadius: 4,\n        position: 'relative',\n        overflow: 'hidden',\n        background: `radial-gradient(circle at 20% 40%, ${alpha(theme.palette.primary.light, 0.18)} 0%, transparent 60%),\n                         radial-gradient(circle at 80% 30%, ${alpha(theme.palette.secondary.light, 0.18)} 0%, transparent 60%),\n                         repeating-radial-gradient(circle at 50% 50%, ${alpha(theme.palette.info.light, 0.13)} 0px, transparent 2px, transparent 20px),\n                         linear-gradient(120deg, ${alpha(theme.palette.primary.main, 0.08)} 0%, ${alpha(theme.palette.secondary.main, 0.07)} 100%)`,\n        boxShadow: `0 8px 32px 0 ${alpha(theme.palette.primary.main, 0.13)}`,\n        border: `2.5px solid ${alpha(theme.palette.divider, 0.18)}`,\n        '&::before': {\n          content: '\"\"',\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          width: '100%',\n          height: '100%',\n          background: `repeating-radial-gradient(circle at 50% 50%, ${alpha(theme.palette.primary.main, 0.12)} 0px, transparent 2px, transparent 30px)`,\n          opacity: 0.7,\n          zIndex: 0,\n          pointerEvents: 'none'\n        },\n        '&::after': {\n          content: '\"\"',\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          width: '100%',\n          height: '100%',\n          background: `repeating-radial-gradient(circle at 50% 50%, ${alpha(theme.palette.secondary.main, 0.09)} 0px, transparent 1.5px, transparent 18px)`,\n          opacity: 0.5,\n          zIndex: 0,\n          pointerEvents: 'none'\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          width: '100%',\n          height: 160,\n          zIndex: 1,\n          opacity: 0.32,\n          pointerEvents: 'none',\n          overflow: 'hidden'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"100%\",\n          height: \"160\",\n          viewBox: \"0 0 800 160\",\n          fill: \"none\",\n          xmlns: \"http://www.w3.org/2000/svg\",\n          style: {\n            minWidth: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"defs\", {\n            children: [/*#__PURE__*/_jsxDEV(\"linearGradient\", {\n              id: \"skyGrad\",\n              x1: \"0\",\n              y1: \"0\",\n              x2: \"0\",\n              y2: \"1\",\n              children: [/*#__PURE__*/_jsxDEV(\"stop\", {\n                offset: \"0%\",\n                stopColor: alpha(theme.palette.info.light, 0.18)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"stop\", {\n                offset: \"100%\",\n                stopColor: alpha(theme.palette.primary.light, 0.10)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"radialGradient\", {\n              id: \"cloudWhite\",\n              cx: \"0.5\",\n              cy: \"0.5\",\n              r: \"0.5\",\n              children: [/*#__PURE__*/_jsxDEV(\"stop\", {\n                offset: \"0%\",\n                stopColor: alpha('#fff', 0.7)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"stop\", {\n                offset: \"100%\",\n                stopColor: alpha('#fff', 0.0)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n            x: \"0\",\n            y: \"0\",\n            width: \"800\",\n            height: \"160\",\n            fill: \"url(#skyGrad)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"ellipse\", {\n            cx: \"120\",\n            cy: \"60\",\n            rx: \"70\",\n            ry: \"22\",\n            fill: \"url(#cloudWhite)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"ellipse\", {\n            cx: \"250\",\n            cy: \"90\",\n            rx: \"60\",\n            ry: \"18\",\n            fill: \"url(#cloudWhite)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"ellipse\", {\n            cx: \"400\",\n            cy: \"50\",\n            rx: \"90\",\n            ry: \"28\",\n            fill: \"url(#cloudWhite)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"ellipse\", {\n            cx: \"600\",\n            cy: \"80\",\n            rx: \"80\",\n            ry: \"22\",\n            fill: \"url(#cloudWhite)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"ellipse\", {\n            cx: \"700\",\n            cy: \"55\",\n            rx: \"60\",\n            ry: \"16\",\n            fill: \"url(#cloudWhite)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 434,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: {\n          xs: 2,\n          sm: 3\n        },\n        alignItems: \"center\",\n        sx: {\n          width: '100%',\n          mx: 0,\n          position: 'relative',\n          zIndex: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: \"auto\",\n          children: organization.logo_url && /*#__PURE__*/_jsxDEV(Box, {\n            component: \"img\",\n            src: organization.logo_url,\n            alt: organization.name,\n            sx: {\n              maxWidth: {\n                xs: 140,\n                sm: 180\n              },\n              maxHeight: {\n                xs: 80,\n                sm: 100\n              },\n              objectFit: 'contain',\n              background: 'transparent',\n              borderRadius: 2,\n              boxShadow: '0 4px 24px 0 rgba(0,0,0,0.07)',\n              p: 0,\n              m: 0,\n              display: 'block'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: true,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h3\",\n            component: \"h1\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 900,\n              fontSize: {\n                xs: '2.2rem',\n                sm: '2.8rem'\n              },\n              color: theme.palette.text.primary,\n              letterSpacing: '-0.02em',\n              textShadow: `0 2px 16px ${alpha(theme.palette.primary.main, 0.13)}`,\n              mb: 0.5,\n              overflow: 'hidden',\n              textOverflow: 'ellipsis',\n              maxWidth: '100%',\n              background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n              WebkitBackgroundClip: 'text',\n              WebkitTextFillColor: 'transparent',\n              display: 'inline-block'\n            },\n            children: [organization.name, organization.shortcode && /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontWeight: 800,\n                color: theme.palette.secondary.main\n              },\n              children: [' - ', organization.shortcode]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 15\n          }, this), organization.motto && /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"text.secondary\",\n            gutterBottom: true,\n            sx: {\n              fontStyle: 'italic',\n              fontWeight: 400,\n              fontSize: {\n                xs: '1.1rem',\n                sm: '1.2rem'\n              },\n              opacity: 0.85,\n              mb: 1,\n              overflow: 'hidden',\n              textOverflow: 'ellipsis',\n              maxWidth: '100%'\n            },\n            children: [\"\\\"\", organization.motto, \"\\\"\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 3,\n            sx: {\n              mt: 1,\n              flexWrap: 'wrap'\n            },\n            children: [organization.website && /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(LanguageIcon, {\n                sx: {\n                  fontSize: 22,\n                  color: theme.palette.text.secondary\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 539,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: organization.website,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                style: {\n                  color: theme.palette.primary.main,\n                  textDecoration: 'underline',\n                  wordBreak: 'break-all',\n                  fontWeight: 600\n                },\n                children: organization.website\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 19\n            }, this), organization.phone && /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(PhoneIcon, {\n                sx: {\n                  fontSize: 22,\n                  color: theme.palette.text.secondary\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 545,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontWeight: 600\n                },\n                children: organization.phone\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 546,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 19\n            }, this), organization.email && /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(EmailIcon, {\n                sx: {\n                  fontSize: 22,\n                  color: theme.palette.text.secondary\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 551,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: `mailto:${organization.email}`,\n                style: {\n                  color: theme.palette.primary.main,\n                  textDecoration: 'underline',\n                  wordBreak: 'break-all',\n                  fontWeight: 600\n                },\n                children: organization.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 536,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 488,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 467,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 393,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        component: \"h2\",\n        sx: {\n          fontWeight: 700,\n          position: 'relative',\n          display: 'inline-block',\n          '&::after': {\n            content: '\"\"',\n            position: 'absolute',\n            bottom: -8,\n            left: 0,\n            width: 60,\n            height: 4,\n            borderRadius: 2,\n            background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${alpha(theme.palette.primary.main, 0.4)} 100%)`\n          }\n        },\n        children: \"System Overview\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 562,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"Refresh Data\",\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: refreshData,\n          color: \"primary\",\n          disabled: refreshing,\n          children: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 585,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 584,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 583,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 561,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: {\n        xs: 2,\n        sm: 3\n      },\n      sx: {\n        mb: 4,\n        width: '100%',\n        mx: 0\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Organizations\",\n          value: stats.organizations,\n          icon: /*#__PURE__*/_jsxDEV(BusinessIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 596,\n            columnNumber: 21\n          }, this),\n          color: theme.palette.primary.main,\n          onClick: () => navigate('/organizations'),\n          trend: \"up\",\n          trendValue: \"12\",\n          subtitle: \"Total registered organizations\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 593,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 592,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Offices\",\n          value: stats.offices,\n          icon: /*#__PURE__*/_jsxDEV(LocationIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 608,\n            columnNumber: 21\n          }, this),\n          color: theme.palette.secondary.main,\n          onClick: () => navigate('/offices'),\n          trend: \"up\",\n          trendValue: \"5\",\n          subtitle: \"Across all organizations\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 605,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 604,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Classifications\",\n          value: \"24\",\n          icon: /*#__PURE__*/_jsxDEV(CategoryIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 620,\n            columnNumber: 21\n          }, this),\n          color: theme.palette.warning.main,\n          onClick: () => navigate('/main-classifications'),\n          subtitle: \"Main & sub classifications\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 617,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 616,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Inventory Items\",\n          value: \"156\",\n          icon: /*#__PURE__*/_jsxDEV(InventoryIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 630,\n            columnNumber: 21\n          }, this),\n          color: theme.palette.info.main,\n          trend: \"up\",\n          trendValue: \"8\",\n          subtitle: \"Total items in inventory\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 627,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 626,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 591,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        component: \"h2\",\n        sx: {\n          fontWeight: 700,\n          position: 'relative',\n          display: 'inline-block',\n          '&::after': {\n            content: '\"\"',\n            position: 'absolute',\n            bottom: -8,\n            left: 0,\n            width: 60,\n            height: 4,\n            borderRadius: 2,\n            background: `linear-gradient(90deg, ${theme.palette.secondary.main} 0%, ${alpha(theme.palette.secondary.main, 0.4)} 100%)`\n          }\n        },\n        children: \"Gate Passes\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 640,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 639,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: {\n        xs: 2,\n        sm: 3\n      },\n      sx: {\n        width: '100%',\n        mx: 0\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Total Gate Passes\",\n          value: stats.gatePasses,\n          icon: /*#__PURE__*/_jsxDEV(AssignmentIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 668,\n            columnNumber: 21\n          }, this),\n          color: theme.palette.info.dark,\n          onClick: () => navigate('/gate-passes'),\n          subtitle: \"All time gate passes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 665,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 664,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Active Gate Passes\",\n          value: stats.activeGatePasses,\n          icon: /*#__PURE__*/_jsxDEV(CheckCircleIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 678,\n            columnNumber: 21\n          }, this),\n          color: theme.palette.success.main,\n          onClick: () => navigate('/gate-passes'),\n          trend: \"up\",\n          trendValue: \"15\",\n          subtitle: \"Currently active passes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 675,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 674,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Overdue Gate Passes\",\n          value: stats.overdueGatePasses,\n          icon: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 690,\n            columnNumber: 21\n          }, this),\n          color: theme.palette.error.main,\n          onClick: () => navigate('/gate-passes'),\n          trend: \"down\",\n          trendValue: \"5\",\n          subtitle: \"Require immediate attention\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 687,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 686,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 663,\n      columnNumber: 9\n    }, this), dashboardMenu]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 378,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"IKtzbI9tjaBRs2c4j/qSuisal2U=\", false, function () {\n  return [useSnackbar, useNavigate, useTheme];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Avatar", "Divider", "Chip", "<PERSON><PERSON>", "<PERSON><PERSON>", "CircularProgress", "useTheme", "alpha", "LinearProgress", "IconButton", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "MenuItem", "ListItemIcon", "ListItemText", "DashboardBanner", "Business", "BusinessIcon", "Category", "CategoryIcon", "LocationOn", "LocationIcon", "Assignment", "AssignmentIcon", "Inventory", "InventoryIcon", "People", "PeopleIcon", "Store", "StoreIcon", "Dashboard", "DashboardIcon", "<PERSON><PERSON><PERSON>", "MoreVertIcon", "Refresh", "RefreshIcon", "TrendingUp", "TrendingUpIcon", "TrendingDown", "TrendingDownIcon", "Warning", "WarningIcon", "CheckCircle", "CheckCircleIcon", "Error", "ErrorIcon", "Info", "InfoIcon", "Settings", "SettingsIcon", "<PERSON><PERSON><PERSON>", "BarChartIcon", "<PERSON><PERSON><PERSON>", "PieChartIcon", "Timeline", "TimelineIcon", "CalendarToday", "CalendarTodayIcon", "Language", "LanguageIcon", "Phone", "PhoneIcon", "Email", "EmailIcon", "useNavigate", "getRequisitions", "authService", "api", "useSnackbar", "jsxDEV", "_jsxDEV", "_s", "organization", "setOrganization", "loading", "setLoading", "refreshing", "setRefreshing", "menuAnchorEl", "setMenuAnchorEl", "currentUser", "setCurrentUser", "userRequisitions", "setUserRequisitions", "stats", "setStats", "organizations", "offices", "items", "gatePasses", "activeGatePasses", "overdueGatePasses", "pendingRequisitions", "approvedRequisitions", "rejectedRequisitions", "enqueueSnackbar", "navigate", "theme", "handleMenuOpen", "event", "currentTarget", "handleMenuClose", "refreshData", "fetchOrganization", "fetchStats", "fetchUserRequisitions", "variant", "error", "console", "user", "getCurrentUser", "response", "results", "pendingCount", "filter", "req", "_req$status_name", "status_name", "toLowerCase", "includes", "length", "approvedCount", "_req$status_name2", "_req$status_name3", "rejectedCount", "_req$status_name4", "prev", "mainResponse", "get", "data", "mainError", "log", "Array", "isArray", "orgsResponse", "officesResponse", "Promise", "all", "loadData", "StatCard", "title", "value", "icon", "color", "onClick", "trend", "trendValue", "subtitle", "sx", "height", "cursor", "transition", "borderRadius", "overflow", "position", "border", "background", "<PERSON><PERSON>ilter", "transform", "boxShadow", "palette", "common", "black", "content", "top", "left", "width", "elevation", "children", "p", "xs", "sm", "mb", "display", "justifyContent", "alignItems", "bgcolor", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "size", "fontWeight", "component", "WebkitBackgroundClip", "WebkitTextFillColor", "letterSpacing", "md", "textOverflow", "whiteSpace", "text", "primary", "secondary", "opacity", "flexDirection", "thickness", "dashboardMenu", "anchorEl", "open", "Boolean", "onClose", "PaperProps", "mt", "right", "zIndex", "max<PERSON><PERSON><PERSON>", "light", "info", "main", "divider", "pointerEvents", "viewBox", "fill", "xmlns", "style", "min<PERSON><PERSON><PERSON>", "id", "x1", "y1", "x2", "y2", "offset", "stopColor", "cx", "cy", "r", "x", "y", "rx", "ry", "container", "spacing", "mx", "item", "logo_url", "src", "alt", "name", "maxHeight", "objectFit", "m", "gutterBottom", "textShadow", "shortcode", "motto", "fontStyle", "direction", "flexWrap", "website", "gap", "href", "target", "rel", "textDecoration", "wordBreak", "phone", "email", "bottom", "disabled", "warning", "dark", "success", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/project ai/Store Management System/asset management/frontend/src/features/dashboard/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n  CardHeader,\n  Avatar,\n  Divider,\n  Chip,\n  Stack,\n  Button,\n  CircularProgress,\n  useTheme,\n  alpha,\n  LinearProgress,\n  IconButton,\n  Tooltip,\n  Menu,\n  MenuItem,\n  ListItemIcon,\n  ListItemText,\n} from '@mui/material';\nimport DashboardBanner from '../../components/DashboardBanner';\n\nimport {\n  Business as BusinessIcon,\n  Category as CategoryIcon,\n  LocationOn as LocationIcon,\n  Assignment as AssignmentIcon,\n  Inventory as InventoryIcon,\n  People as PeopleIcon,\n  Store as StoreIcon,\n  Dashboard as DashboardIcon,\n  MoreVert as MoreVertIcon,\n  Refresh as RefreshIcon,\n  TrendingUp as TrendingUpIcon,\n  TrendingDown as TrendingDownIcon,\n  Warning as WarningIcon,\n  CheckCircle as CheckCircleIcon,\n  Error as ErrorIcon,\n  Info as InfoIcon,\n  Settings as SettingsIcon,\n  <PERSON><PERSON><PERSON> as BarChartIcon,\n  <PERSON><PERSON><PERSON> as PieChartIcon,\n  Timeline as TimelineIcon,\n  CalendarToday as CalendarTodayIcon,\n  Language as LanguageIcon,\n  Phone as PhoneIcon,\n  Email as EmailIcon,\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { getRequisitions } from '../../services/requisitions';\nimport { authService } from '../../services/auth';\nimport api from '../../utils/axios';\nimport { useSnackbar } from 'notistack';\n\nconst Dashboard = () => {\n  const [organization, setOrganization] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [refreshing, setRefreshing] = useState(false);\n  const [menuAnchorEl, setMenuAnchorEl] = useState(null);\n  const [currentUser, setCurrentUser] = useState(null);\n  const [userRequisitions, setUserRequisitions] = useState([]);\n  const [stats, setStats] = useState({\n    organizations: 0,\n    offices: 0,\n    items: 0,\n    gatePasses: 0,\n    activeGatePasses: 0,\n    overdueGatePasses: 0,\n    pendingRequisitions: 0,\n    approvedRequisitions: 0,\n    rejectedRequisitions: 0,\n  });\n  const { enqueueSnackbar } = useSnackbar();\n  const navigate = useNavigate();\n  const theme = useTheme();\n\n\n\n  const handleMenuOpen = (event) => {\n    setMenuAnchorEl(event.currentTarget);\n  };\n\n  const handleMenuClose = () => {\n    setMenuAnchorEl(null);\n  };\n\n  const refreshData = async () => {\n    setRefreshing(true);\n    try {\n      await fetchOrganization();\n      await fetchStats();\n      await fetchUserRequisitions();\n      enqueueSnackbar('Dashboard data refreshed', { variant: 'success' });\n    } catch (error) {\n      console.error('Error refreshing data:', error);\n      enqueueSnackbar('Failed to refresh data', { variant: 'error' });\n    } finally {\n      setRefreshing(false);\n    }\n  };\n\n  const fetchUserRequisitions = async () => {\n    try {\n      // Get current user if not already loaded\n      if (!currentUser) {\n        const user = await authService.getCurrentUser();\n        setCurrentUser(user);\n      }\n\n      // Fetch user's requisitions\n      const response = await getRequisitions();\n      if (response && response.results) {\n        setUserRequisitions(response.results);\n\n        // Update requisition stats\n        const pendingCount = response.results.filter(req =>\n          req.status_name?.toLowerCase().includes('pending')).length;\n\n        const approvedCount = response.results.filter(req =>\n          req.status_name?.toLowerCase() === 'approved' ||\n          req.status_name?.toLowerCase() === 'handed over').length;\n\n        const rejectedCount = response.results.filter(req =>\n          req.status_name?.toLowerCase() === 'rejected').length;\n\n        setStats(prev => ({\n          ...prev,\n          pendingRequisitions: pendingCount,\n          approvedRequisitions: approvedCount,\n          rejectedRequisitions: rejectedCount\n        }));\n      }\n    } catch (error) {\n      console.error('Error fetching user requisitions:', error);\n    }\n  };\n\n  const fetchOrganization = async () => {\n    try {\n      // First try to get the main organization\n      try {\n        const mainResponse = await api.get('/organizations/main/');\n        if (mainResponse.data) {\n          setOrganization(mainResponse.data);\n          return; // Exit if we found the main organization\n        }\n      } catch (mainError) {\n        console.log('No main organization set, falling back to first organization');\n      }\n\n      // Fallback to getting the first organization if no main is set\n      const response = await api.get('/organizations/');\n      if (Array.isArray(response.data) && response.data.length > 0) {\n        setOrganization(response.data[0]);\n      } else if (response.data && response.data.results && Array.isArray(response.data.results) && response.data.results.length > 0) {\n        setOrganization(response.data.results[0]);\n      } else {\n        console.error('No organizations found');\n      }\n    } catch (error) {\n      console.error('Error fetching organization:', error);\n      throw error;\n    }\n  };\n\n  const fetchStats = async () => {\n    try {\n      // Fetch counts for various entities\n      const [orgsResponse, officesResponse, gatePasses, activeGatePasses, overdueGatePasses] = await Promise.all([\n        api.get('/organizations/'),\n        api.get('/offices/'),\n        api.get('/gate-passes/'),\n        api.get('/gate-passes/active/'),\n        api.get('/gate-passes/overdue/'),\n      ]);\n\n      setStats({\n        organizations: Array.isArray(orgsResponse.data) ? orgsResponse.data.length : 0,\n        offices: Array.isArray(officesResponse.data) ? officesResponse.data.length : 0,\n        gatePasses: Array.isArray(gatePasses.data) ? gatePasses.data.length : 0,\n        activeGatePasses: Array.isArray(activeGatePasses.data) ? activeGatePasses.data.length : 0,\n        overdueGatePasses: Array.isArray(overdueGatePasses.data) ? overdueGatePasses.data.length : 0,\n      });\n    } catch (error) {\n      console.error('Error fetching stats:', error);\n      throw error;\n    }\n  };\n\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        await fetchOrganization();\n        await fetchStats();\n        await fetchUserRequisitions();\n      } catch (error) {\n        enqueueSnackbar('Failed to fetch dashboard information', { variant: 'error' });\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadData();\n  }, [enqueueSnackbar]);\n\n  const StatCard = ({ title, value, icon, color, onClick, trend, trendValue, subtitle }) => (\n    <Card\n      sx={{\n        height: '100%',\n        cursor: onClick ? 'pointer' : 'default',\n        transition: 'all 0.3s ease',\n        borderRadius: 3,\n        overflow: 'hidden',\n        position: 'relative',\n        border: '1px solid rgba(0, 0, 0, 0.05)',\n        background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.6) 100%)',\n        backdropFilter: 'blur(10px)',\n        '&:hover': onClick ? {\n          transform: 'translateY(-5px)',\n          boxShadow: `0 15px 30px ${alpha(theme.palette.common.black, 0.1)}`,\n        } : {},\n        '&::after': {\n          content: '\"\"',\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          width: '100%',\n          height: '4px',\n          background: `linear-gradient(90deg, ${color} 0%, ${alpha(color, 0.4)} 100%)`,\n        }\n      }}\n      onClick={onClick}\n      elevation={0}\n    >\n        <CardContent sx={{ p: { xs: 2, sm: 3 }, overflow: 'hidden' }}>\n          <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>\n            <Avatar\n              sx={{\n                bgcolor: alpha(color, 0.1),\n                color: color,\n                width: 56,\n                height: 56,\n                borderRadius: 2,\n                boxShadow: `0 4px 12px ${alpha(color, 0.2)}`,\n                '& .MuiSvgIcon-root': {\n                  fontSize: '1.75rem',\n                },\n              }}\n            >\n              {icon}\n            </Avatar>\n            {trend && (\n              <Chip\n                icon={trend === 'up' ? <TrendingUpIcon /> : <TrendingDownIcon />}\n                label={`${trendValue}%`}\n                size=\"small\"\n                color={trend === 'up' ? 'success' : 'error'}\n                variant=\"filled\"\n                sx={{\n                  fontWeight: 'bold',\n                  borderRadius: '20px',\n                  '& .MuiChip-icon': {\n                    fontSize: '1rem',\n                  }\n                }}\n              />\n            )}\n          </Box>\n\n          <Typography variant=\"h3\" component=\"div\" sx={{\n            fontWeight: 800,\n            mb: 0.5,\n            background: `linear-gradient(90deg, ${color} 0%, ${alpha(color, 0.7)} 100%)`,\n            WebkitBackgroundClip: 'text',\n            WebkitTextFillColor: 'transparent',\n            letterSpacing: '-0.025em',\n            fontSize: { xs: '1.75rem', sm: '2.25rem', md: '2.5rem' },\n            overflow: 'hidden',\n            textOverflow: 'ellipsis',\n            whiteSpace: 'nowrap',\n          }}>\n            {value}\n          </Typography>\n\n          <Typography variant=\"subtitle1\" sx={{\n            fontWeight: 600,\n            color: theme.palette.text.primary,\n            mb: 0.5,\n            overflow: 'hidden',\n            textOverflow: 'ellipsis',\n            whiteSpace: 'nowrap',\n          }}>\n            {title}\n          </Typography>\n\n          {subtitle && (\n            <Typography variant=\"body2\" sx={{\n              color: theme.palette.text.secondary,\n              display: 'block',\n              fontWeight: 500,\n              opacity: 0.8,\n              overflow: 'hidden',\n              textOverflow: 'ellipsis',\n              whiteSpace: 'nowrap',\n            }}>\n              {subtitle}\n            </Typography>\n          )}\n        </CardContent>\n      </Card>\n  );\n\n  if (loading) {\n    return (\n      <Box sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>\n        <CircularProgress size={60} thickness={4} sx={{ mb: 3 }} />\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          Loading dashboard...\n        </Typography>\n      </Box>\n    );\n  }\n\n  // Dashboard menu\n  const dashboardMenu = (\n    <Menu\n      anchorEl={menuAnchorEl}\n      open={Boolean(menuAnchorEl)}\n      onClose={handleMenuClose}\n      PaperProps={{\n        elevation: 3,\n        sx: {\n          overflow: 'visible',\n          filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.1))',\n          mt: 1.5,\n          '&:before': {\n            content: '\"\"',\n            display: 'block',\n            position: 'absolute',\n            top: 0,\n            right: 14,\n            width: 10,\n            height: 10,\n            bgcolor: 'background.paper',\n            transform: 'translateY(-50%) rotate(45deg)',\n            zIndex: 0,\n          },\n        },\n      }}\n    >\n      <MenuItem onClick={refreshData}>\n        <ListItemIcon>\n          <RefreshIcon fontSize=\"small\" />\n        </ListItemIcon>\n        <ListItemText>Refresh Data</ListItemText>\n      </MenuItem>\n      <MenuItem onClick={() => navigate('/organizations')}>\n        <ListItemIcon>\n          <BusinessIcon fontSize=\"small\" />\n        </ListItemIcon>\n        <ListItemText>Manage Organizations</ListItemText>\n      </MenuItem>\n      <MenuItem onClick={() => navigate('/settings')}>\n        <ListItemIcon>\n          <SettingsIcon fontSize=\"small\" />\n        </ListItemIcon>\n        <ListItemText>Dashboard Settings</ListItemText>\n      </MenuItem>\n    </Menu>\n  );\n\n  return (\n    <Box sx={{ width: '100%', maxWidth: '100%', overflow: 'hidden' }}>\n      {refreshing && (\n        <LinearProgress\n          sx={{\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            zIndex: 9999,\n            height: 3,\n          }}\n        />\n      )}\n\n      {organization && (\n        <Paper\n          elevation={3}\n          sx={{\n            p: { xs: 3, sm: 4 },\n            mb: 5,\n            borderRadius: 4,\n            position: 'relative',\n            overflow: 'hidden',\n            background: `radial-gradient(circle at 20% 40%, ${alpha(theme.palette.primary.light, 0.18)} 0%, transparent 60%),\n                         radial-gradient(circle at 80% 30%, ${alpha(theme.palette.secondary.light, 0.18)} 0%, transparent 60%),\n                         repeating-radial-gradient(circle at 50% 50%, ${alpha(theme.palette.info.light, 0.13)} 0px, transparent 2px, transparent 20px),\n                         linear-gradient(120deg, ${alpha(theme.palette.primary.main, 0.08)} 0%, ${alpha(theme.palette.secondary.main, 0.07)} 100%)`,\n            boxShadow: `0 8px 32px 0 ${alpha(theme.palette.primary.main, 0.13)}`,\n            border: `2.5px solid ${alpha(theme.palette.divider, 0.18)}`,\n            '&::before': {\n              content: '\"\"',\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              width: '100%',\n              height: '100%',\n              background: `repeating-radial-gradient(circle at 50% 50%, ${alpha(theme.palette.primary.main, 0.12)} 0px, transparent 2px, transparent 30px)`,\n              opacity: 0.7,\n              zIndex: 0,\n              pointerEvents: 'none',\n            },\n            '&::after': {\n              content: '\"\"',\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              width: '100%',\n              height: '100%',\n              background: `repeating-radial-gradient(circle at 50% 50%, ${alpha(theme.palette.secondary.main, 0.09)} 0px, transparent 1.5px, transparent 18px)`,\n              opacity: 0.5,\n              zIndex: 0,\n              pointerEvents: 'none',\n            },\n          }}\n        >\n          {/* Subtle sky/clouds accent shapes */}\n          <Box sx={{\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            width: '100%',\n            height: 160,\n            zIndex: 1,\n            opacity: 0.32,\n            pointerEvents: 'none',\n            overflow: 'hidden',\n          }}>\n            <svg width=\"100%\" height=\"160\" viewBox=\"0 0 800 160\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" style={{ minWidth: '100%' }}>\n              <defs>\n                <linearGradient id=\"skyGrad\" x1=\"0\" y1=\"0\" x2=\"0\" y2=\"1\">\n                  <stop offset=\"0%\" stopColor={alpha(theme.palette.info.light, 0.18)} />\n                  <stop offset=\"100%\" stopColor={alpha(theme.palette.primary.light, 0.10)} />\n                </linearGradient>\n                <radialGradient id=\"cloudWhite\" cx=\"0.5\" cy=\"0.5\" r=\"0.5\">\n                  <stop offset=\"0%\" stopColor={alpha('#fff', 0.7)} />\n                  <stop offset=\"100%\" stopColor={alpha('#fff', 0.0)} />\n                </radialGradient>\n              </defs>\n              {/* Sky background */}\n              <rect x=\"0\" y=\"0\" width=\"800\" height=\"160\" fill=\"url(#skyGrad)\" />\n              {/* Clouds */}\n              <ellipse cx=\"120\" cy=\"60\" rx=\"70\" ry=\"22\" fill=\"url(#cloudWhite)\" />\n              <ellipse cx=\"250\" cy=\"90\" rx=\"60\" ry=\"18\" fill=\"url(#cloudWhite)\" />\n              <ellipse cx=\"400\" cy=\"50\" rx=\"90\" ry=\"28\" fill=\"url(#cloudWhite)\" />\n              <ellipse cx=\"600\" cy=\"80\" rx=\"80\" ry=\"22\" fill=\"url(#cloudWhite)\" />\n              <ellipse cx=\"700\" cy=\"55\" rx=\"60\" ry=\"16\" fill=\"url(#cloudWhite)\" />\n            </svg>\n          </Box>\n          {/* End sky/clouds accent shapes */}\n          <Grid container spacing={{ xs: 2, sm: 3 }} alignItems=\"center\" sx={{ width: '100%', mx: 0, position: 'relative', zIndex: 2 }}>\n            <Grid item xs={12} sm=\"auto\">\n              {organization.logo_url && (\n                <Box\n                  component=\"img\"\n                  src={organization.logo_url}\n                  alt={organization.name}\n                  sx={{\n                    maxWidth: { xs: 140, sm: 180 },\n                    maxHeight: { xs: 80, sm: 100 },\n                    objectFit: 'contain',\n                    background: 'transparent',\n                    borderRadius: 2,\n                    boxShadow: '0 4px 24px 0 rgba(0,0,0,0.07)',\n                    p: 0,\n                    m: 0,\n                    display: 'block',\n                  }}\n                />\n              )}\n            </Grid>\n            <Grid item xs={12} sm>\n              <Typography\n                variant=\"h3\"\n                component=\"h1\"\n                gutterBottom\n                sx={{\n                  fontWeight: 900,\n                  fontSize: { xs: '2.2rem', sm: '2.8rem' },\n                  color: theme.palette.text.primary,\n                  letterSpacing: '-0.02em',\n                  textShadow: `0 2px 16px ${alpha(theme.palette.primary.main, 0.13)}`,\n                  mb: 0.5,\n                  overflow: 'hidden',\n                  textOverflow: 'ellipsis',\n                  maxWidth: '100%',\n                  background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                  display: 'inline-block',\n                }}\n              >\n                {organization.name}\n                {organization.shortcode && (\n                  <span style={{ fontWeight: 800, color: theme.palette.secondary.main }}>\n                    {' - '}{organization.shortcode}\n                  </span>\n                )}\n              </Typography>\n              {organization.motto && (\n                <Typography\n                  variant=\"h6\"\n                  color=\"text.secondary\"\n                  gutterBottom\n                  sx={{\n                    fontStyle: 'italic',\n                    fontWeight: 400,\n                    fontSize: { xs: '1.1rem', sm: '1.2rem' },\n                    opacity: 0.85,\n                    mb: 1,\n                    overflow: 'hidden',\n                    textOverflow: 'ellipsis',\n                    maxWidth: '100%',\n                  }}\n                >\n                  \"{organization.motto}\"\n                </Typography>\n              )}\n              {/* Only icon and value, no field labels */}\n              <Stack direction=\"row\" spacing={3} sx={{ mt: 1, flexWrap: 'wrap' }}>\n                {organization.website && (\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                    <LanguageIcon sx={{ fontSize: 22, color: theme.palette.text.secondary }} />\n                    <a href={organization.website} target=\"_blank\" rel=\"noopener noreferrer\" style={{ color: theme.palette.primary.main, textDecoration: 'underline', wordBreak: 'break-all', fontWeight: 600 }}>{organization.website}</a>\n                  </Box>\n                )}\n                {organization.phone && (\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                    <PhoneIcon sx={{ fontSize: 22, color: theme.palette.text.secondary }} />\n                    <span style={{ fontWeight: 600 }}>{organization.phone}</span>\n                  </Box>\n                )}\n                {organization.email && (\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                    <EmailIcon sx={{ fontSize: 22, color: theme.palette.text.secondary }} />\n                    <a href={`mailto:${organization.email}`} style={{ color: theme.palette.primary.main, textDecoration: 'underline', wordBreak: 'break-all', fontWeight: 600 }}>{organization.email}</a>\n                  </Box>\n                )}\n              </Stack>\n            </Grid>\n          </Grid>\n        </Paper>\n      )}\n\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography\n          variant=\"h5\"\n          component=\"h2\"\n          sx={{\n            fontWeight: 700,\n            position: 'relative',\n            display: 'inline-block',\n            '&::after': {\n              content: '\"\"',\n              position: 'absolute',\n              bottom: -8,\n              left: 0,\n              width: 60,\n              height: 4,\n              borderRadius: 2,\n              background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${alpha(theme.palette.primary.main, 0.4)} 100%)`,\n            }\n          }}\n        >\n          System Overview\n        </Typography>\n        <Tooltip title=\"Refresh Data\">\n          <IconButton onClick={refreshData} color=\"primary\" disabled={refreshing}>\n            <RefreshIcon />\n          </IconButton>\n        </Tooltip>\n      </Box>\n\n\n        <Grid container spacing={{ xs: 2, sm: 3 }} sx={{ mb: 4, width: '100%', mx: 0 }}>\n          <Grid item xs={12} sm={6} md={3}>\n            <StatCard\n              title=\"Organizations\"\n              value={stats.organizations}\n              icon={<BusinessIcon />}\n              color={theme.palette.primary.main}\n              onClick={() => navigate('/organizations')}\n              trend=\"up\"\n              trendValue=\"12\"\n              subtitle=\"Total registered organizations\"\n            />\n          </Grid>\n          <Grid item xs={12} sm={6} md={3}>\n            <StatCard\n              title=\"Offices\"\n              value={stats.offices}\n              icon={<LocationIcon />}\n              color={theme.palette.secondary.main}\n              onClick={() => navigate('/offices')}\n              trend=\"up\"\n              trendValue=\"5\"\n              subtitle=\"Across all organizations\"\n            />\n          </Grid>\n          <Grid item xs={12} sm={6} md={3}>\n            <StatCard\n              title=\"Classifications\"\n              value=\"24\"\n              icon={<CategoryIcon />}\n              color={theme.palette.warning.main}\n              onClick={() => navigate('/main-classifications')}\n              subtitle=\"Main & sub classifications\"\n            />\n          </Grid>\n          <Grid item xs={12} sm={6} md={3}>\n            <StatCard\n              title=\"Inventory Items\"\n              value=\"156\"\n              icon={<InventoryIcon />}\n              color={theme.palette.info.main}\n              trend=\"up\"\n              trendValue=\"8\"\n              subtitle=\"Total items in inventory\"\n            />\n          </Grid>\n        </Grid>\n\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography\n          variant=\"h5\"\n          component=\"h2\"\n          sx={{\n            fontWeight: 700,\n            position: 'relative',\n            display: 'inline-block',\n            '&::after': {\n              content: '\"\"',\n              position: 'absolute',\n              bottom: -8,\n              left: 0,\n              width: 60,\n              height: 4,\n              borderRadius: 2,\n              background: `linear-gradient(90deg, ${theme.palette.secondary.main} 0%, ${alpha(theme.palette.secondary.main, 0.4)} 100%)`,\n            }\n          }}\n        >\n          Gate Passes\n        </Typography>\n      </Box>\n\n        <Grid container spacing={{ xs: 2, sm: 3 }} sx={{ width: '100%', mx: 0 }}>\n          <Grid item xs={12} sm={6} md={4}>\n            <StatCard\n              title=\"Total Gate Passes\"\n              value={stats.gatePasses}\n              icon={<AssignmentIcon />}\n              color={theme.palette.info.dark}\n              onClick={() => navigate('/gate-passes')}\n              subtitle=\"All time gate passes\"\n            />\n          </Grid>\n          <Grid item xs={12} sm={6} md={4}>\n            <StatCard\n              title=\"Active Gate Passes\"\n              value={stats.activeGatePasses}\n              icon={<CheckCircleIcon />}\n              color={theme.palette.success.main}\n              onClick={() => navigate('/gate-passes')}\n              trend=\"up\"\n              trendValue=\"15\"\n              subtitle=\"Currently active passes\"\n            />\n          </Grid>\n          <Grid item xs={12} sm={6} md={4}>\n            <StatCard\n              title=\"Overdue Gate Passes\"\n              value={stats.overdueGatePasses}\n              icon={<WarningIcon />}\n              color={theme.palette.error.main}\n              onClick={() => navigate('/gate-passes')}\n              trend=\"down\"\n              trendValue=\"5\"\n              subtitle=\"Require immediate attention\"\n            />\n          </Grid>\n        </Grid>\n\n      {dashboardMenu}\n    </Box>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,OAAO,EACPC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,gBAAgB,EAChBC,QAAQ,EACRC,KAAK,EACLC,cAAc,EACdC,UAAU,EACVC,OAAO,EACPC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,QACP,eAAe;AACtB,OAAOC,eAAe,MAAM,kCAAkC;AAE9D,SACEC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,YAAY,EAC1BC,UAAU,IAAIC,cAAc,EAC5BC,SAAS,IAAIC,aAAa,EAC1BC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,SAAS,IAAIC,aAAa,EAC1BC,QAAQ,IAAIC,YAAY,EACxBC,OAAO,IAAIC,WAAW,EACtBC,UAAU,IAAIC,cAAc,EAC5BC,YAAY,IAAIC,gBAAgB,EAChCC,OAAO,IAAIC,WAAW,EACtBC,WAAW,IAAIC,eAAe,EAC9BC,KAAK,IAAIC,SAAS,EAClBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,aAAa,IAAIC,iBAAiB,EAClCC,QAAQ,IAAIC,YAAY,EACxBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,QACb,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,WAAW,QAAQ,qBAAqB;AACjD,OAAOC,GAAG,MAAM,mBAAmB;AACnC,SAASC,WAAW,QAAQ,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMxC,SAAS,GAAGA,CAAA,KAAM;EAAAyC,EAAA;EACtB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGlF,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACmF,OAAO,EAAEC,UAAU,CAAC,GAAGpF,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqF,UAAU,EAAEC,aAAa,CAAC,GAAGtF,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACuF,YAAY,EAAEC,eAAe,CAAC,GAAGxF,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACyF,WAAW,EAAEC,cAAc,CAAC,GAAG1F,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC2F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5F,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC6F,KAAK,EAAEC,QAAQ,CAAC,GAAG9F,QAAQ,CAAC;IACjC+F,aAAa,EAAE,CAAC;IAChBC,OAAO,EAAE,CAAC;IACVC,KAAK,EAAE,CAAC;IACRC,UAAU,EAAE,CAAC;IACbC,gBAAgB,EAAE,CAAC;IACnBC,iBAAiB,EAAE,CAAC;IACpBC,mBAAmB,EAAE,CAAC;IACtBC,oBAAoB,EAAE,CAAC;IACvBC,oBAAoB,EAAE;EACxB,CAAC,CAAC;EACF,MAAM;IAAEC;EAAgB,CAAC,GAAG3B,WAAW,CAAC,CAAC;EACzC,MAAM4B,QAAQ,GAAGhC,WAAW,CAAC,CAAC;EAC9B,MAAMiC,KAAK,GAAG3F,QAAQ,CAAC,CAAC;EAIxB,MAAM4F,cAAc,GAAIC,KAAK,IAAK;IAChCpB,eAAe,CAACoB,KAAK,CAACC,aAAa,CAAC;EACtC,CAAC;EAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5BtB,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMuB,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9BzB,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF,MAAM0B,iBAAiB,CAAC,CAAC;MACzB,MAAMC,UAAU,CAAC,CAAC;MAClB,MAAMC,qBAAqB,CAAC,CAAC;MAC7BV,eAAe,CAAC,0BAA0B,EAAE;QAAEW,OAAO,EAAE;MAAU,CAAC,CAAC;IACrE,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CZ,eAAe,CAAC,wBAAwB,EAAE;QAAEW,OAAO,EAAE;MAAQ,CAAC,CAAC;IACjE,CAAC,SAAS;MACR7B,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAM4B,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACF;MACA,IAAI,CAACzB,WAAW,EAAE;QAChB,MAAM6B,IAAI,GAAG,MAAM3C,WAAW,CAAC4C,cAAc,CAAC,CAAC;QAC/C7B,cAAc,CAAC4B,IAAI,CAAC;MACtB;;MAEA;MACA,MAAME,QAAQ,GAAG,MAAM9C,eAAe,CAAC,CAAC;MACxC,IAAI8C,QAAQ,IAAIA,QAAQ,CAACC,OAAO,EAAE;QAChC7B,mBAAmB,CAAC4B,QAAQ,CAACC,OAAO,CAAC;;QAErC;QACA,MAAMC,YAAY,GAAGF,QAAQ,CAACC,OAAO,CAACE,MAAM,CAACC,GAAG;UAAA,IAAAC,gBAAA;UAAA,QAAAA,gBAAA,GAC9CD,GAAG,CAACE,WAAW,cAAAD,gBAAA,uBAAfA,gBAAA,CAAiBE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,SAAS,CAAC;QAAA,EAAC,CAACC,MAAM;QAE5D,MAAMC,aAAa,GAAGV,QAAQ,CAACC,OAAO,CAACE,MAAM,CAACC,GAAG;UAAA,IAAAO,iBAAA,EAAAC,iBAAA;UAAA,OAC/C,EAAAD,iBAAA,GAAAP,GAAG,CAACE,WAAW,cAAAK,iBAAA,uBAAfA,iBAAA,CAAiBJ,WAAW,CAAC,CAAC,MAAK,UAAU,IAC7C,EAAAK,iBAAA,GAAAR,GAAG,CAACE,WAAW,cAAAM,iBAAA,uBAAfA,iBAAA,CAAiBL,WAAW,CAAC,CAAC,MAAK,aAAa;QAAA,EAAC,CAACE,MAAM;QAE1D,MAAMI,aAAa,GAAGb,QAAQ,CAACC,OAAO,CAACE,MAAM,CAACC,GAAG;UAAA,IAAAU,iBAAA;UAAA,OAC/C,EAAAA,iBAAA,GAAAV,GAAG,CAACE,WAAW,cAAAQ,iBAAA,uBAAfA,iBAAA,CAAiBP,WAAW,CAAC,CAAC,MAAK,UAAU;QAAA,EAAC,CAACE,MAAM;QAEvDnC,QAAQ,CAACyC,IAAI,KAAK;UAChB,GAAGA,IAAI;UACPlC,mBAAmB,EAAEqB,YAAY;UACjCpB,oBAAoB,EAAE4B,aAAa;UACnC3B,oBAAoB,EAAE8B;QACxB,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IAC3D;EACF,CAAC;EAED,MAAMJ,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF;MACA,IAAI;QACF,MAAMwB,YAAY,GAAG,MAAM5D,GAAG,CAAC6D,GAAG,CAAC,sBAAsB,CAAC;QAC1D,IAAID,YAAY,CAACE,IAAI,EAAE;UACrBxD,eAAe,CAACsD,YAAY,CAACE,IAAI,CAAC;UAClC,OAAO,CAAC;QACV;MACF,CAAC,CAAC,OAAOC,SAAS,EAAE;QAClBtB,OAAO,CAACuB,GAAG,CAAC,8DAA8D,CAAC;MAC7E;;MAEA;MACA,MAAMpB,QAAQ,GAAG,MAAM5C,GAAG,CAAC6D,GAAG,CAAC,iBAAiB,CAAC;MACjD,IAAII,KAAK,CAACC,OAAO,CAACtB,QAAQ,CAACkB,IAAI,CAAC,IAAIlB,QAAQ,CAACkB,IAAI,CAACT,MAAM,GAAG,CAAC,EAAE;QAC5D/C,eAAe,CAACsC,QAAQ,CAACkB,IAAI,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,MAAM,IAAIlB,QAAQ,CAACkB,IAAI,IAAIlB,QAAQ,CAACkB,IAAI,CAACjB,OAAO,IAAIoB,KAAK,CAACC,OAAO,CAACtB,QAAQ,CAACkB,IAAI,CAACjB,OAAO,CAAC,IAAID,QAAQ,CAACkB,IAAI,CAACjB,OAAO,CAACQ,MAAM,GAAG,CAAC,EAAE;QAC7H/C,eAAe,CAACsC,QAAQ,CAACkB,IAAI,CAACjB,OAAO,CAAC,CAAC,CAAC,CAAC;MAC3C,CAAC,MAAM;QACLJ,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAC;MACzC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMH,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF;MACA,MAAM,CAAC8B,YAAY,EAAEC,eAAe,EAAE9C,UAAU,EAAEC,gBAAgB,EAAEC,iBAAiB,CAAC,GAAG,MAAM6C,OAAO,CAACC,GAAG,CAAC,CACzGtE,GAAG,CAAC6D,GAAG,CAAC,iBAAiB,CAAC,EAC1B7D,GAAG,CAAC6D,GAAG,CAAC,WAAW,CAAC,EACpB7D,GAAG,CAAC6D,GAAG,CAAC,eAAe,CAAC,EACxB7D,GAAG,CAAC6D,GAAG,CAAC,sBAAsB,CAAC,EAC/B7D,GAAG,CAAC6D,GAAG,CAAC,uBAAuB,CAAC,CACjC,CAAC;MAEF3C,QAAQ,CAAC;QACPC,aAAa,EAAE8C,KAAK,CAACC,OAAO,CAACC,YAAY,CAACL,IAAI,CAAC,GAAGK,YAAY,CAACL,IAAI,CAACT,MAAM,GAAG,CAAC;QAC9EjC,OAAO,EAAE6C,KAAK,CAACC,OAAO,CAACE,eAAe,CAACN,IAAI,CAAC,GAAGM,eAAe,CAACN,IAAI,CAACT,MAAM,GAAG,CAAC;QAC9E/B,UAAU,EAAE2C,KAAK,CAACC,OAAO,CAAC5C,UAAU,CAACwC,IAAI,CAAC,GAAGxC,UAAU,CAACwC,IAAI,CAACT,MAAM,GAAG,CAAC;QACvE9B,gBAAgB,EAAE0C,KAAK,CAACC,OAAO,CAAC3C,gBAAgB,CAACuC,IAAI,CAAC,GAAGvC,gBAAgB,CAACuC,IAAI,CAACT,MAAM,GAAG,CAAC;QACzF7B,iBAAiB,EAAEyC,KAAK,CAACC,OAAO,CAAC1C,iBAAiB,CAACsC,IAAI,CAAC,GAAGtC,iBAAiB,CAACsC,IAAI,CAACT,MAAM,GAAG;MAC7F,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK;IACb;EACF,CAAC;EAEDnH,SAAS,CAAC,MAAM;IACd,MAAMkJ,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,IAAI;QACF,MAAMnC,iBAAiB,CAAC,CAAC;QACzB,MAAMC,UAAU,CAAC,CAAC;QAClB,MAAMC,qBAAqB,CAAC,CAAC;MAC/B,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdZ,eAAe,CAAC,uCAAuC,EAAE;UAAEW,OAAO,EAAE;QAAQ,CAAC,CAAC;MAChF,CAAC,SAAS;QACR/B,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED+D,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAAC3C,eAAe,CAAC,CAAC;EAErB,MAAM4C,QAAQ,GAAGA,CAAC;IAAEC,KAAK;IAAEC,KAAK;IAAEC,IAAI;IAAEC,KAAK;IAAEC,OAAO;IAAEC,KAAK;IAAEC,UAAU;IAAEC;EAAS,CAAC,kBACnF7E,OAAA,CAACzE,IAAI;IACHuJ,EAAE,EAAE;MACFC,MAAM,EAAE,MAAM;MACdC,MAAM,EAAEN,OAAO,GAAG,SAAS,GAAG,SAAS;MACvCO,UAAU,EAAE,eAAe;MAC3BC,YAAY,EAAE,CAAC;MACfC,QAAQ,EAAE,QAAQ;MAClBC,QAAQ,EAAE,UAAU;MACpBC,MAAM,EAAE,+BAA+B;MACvCC,UAAU,EAAE,qFAAqF;MACjGC,cAAc,EAAE,YAAY;MAC5B,SAAS,EAAEb,OAAO,GAAG;QACnBc,SAAS,EAAE,kBAAkB;QAC7BC,SAAS,EAAE,eAAexJ,KAAK,CAAC0F,KAAK,CAAC+D,OAAO,CAACC,MAAM,CAACC,KAAK,EAAE,GAAG,CAAC;MAClE,CAAC,GAAG,CAAC,CAAC;MACN,UAAU,EAAE;QACVC,OAAO,EAAE,IAAI;QACbT,QAAQ,EAAE,UAAU;QACpBU,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,MAAM;QACbjB,MAAM,EAAE,KAAK;QACbO,UAAU,EAAE,0BAA0Bb,KAAK,QAAQxI,KAAK,CAACwI,KAAK,EAAE,GAAG,CAAC;MACtE;IACF,CAAE;IACFC,OAAO,EAAEA,OAAQ;IACjBuB,SAAS,EAAE,CAAE;IAAAC,QAAA,eAEXlG,OAAA,CAACxE,WAAW;MAACsJ,EAAE,EAAE;QAAEqB,CAAC,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAC;QAAElB,QAAQ,EAAE;MAAS,CAAE;MAAAe,QAAA,gBAC3DlG,OAAA,CAAC7E,GAAG;QAAC2J,EAAE,EAAE;UAAEwB,EAAE,EAAE,CAAC;UAAEC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE;QAAa,CAAE;QAAAP,QAAA,gBAC7FlG,OAAA,CAACtE,MAAM;UACLoJ,EAAE,EAAE;YACF4B,OAAO,EAAEzK,KAAK,CAACwI,KAAK,EAAE,GAAG,CAAC;YAC1BA,KAAK,EAAEA,KAAK;YACZuB,KAAK,EAAE,EAAE;YACTjB,MAAM,EAAE,EAAE;YACVG,YAAY,EAAE,CAAC;YACfO,SAAS,EAAE,cAAcxJ,KAAK,CAACwI,KAAK,EAAE,GAAG,CAAC,EAAE;YAC5C,oBAAoB,EAAE;cACpBkC,QAAQ,EAAE;YACZ;UACF,CAAE;UAAAT,QAAA,EAED1B;QAAI;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EACRpC,KAAK,iBACJ3E,OAAA,CAACpE,IAAI;UACH4I,IAAI,EAAEG,KAAK,KAAK,IAAI,gBAAG3E,OAAA,CAACjC,cAAc;YAAA6I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG/G,OAAA,CAAC/B,gBAAgB;YAAA2I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACjEC,KAAK,EAAE,GAAGpC,UAAU,GAAI;UACxBqC,IAAI,EAAC,OAAO;UACZxC,KAAK,EAAEE,KAAK,KAAK,IAAI,GAAG,SAAS,GAAG,OAAQ;UAC5CvC,OAAO,EAAC,QAAQ;UAChB0C,EAAE,EAAE;YACFoC,UAAU,EAAE,MAAM;YAClBhC,YAAY,EAAE,MAAM;YACpB,iBAAiB,EAAE;cACjByB,QAAQ,EAAE;YACZ;UACF;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN/G,OAAA,CAAC5E,UAAU;QAACgH,OAAO,EAAC,IAAI;QAAC+E,SAAS,EAAC,KAAK;QAACrC,EAAE,EAAE;UAC3CoC,UAAU,EAAE,GAAG;UACfZ,EAAE,EAAE,GAAG;UACPhB,UAAU,EAAE,0BAA0Bb,KAAK,QAAQxI,KAAK,CAACwI,KAAK,EAAE,GAAG,CAAC,QAAQ;UAC5E2C,oBAAoB,EAAE,MAAM;UAC5BC,mBAAmB,EAAE,aAAa;UAClCC,aAAa,EAAE,UAAU;UACzBX,QAAQ,EAAE;YAAEP,EAAE,EAAE,SAAS;YAAEC,EAAE,EAAE,SAAS;YAAEkB,EAAE,EAAE;UAAS,CAAC;UACxDpC,QAAQ,EAAE,QAAQ;UAClBqC,YAAY,EAAE,UAAU;UACxBC,UAAU,EAAE;QACd,CAAE;QAAAvB,QAAA,EACC3B;MAAK;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAEb/G,OAAA,CAAC5E,UAAU;QAACgH,OAAO,EAAC,WAAW;QAAC0C,EAAE,EAAE;UAClCoC,UAAU,EAAE,GAAG;UACfzC,KAAK,EAAE9C,KAAK,CAAC+D,OAAO,CAACgC,IAAI,CAACC,OAAO;UACjCrB,EAAE,EAAE,GAAG;UACPnB,QAAQ,EAAE,QAAQ;UAClBqC,YAAY,EAAE,UAAU;UACxBC,UAAU,EAAE;QACd,CAAE;QAAAvB,QAAA,EACC5B;MAAK;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,EAEZlC,QAAQ,iBACP7E,OAAA,CAAC5E,UAAU;QAACgH,OAAO,EAAC,OAAO;QAAC0C,EAAE,EAAE;UAC9BL,KAAK,EAAE9C,KAAK,CAAC+D,OAAO,CAACgC,IAAI,CAACE,SAAS;UACnCrB,OAAO,EAAE,OAAO;UAChBW,UAAU,EAAE,GAAG;UACfW,OAAO,EAAE,GAAG;UACZ1C,QAAQ,EAAE,QAAQ;UAClBqC,YAAY,EAAE,UAAU;UACxBC,UAAU,EAAE;QACd,CAAE;QAAAvB,QAAA,EACCrB;MAAQ;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACT;EAED,IAAI3G,OAAO,EAAE;IACX,oBACEJ,OAAA,CAAC7E,GAAG;MAAC2J,EAAE,EAAE;QAAEyB,OAAO,EAAE,MAAM;QAAEuB,aAAa,EAAE,QAAQ;QAAEtB,cAAc,EAAE,QAAQ;QAAEC,UAAU,EAAE,QAAQ;QAAE1B,MAAM,EAAE;MAAO,CAAE;MAAAmB,QAAA,gBACpHlG,OAAA,CAACjE,gBAAgB;QAACkL,IAAI,EAAE,EAAG;QAACc,SAAS,EAAE,CAAE;QAACjD,EAAE,EAAE;UAAEwB,EAAE,EAAE;QAAE;MAAE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3D/G,OAAA,CAAC5E,UAAU;QAACgH,OAAO,EAAC,IAAI;QAACqC,KAAK,EAAC,gBAAgB;QAAAyB,QAAA,EAAC;MAEhD;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;;EAEA;EACA,MAAMiB,aAAa,gBACjBhI,OAAA,CAAC3D,IAAI;IACH4L,QAAQ,EAAEzH,YAAa;IACvB0H,IAAI,EAAEC,OAAO,CAAC3H,YAAY,CAAE;IAC5B4H,OAAO,EAAErG,eAAgB;IACzBsG,UAAU,EAAE;MACVpC,SAAS,EAAE,CAAC;MACZnB,EAAE,EAAE;QACFK,QAAQ,EAAE,SAAS;QACnBvC,MAAM,EAAE,0CAA0C;QAClD0F,EAAE,EAAE,GAAG;QACP,UAAU,EAAE;UACVzC,OAAO,EAAE,IAAI;UACbU,OAAO,EAAE,OAAO;UAChBnB,QAAQ,EAAE,UAAU;UACpBU,GAAG,EAAE,CAAC;UACNyC,KAAK,EAAE,EAAE;UACTvC,KAAK,EAAE,EAAE;UACTjB,MAAM,EAAE,EAAE;UACV2B,OAAO,EAAE,kBAAkB;UAC3BlB,SAAS,EAAE,gCAAgC;UAC3CgD,MAAM,EAAE;QACV;MACF;IACF,CAAE;IAAAtC,QAAA,gBAEFlG,OAAA,CAAC1D,QAAQ;MAACoI,OAAO,EAAE1C,WAAY;MAAAkE,QAAA,gBAC7BlG,OAAA,CAACzD,YAAY;QAAA2J,QAAA,eACXlG,OAAA,CAACnC,WAAW;UAAC8I,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC,eACf/G,OAAA,CAACxD,YAAY;QAAA0J,QAAA,EAAC;MAAY;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC,eACX/G,OAAA,CAAC1D,QAAQ;MAACoI,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,gBAAgB,CAAE;MAAAwE,QAAA,gBAClDlG,OAAA,CAACzD,YAAY;QAAA2J,QAAA,eACXlG,OAAA,CAACrD,YAAY;UAACgK,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,eACf/G,OAAA,CAACxD,YAAY;QAAA0J,QAAA,EAAC;MAAoB;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC,CAAC,eACX/G,OAAA,CAAC1D,QAAQ;MAACoI,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,WAAW,CAAE;MAAAwE,QAAA,gBAC7ClG,OAAA,CAACzD,YAAY;QAAA2J,QAAA,eACXlG,OAAA,CAACrB,YAAY;UAACgI,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,eACf/G,OAAA,CAACxD,YAAY;QAAA0J,QAAA,EAAC;MAAkB;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CACP;EAED,oBACE/G,OAAA,CAAC7E,GAAG;IAAC2J,EAAE,EAAE;MAAEkB,KAAK,EAAE,MAAM;MAAEyC,QAAQ,EAAE,MAAM;MAAEtD,QAAQ,EAAE;IAAS,CAAE;IAAAe,QAAA,GAC9D5F,UAAU,iBACTN,OAAA,CAAC9D,cAAc;MACb4I,EAAE,EAAE;QACFM,QAAQ,EAAE,UAAU;QACpBU,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPwC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,IAAI;QACZzD,MAAM,EAAE;MACV;IAAE;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF,EAEA7G,YAAY,iBACXF,OAAA,CAAC3E,KAAK;MACJ4K,SAAS,EAAE,CAAE;MACbnB,EAAE,EAAE;QACFqB,CAAC,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAC;QACnBC,EAAE,EAAE,CAAC;QACLpB,YAAY,EAAE,CAAC;QACfE,QAAQ,EAAE,UAAU;QACpBD,QAAQ,EAAE,QAAQ;QAClBG,UAAU,EAAE,sCAAsCrJ,KAAK,CAAC0F,KAAK,CAAC+D,OAAO,CAACiC,OAAO,CAACe,KAAK,EAAE,IAAI,CAAC;AACtG,8DAA8DzM,KAAK,CAAC0F,KAAK,CAAC+D,OAAO,CAACkC,SAAS,CAACc,KAAK,EAAE,IAAI,CAAC;AACxG,wEAAwEzM,KAAK,CAAC0F,KAAK,CAAC+D,OAAO,CAACiD,IAAI,CAACD,KAAK,EAAE,IAAI,CAAC;AAC7G,mDAAmDzM,KAAK,CAAC0F,KAAK,CAAC+D,OAAO,CAACiC,OAAO,CAACiB,IAAI,EAAE,IAAI,CAAC,QAAQ3M,KAAK,CAAC0F,KAAK,CAAC+D,OAAO,CAACkC,SAAS,CAACgB,IAAI,EAAE,IAAI,CAAC,QAAQ;QACvInD,SAAS,EAAE,gBAAgBxJ,KAAK,CAAC0F,KAAK,CAAC+D,OAAO,CAACiC,OAAO,CAACiB,IAAI,EAAE,IAAI,CAAC,EAAE;QACpEvD,MAAM,EAAE,eAAepJ,KAAK,CAAC0F,KAAK,CAAC+D,OAAO,CAACmD,OAAO,EAAE,IAAI,CAAC,EAAE;QAC3D,WAAW,EAAE;UACXhD,OAAO,EAAE,IAAI;UACbT,QAAQ,EAAE,UAAU;UACpBU,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,MAAM;UACbjB,MAAM,EAAE,MAAM;UACdO,UAAU,EAAE,gDAAgDrJ,KAAK,CAAC0F,KAAK,CAAC+D,OAAO,CAACiC,OAAO,CAACiB,IAAI,EAAE,IAAI,CAAC,0CAA0C;UAC7If,OAAO,EAAE,GAAG;UACZW,MAAM,EAAE,CAAC;UACTM,aAAa,EAAE;QACjB,CAAC;QACD,UAAU,EAAE;UACVjD,OAAO,EAAE,IAAI;UACbT,QAAQ,EAAE,UAAU;UACpBU,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,MAAM;UACbjB,MAAM,EAAE,MAAM;UACdO,UAAU,EAAE,gDAAgDrJ,KAAK,CAAC0F,KAAK,CAAC+D,OAAO,CAACkC,SAAS,CAACgB,IAAI,EAAE,IAAI,CAAC,4CAA4C;UACjJf,OAAO,EAAE,GAAG;UACZW,MAAM,EAAE,CAAC;UACTM,aAAa,EAAE;QACjB;MACF,CAAE;MAAA5C,QAAA,gBAGFlG,OAAA,CAAC7E,GAAG;QAAC2J,EAAE,EAAE;UACPM,QAAQ,EAAE,UAAU;UACpBU,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,MAAM;UACbjB,MAAM,EAAE,GAAG;UACXyD,MAAM,EAAE,CAAC;UACTX,OAAO,EAAE,IAAI;UACbiB,aAAa,EAAE,MAAM;UACrB3D,QAAQ,EAAE;QACZ,CAAE;QAAAe,QAAA,eACAlG,OAAA;UAAKgG,KAAK,EAAC,MAAM;UAACjB,MAAM,EAAC,KAAK;UAACgE,OAAO,EAAC,aAAa;UAACC,IAAI,EAAC,MAAM;UAACC,KAAK,EAAC,4BAA4B;UAACC,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAjD,QAAA,gBAC9HlG,OAAA;YAAAkG,QAAA,gBACElG,OAAA;cAAgBoJ,EAAE,EAAC,SAAS;cAACC,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC,GAAG;cAAAtD,QAAA,gBACtDlG,OAAA;gBAAMyJ,MAAM,EAAC,IAAI;gBAACC,SAAS,EAAEzN,KAAK,CAAC0F,KAAK,CAAC+D,OAAO,CAACiD,IAAI,CAACD,KAAK,EAAE,IAAI;cAAE;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtE/G,OAAA;gBAAMyJ,MAAM,EAAC,MAAM;gBAACC,SAAS,EAAEzN,KAAK,CAAC0F,KAAK,CAAC+D,OAAO,CAACiC,OAAO,CAACe,KAAK,EAAE,IAAI;cAAE;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,eACjB/G,OAAA;cAAgBoJ,EAAE,EAAC,YAAY;cAACO,EAAE,EAAC,KAAK;cAACC,EAAE,EAAC,KAAK;cAACC,CAAC,EAAC,KAAK;cAAA3D,QAAA,gBACvDlG,OAAA;gBAAMyJ,MAAM,EAAC,IAAI;gBAACC,SAAS,EAAEzN,KAAK,CAAC,MAAM,EAAE,GAAG;cAAE;gBAAA2K,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnD/G,OAAA;gBAAMyJ,MAAM,EAAC,MAAM;gBAACC,SAAS,EAAEzN,KAAK,CAAC,MAAM,EAAE,GAAG;cAAE;gBAAA2K,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eAEP/G,OAAA;YAAM8J,CAAC,EAAC,GAAG;YAACC,CAAC,EAAC,GAAG;YAAC/D,KAAK,EAAC,KAAK;YAACjB,MAAM,EAAC,KAAK;YAACiE,IAAI,EAAC;UAAe;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAElE/G,OAAA;YAAS2J,EAAE,EAAC,KAAK;YAACC,EAAE,EAAC,IAAI;YAACI,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACjB,IAAI,EAAC;UAAkB;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpE/G,OAAA;YAAS2J,EAAE,EAAC,KAAK;YAACC,EAAE,EAAC,IAAI;YAACI,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACjB,IAAI,EAAC;UAAkB;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpE/G,OAAA;YAAS2J,EAAE,EAAC,KAAK;YAACC,EAAE,EAAC,IAAI;YAACI,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACjB,IAAI,EAAC;UAAkB;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpE/G,OAAA;YAAS2J,EAAE,EAAC,KAAK;YAACC,EAAE,EAAC,IAAI;YAACI,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACjB,IAAI,EAAC;UAAkB;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpE/G,OAAA;YAAS2J,EAAE,EAAC,KAAK;YAACC,EAAE,EAAC,IAAI;YAACI,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACjB,IAAI,EAAC;UAAkB;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/G,OAAA,CAAC1E,IAAI;QAAC4O,SAAS;QAACC,OAAO,EAAE;UAAE/D,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAACI,UAAU,EAAC,QAAQ;QAAC3B,EAAE,EAAE;UAAEkB,KAAK,EAAE,MAAM;UAAEoE,EAAE,EAAE,CAAC;UAAEhF,QAAQ,EAAE,UAAU;UAAEoD,MAAM,EAAE;QAAE,CAAE;QAAAtC,QAAA,gBAC3HlG,OAAA,CAAC1E,IAAI;UAAC+O,IAAI;UAACjE,EAAE,EAAE,EAAG;UAACC,EAAE,EAAC,MAAM;UAAAH,QAAA,EACzBhG,YAAY,CAACoK,QAAQ,iBACpBtK,OAAA,CAAC7E,GAAG;YACFgM,SAAS,EAAC,KAAK;YACfoD,GAAG,EAAErK,YAAY,CAACoK,QAAS;YAC3BE,GAAG,EAAEtK,YAAY,CAACuK,IAAK;YACvB3F,EAAE,EAAE;cACF2D,QAAQ,EAAE;gBAAErC,EAAE,EAAE,GAAG;gBAAEC,EAAE,EAAE;cAAI,CAAC;cAC9BqE,SAAS,EAAE;gBAAEtE,EAAE,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAI,CAAC;cAC9BsE,SAAS,EAAE,SAAS;cACpBrF,UAAU,EAAE,aAAa;cACzBJ,YAAY,EAAE,CAAC;cACfO,SAAS,EAAE,+BAA+B;cAC1CU,CAAC,EAAE,CAAC;cACJyE,CAAC,EAAE,CAAC;cACJrE,OAAO,EAAE;YACX;UAAE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACP/G,OAAA,CAAC1E,IAAI;UAAC+O,IAAI;UAACjE,EAAE,EAAE,EAAG;UAACC,EAAE;UAAAH,QAAA,gBACnBlG,OAAA,CAAC5E,UAAU;YACTgH,OAAO,EAAC,IAAI;YACZ+E,SAAS,EAAC,IAAI;YACd0D,YAAY;YACZ/F,EAAE,EAAE;cACFoC,UAAU,EAAE,GAAG;cACfP,QAAQ,EAAE;gBAAEP,EAAE,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAS,CAAC;cACxC5B,KAAK,EAAE9C,KAAK,CAAC+D,OAAO,CAACgC,IAAI,CAACC,OAAO;cACjCL,aAAa,EAAE,SAAS;cACxBwD,UAAU,EAAE,cAAc7O,KAAK,CAAC0F,KAAK,CAAC+D,OAAO,CAACiC,OAAO,CAACiB,IAAI,EAAE,IAAI,CAAC,EAAE;cACnEtC,EAAE,EAAE,GAAG;cACPnB,QAAQ,EAAE,QAAQ;cAClBqC,YAAY,EAAE,UAAU;cACxBiB,QAAQ,EAAE,MAAM;cAChBnD,UAAU,EAAE,0BAA0B3D,KAAK,CAAC+D,OAAO,CAACiC,OAAO,CAACiB,IAAI,QAAQjH,KAAK,CAAC+D,OAAO,CAACkC,SAAS,CAACgB,IAAI,QAAQ;cAC5GxB,oBAAoB,EAAE,MAAM;cAC5BC,mBAAmB,EAAE,aAAa;cAClCd,OAAO,EAAE;YACX,CAAE;YAAAL,QAAA,GAEDhG,YAAY,CAACuK,IAAI,EACjBvK,YAAY,CAAC6K,SAAS,iBACrB/K,OAAA;cAAMkJ,KAAK,EAAE;gBAAEhC,UAAU,EAAE,GAAG;gBAAEzC,KAAK,EAAE9C,KAAK,CAAC+D,OAAO,CAACkC,SAAS,CAACgB;cAAK,CAAE;cAAA1C,QAAA,GACnE,KAAK,EAAEhG,YAAY,CAAC6K,SAAS;YAAA;cAAAnE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC,EACZ7G,YAAY,CAAC8K,KAAK,iBACjBhL,OAAA,CAAC5E,UAAU;YACTgH,OAAO,EAAC,IAAI;YACZqC,KAAK,EAAC,gBAAgB;YACtBoG,YAAY;YACZ/F,EAAE,EAAE;cACFmG,SAAS,EAAE,QAAQ;cACnB/D,UAAU,EAAE,GAAG;cACfP,QAAQ,EAAE;gBAAEP,EAAE,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAS,CAAC;cACxCwB,OAAO,EAAE,IAAI;cACbvB,EAAE,EAAE,CAAC;cACLnB,QAAQ,EAAE,QAAQ;cAClBqC,YAAY,EAAE,UAAU;cACxBiB,QAAQ,EAAE;YACZ,CAAE;YAAAvC,QAAA,GACH,IACE,EAAChG,YAAY,CAAC8K,KAAK,EAAC,IACvB;UAAA;YAAApE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CACb,eAED/G,OAAA,CAACnE,KAAK;YAACqP,SAAS,EAAC,KAAK;YAACf,OAAO,EAAE,CAAE;YAACrF,EAAE,EAAE;cAAEwD,EAAE,EAAE,CAAC;cAAE6C,QAAQ,EAAE;YAAO,CAAE;YAAAjF,QAAA,GAChEhG,YAAY,CAACkL,OAAO,iBACnBpL,OAAA,CAAC7E,GAAG;cAAC2J,EAAE,EAAE;gBAAEyB,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAE4E,GAAG,EAAE;cAAE,CAAE;cAAAnF,QAAA,gBACzDlG,OAAA,CAACX,YAAY;gBAACyF,EAAE,EAAE;kBAAE6B,QAAQ,EAAE,EAAE;kBAAElC,KAAK,EAAE9C,KAAK,CAAC+D,OAAO,CAACgC,IAAI,CAACE;gBAAU;cAAE;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3E/G,OAAA;gBAAGsL,IAAI,EAAEpL,YAAY,CAACkL,OAAQ;gBAACG,MAAM,EAAC,QAAQ;gBAACC,GAAG,EAAC,qBAAqB;gBAACtC,KAAK,EAAE;kBAAEzE,KAAK,EAAE9C,KAAK,CAAC+D,OAAO,CAACiC,OAAO,CAACiB,IAAI;kBAAE6C,cAAc,EAAE,WAAW;kBAAEC,SAAS,EAAE,WAAW;kBAAExE,UAAU,EAAE;gBAAI,CAAE;gBAAAhB,QAAA,EAAEhG,YAAY,CAACkL;cAAO;gBAAAxE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpN,CACN,EACA7G,YAAY,CAACyL,KAAK,iBACjB3L,OAAA,CAAC7E,GAAG;cAAC2J,EAAE,EAAE;gBAAEyB,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAE4E,GAAG,EAAE;cAAE,CAAE;cAAAnF,QAAA,gBACzDlG,OAAA,CAACT,SAAS;gBAACuF,EAAE,EAAE;kBAAE6B,QAAQ,EAAE,EAAE;kBAAElC,KAAK,EAAE9C,KAAK,CAAC+D,OAAO,CAACgC,IAAI,CAACE;gBAAU;cAAE;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxE/G,OAAA;gBAAMkJ,KAAK,EAAE;kBAAEhC,UAAU,EAAE;gBAAI,CAAE;gBAAAhB,QAAA,EAAEhG,YAAY,CAACyL;cAAK;gBAAA/E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CACN,EACA7G,YAAY,CAAC0L,KAAK,iBACjB5L,OAAA,CAAC7E,GAAG;cAAC2J,EAAE,EAAE;gBAAEyB,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAE4E,GAAG,EAAE;cAAE,CAAE;cAAAnF,QAAA,gBACzDlG,OAAA,CAACP,SAAS;gBAACqF,EAAE,EAAE;kBAAE6B,QAAQ,EAAE,EAAE;kBAAElC,KAAK,EAAE9C,KAAK,CAAC+D,OAAO,CAACgC,IAAI,CAACE;gBAAU;cAAE;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxE/G,OAAA;gBAAGsL,IAAI,EAAE,UAAUpL,YAAY,CAAC0L,KAAK,EAAG;gBAAC1C,KAAK,EAAE;kBAAEzE,KAAK,EAAE9C,KAAK,CAAC+D,OAAO,CAACiC,OAAO,CAACiB,IAAI;kBAAE6C,cAAc,EAAE,WAAW;kBAAEC,SAAS,EAAE,WAAW;kBAAExE,UAAU,EAAE;gBAAI,CAAE;gBAAAhB,QAAA,EAAEhG,YAAY,CAAC0L;cAAK;gBAAAhF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClL,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACR,eAED/G,OAAA,CAAC7E,GAAG;MAAC2J,EAAE,EAAE;QAAEyB,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEH,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACzFlG,OAAA,CAAC5E,UAAU;QACTgH,OAAO,EAAC,IAAI;QACZ+E,SAAS,EAAC,IAAI;QACdrC,EAAE,EAAE;UACFoC,UAAU,EAAE,GAAG;UACf9B,QAAQ,EAAE,UAAU;UACpBmB,OAAO,EAAE,cAAc;UACvB,UAAU,EAAE;YACVV,OAAO,EAAE,IAAI;YACbT,QAAQ,EAAE,UAAU;YACpByG,MAAM,EAAE,CAAC,CAAC;YACV9F,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,EAAE;YACTjB,MAAM,EAAE,CAAC;YACTG,YAAY,EAAE,CAAC;YACfI,UAAU,EAAE,0BAA0B3D,KAAK,CAAC+D,OAAO,CAACiC,OAAO,CAACiB,IAAI,QAAQ3M,KAAK,CAAC0F,KAAK,CAAC+D,OAAO,CAACiC,OAAO,CAACiB,IAAI,EAAE,GAAG,CAAC;UAChH;QACF,CAAE;QAAA1C,QAAA,EACH;MAED;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb/G,OAAA,CAAC5D,OAAO;QAACkI,KAAK,EAAC,cAAc;QAAA4B,QAAA,eAC3BlG,OAAA,CAAC7D,UAAU;UAACuI,OAAO,EAAE1C,WAAY;UAACyC,KAAK,EAAC,SAAS;UAACqH,QAAQ,EAAExL,UAAW;UAAA4F,QAAA,eACrElG,OAAA,CAACnC,WAAW;YAAA+I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGJ/G,OAAA,CAAC1E,IAAI;MAAC4O,SAAS;MAACC,OAAO,EAAE;QAAE/D,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAACvB,EAAE,EAAE;QAAEwB,EAAE,EAAE,CAAC;QAAEN,KAAK,EAAE,MAAM;QAAEoE,EAAE,EAAE;MAAE,CAAE;MAAAlE,QAAA,gBAC7ElG,OAAA,CAAC1E,IAAI;QAAC+O,IAAI;QAACjE,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACkB,EAAE,EAAE,CAAE;QAAArB,QAAA,eAC9BlG,OAAA,CAACqE,QAAQ;UACPC,KAAK,EAAC,eAAe;UACrBC,KAAK,EAAEzD,KAAK,CAACE,aAAc;UAC3BwD,IAAI,eAAExE,OAAA,CAACrD,YAAY;YAAAiK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBtC,KAAK,EAAE9C,KAAK,CAAC+D,OAAO,CAACiC,OAAO,CAACiB,IAAK;UAClClE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,gBAAgB,CAAE;UAC1CiD,KAAK,EAAC,IAAI;UACVC,UAAU,EAAC,IAAI;UACfC,QAAQ,EAAC;QAAgC;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACP/G,OAAA,CAAC1E,IAAI;QAAC+O,IAAI;QAACjE,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACkB,EAAE,EAAE,CAAE;QAAArB,QAAA,eAC9BlG,OAAA,CAACqE,QAAQ;UACPC,KAAK,EAAC,SAAS;UACfC,KAAK,EAAEzD,KAAK,CAACG,OAAQ;UACrBuD,IAAI,eAAExE,OAAA,CAACjD,YAAY;YAAA6J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBtC,KAAK,EAAE9C,KAAK,CAAC+D,OAAO,CAACkC,SAAS,CAACgB,IAAK;UACpClE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,UAAU,CAAE;UACpCiD,KAAK,EAAC,IAAI;UACVC,UAAU,EAAC,GAAG;UACdC,QAAQ,EAAC;QAA0B;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACP/G,OAAA,CAAC1E,IAAI;QAAC+O,IAAI;QAACjE,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACkB,EAAE,EAAE,CAAE;QAAArB,QAAA,eAC9BlG,OAAA,CAACqE,QAAQ;UACPC,KAAK,EAAC,iBAAiB;UACvBC,KAAK,EAAC,IAAI;UACVC,IAAI,eAAExE,OAAA,CAACnD,YAAY;YAAA+J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBtC,KAAK,EAAE9C,KAAK,CAAC+D,OAAO,CAACqG,OAAO,CAACnD,IAAK;UAClClE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,uBAAuB,CAAE;UACjDmD,QAAQ,EAAC;QAA4B;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACP/G,OAAA,CAAC1E,IAAI;QAAC+O,IAAI;QAACjE,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACkB,EAAE,EAAE,CAAE;QAAArB,QAAA,eAC9BlG,OAAA,CAACqE,QAAQ;UACPC,KAAK,EAAC,iBAAiB;UACvBC,KAAK,EAAC,KAAK;UACXC,IAAI,eAAExE,OAAA,CAAC7C,aAAa;YAAAyJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBtC,KAAK,EAAE9C,KAAK,CAAC+D,OAAO,CAACiD,IAAI,CAACC,IAAK;UAC/BjE,KAAK,EAAC,IAAI;UACVC,UAAU,EAAC,GAAG;UACdC,QAAQ,EAAC;QAA0B;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAET/G,OAAA,CAAC7E,GAAG;MAAC2J,EAAE,EAAE;QAAEyB,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEH,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eACzFlG,OAAA,CAAC5E,UAAU;QACTgH,OAAO,EAAC,IAAI;QACZ+E,SAAS,EAAC,IAAI;QACdrC,EAAE,EAAE;UACFoC,UAAU,EAAE,GAAG;UACf9B,QAAQ,EAAE,UAAU;UACpBmB,OAAO,EAAE,cAAc;UACvB,UAAU,EAAE;YACVV,OAAO,EAAE,IAAI;YACbT,QAAQ,EAAE,UAAU;YACpByG,MAAM,EAAE,CAAC,CAAC;YACV9F,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,EAAE;YACTjB,MAAM,EAAE,CAAC;YACTG,YAAY,EAAE,CAAC;YACfI,UAAU,EAAE,0BAA0B3D,KAAK,CAAC+D,OAAO,CAACkC,SAAS,CAACgB,IAAI,QAAQ3M,KAAK,CAAC0F,KAAK,CAAC+D,OAAO,CAACkC,SAAS,CAACgB,IAAI,EAAE,GAAG,CAAC;UACpH;QACF,CAAE;QAAA1C,QAAA,EACH;MAED;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAEJ/G,OAAA,CAAC1E,IAAI;MAAC4O,SAAS;MAACC,OAAO,EAAE;QAAE/D,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAACvB,EAAE,EAAE;QAAEkB,KAAK,EAAE,MAAM;QAAEoE,EAAE,EAAE;MAAE,CAAE;MAAAlE,QAAA,gBACtElG,OAAA,CAAC1E,IAAI;QAAC+O,IAAI;QAACjE,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACkB,EAAE,EAAE,CAAE;QAAArB,QAAA,eAC9BlG,OAAA,CAACqE,QAAQ;UACPC,KAAK,EAAC,mBAAmB;UACzBC,KAAK,EAAEzD,KAAK,CAACK,UAAW;UACxBqD,IAAI,eAAExE,OAAA,CAAC/C,cAAc;YAAA2J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBtC,KAAK,EAAE9C,KAAK,CAAC+D,OAAO,CAACiD,IAAI,CAACqD,IAAK;UAC/BtH,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,cAAc,CAAE;UACxCmD,QAAQ,EAAC;QAAsB;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACP/G,OAAA,CAAC1E,IAAI;QAAC+O,IAAI;QAACjE,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACkB,EAAE,EAAE,CAAE;QAAArB,QAAA,eAC9BlG,OAAA,CAACqE,QAAQ;UACPC,KAAK,EAAC,oBAAoB;UAC1BC,KAAK,EAAEzD,KAAK,CAACM,gBAAiB;UAC9BoD,IAAI,eAAExE,OAAA,CAAC3B,eAAe;YAAAuI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1BtC,KAAK,EAAE9C,KAAK,CAAC+D,OAAO,CAACuG,OAAO,CAACrD,IAAK;UAClClE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,cAAc,CAAE;UACxCiD,KAAK,EAAC,IAAI;UACVC,UAAU,EAAC,IAAI;UACfC,QAAQ,EAAC;QAAyB;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACP/G,OAAA,CAAC1E,IAAI;QAAC+O,IAAI;QAACjE,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACkB,EAAE,EAAE,CAAE;QAAArB,QAAA,eAC9BlG,OAAA,CAACqE,QAAQ;UACPC,KAAK,EAAC,qBAAqB;UAC3BC,KAAK,EAAEzD,KAAK,CAACO,iBAAkB;UAC/BmD,IAAI,eAAExE,OAAA,CAAC7B,WAAW;YAAAyI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBtC,KAAK,EAAE9C,KAAK,CAAC+D,OAAO,CAACrD,KAAK,CAACuG,IAAK;UAChClE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,cAAc,CAAE;UACxCiD,KAAK,EAAC,MAAM;UACZC,UAAU,EAAC,GAAG;UACdC,QAAQ,EAAC;QAA6B;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAERiB,aAAa;EAAA;IAAApB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACX,CAAC;AAEV,CAAC;AAAC9G,EAAA,CAnoBIzC,SAAS;EAAA,QAkBesC,WAAW,EACtBJ,WAAW,EACd1D,QAAQ;AAAA;AAAAkQ,EAAA,GApBlB1O,SAAS;AAqoBf,eAAeA,SAAS;AAAC,IAAA0O,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}