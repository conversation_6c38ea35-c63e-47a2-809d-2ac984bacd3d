{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project ai\\\\Store Management System\\\\asset management\\\\frontend\\\\src\\\\features\\\\itemReceive\\\\PreRegistrationForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Box, Button, Card, CardContent, CardHeader, Divider, Grid, TextField, Typography, MenuItem, FormControl, InputLabel, Select, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, IconButton, Alert, Snackbar, FormHelperText, List, ListItem, ListItemIcon, ListItemText } from '@mui/material';\nimport { Add as AddIcon, Delete as DeleteIcon, Save as SaveIcon, Send as SendIcon, CloudUpload as UploadIcon, Description as DescriptionIcon } from '@mui/icons-material';\nimport { useSnackbar } from 'notistack';\nimport { format } from 'date-fns';\nimport { getSuppliers } from '../../services/supplier';\nimport { getMainClassifications } from '../../services/classification';\nimport { getUnitsOfMeasure } from '../../services/specifications';\nimport { createPreRegistration, updatePreRegistration } from '../../services/itemReceive';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PreRegistrationForm = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    enqueueSnackbar\n  } = useSnackbar();\n  const [loading, setLoading] = useState(false);\n  const [suppliers, setSuppliers] = useState([]);\n  const [classifications, setClassifications] = useState([]);\n  const [unitOfMeasures, setUnitOfMeasures] = useState([]);\n  const [formData, setFormData] = useState({\n    requestDate: format(new Date(), 'yyyy-MM-dd'),\n    poNumber: '',\n    supplier: '',\n    description: '',\n    technicalSpecs: ''\n  });\n  const [items, setItems] = useState([{\n    id: 'temp-1',\n    itemCode: `PRE-${Math.floor(1000 + Math.random() * 9000)}`,\n    description: '',\n    quantity: 1,\n    unit: '',\n    unitPrice: 0,\n    classification: '',\n    isNew: true\n  }]);\n  const [files, setFiles] = useState([]);\n  const [errors, setErrors] = useState({});\n  const [showInspectionWarning, setShowInspectionWarning] = useState(false);\n  useEffect(() => {\n    const fetchData = async () => {\n      setLoading(true);\n      try {\n        const [suppliersData, classificationsData, unitOfMeasuresData] = await Promise.all([getSuppliers(), getMainClassifications(), getUnitsOfMeasure()]);\n        setSuppliers(suppliersData.results || suppliersData);\n        setClassifications(classificationsData.results || classificationsData);\n        setUnitOfMeasures(unitOfMeasuresData.results || unitOfMeasuresData);\n      } catch (error) {\n        console.error('Error fetching form data:', error);\n        enqueueSnackbar('Failed to load form data', {\n          variant: 'error'\n        });\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [enqueueSnackbar]);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Clear error when field is updated\n    if (errors[name]) {\n      setErrors({\n        ...errors,\n        [name]: ''\n      });\n    }\n  };\n  const handleItemChange = (index, field, value) => {\n    const newItems = [...items];\n    newItems[index][field] = value;\n\n    // Check if inspection warning should be shown\n    if (field === 'classification') {\n      const classification = classifications.find(c => c.id === value);\n      if (classification && (classification.name.toLowerCase().includes('medical') || classification.name.toLowerCase().includes('lab'))) {\n        setShowInspectionWarning(true);\n      }\n    }\n    setItems(newItems);\n  };\n  const handleAddItem = () => {\n    setItems([...items, {\n      id: `temp-${Date.now()}`,\n      itemCode: `PRE-${Math.floor(1000 + Math.random() * 9000)}`,\n      description: '',\n      quantity: 1,\n      unit: '',\n      unitPrice: 0,\n      classification: '',\n      isNew: true\n    }]);\n  };\n  const handleRemoveItem = index => {\n    const newItems = [...items];\n    newItems.splice(index, 1);\n    setItems(newItems);\n  };\n  const handleFileChange = e => {\n    const selectedFiles = Array.from(e.target.files);\n    setFiles([...files, ...selectedFiles]);\n  };\n  const handleRemoveFile = index => {\n    const newFiles = [...files];\n    newFiles.splice(index, 1);\n    setFiles(newFiles);\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.poNumber.trim()) {\n      newErrors.poNumber = 'PO Number is required';\n    }\n    if (!formData.supplier) {\n      newErrors.supplier = 'Supplier is required';\n    }\n    const itemErrors = items.map(item => {\n      const errors = {};\n      if (!item.description.trim()) {\n        errors.description = 'Description is required';\n      }\n      if (!item.unit) {\n        errors.unit = 'Unit is required';\n      }\n      if (!item.classification) {\n        errors.classification = 'Classification is required';\n      }\n      return errors;\n    });\n    if (itemErrors.some(error => Object.keys(error).length > 0)) {\n      newErrors.items = itemErrors;\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async (e, isDraft = false) => {\n    e.preventDefault();\n    if (!isDraft && !validateForm()) {\n      enqueueSnackbar('Please fix the errors in the form', {\n        variant: 'error'\n      });\n      return;\n    }\n    setLoading(true);\n    try {\n      // Prepare form data for submission\n      const formPayload = {\n        ...formData,\n        items: items.map(item => ({\n          item_code: item.itemCode,\n          description: item.description,\n          quantity: item.quantity,\n          unit: item.unit,\n          unit_price: item.unitPrice,\n          classification: item.classification\n        })),\n        is_draft: isDraft\n      };\n\n      // Create FormData for file uploads\n      const formDataWithFiles = new FormData();\n      formDataWithFiles.append('data', JSON.stringify(formPayload));\n      files.forEach((file, index) => {\n        formDataWithFiles.append(`file_${index}`, file);\n      });\n\n      // TODO: Replace with actual API call\n      console.log('Submitting form data:', formPayload);\n      console.log('Files:', files);\n\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      enqueueSnackbar(isDraft ? 'Pre-registration saved as draft' : 'Pre-registration submitted successfully', {\n        variant: 'success'\n      });\n\n      // Navigate back to the dashboard\n      navigate('/item-receive');\n    } catch (error) {\n      console.error('Error submitting pre-registration:', error);\n      enqueueSnackbar('Failed to submit pre-registration', {\n        variant: 'error'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3,\n      maxWidth: 1200,\n      mx: 'auto'\n    },\n    children: /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        gutterBottom: true,\n        children: \"Item Entry Request\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        color: \"textSecondary\",\n        gutterBottom: true,\n        children: \"Pre-Registration for Inventory Receiving\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          mb: 3\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: e => handleSubmit(e, false),\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Request Date\",\n              type: \"date\",\n              name: \"requestDate\",\n              value: formData.requestDate,\n              InputLabelProps: {\n                shrink: true\n              },\n              disabled: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Purchase Order (PO) Number\",\n              name: \"poNumber\",\n              value: formData.poNumber,\n              onChange: handleInputChange,\n              placeholder: \"PO-2024-XXXX\",\n              required: true,\n              error: !!errors.poNumber,\n              helperText: errors.poNumber\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              error: !!errors.supplier,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Supplier *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                name: \"supplier\",\n                value: formData.supplier,\n                onChange: handleInputChange,\n                label: \"Supplier *\",\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"-- Select Supplier --\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 19\n                }, this), suppliers.map(supplier => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: supplier.id,\n                  children: supplier.name\n                }, supplier.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 17\n              }, this), errors.supplier && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                children: errors.supplier\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Description\",\n              name: \"description\",\n              value: formData.description,\n              onChange: handleInputChange,\n              multiline: true,\n              rows: 2\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Supporting Documents\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Paper, {\n              variant: \"outlined\",\n              sx: {\n                p: 3,\n                border: '2px dashed #ccc',\n                textAlign: 'center',\n                cursor: 'pointer',\n                mb: 2\n              },\n              onClick: () => document.getElementById('file-upload').click(),\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                id: \"file-upload\",\n                multiple: true,\n                style: {\n                  display: 'none'\n                },\n                onChange: handleFileChange,\n                accept: \".pdf,.doc,.docx,.xls,.xlsx\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(UploadIcon, {\n                sx: {\n                  fontSize: 40,\n                  color: 'primary.main',\n                  mb: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                gutterBottom: true,\n                children: \"Drag & drop files here or click to browse\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"textSecondary\",\n                children: \"Accepted: PO copy, bid documents, specifications (PDF/Word/Excel)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 15\n            }, this), files.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                children: \"Uploaded Files:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(List, {\n                children: files.map((file, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n                  secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n                    edge: \"end\",\n                    onClick: () => handleRemoveFile(index),\n                    children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 370,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 369,\n                    columnNumber: 27\n                  }, this),\n                  children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                    children: /*#__PURE__*/_jsxDEV(DescriptionIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 375,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 374,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: file.name,\n                    secondary: `${(file.size / 1024).toFixed(2)} KB`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 377,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Items to Pre-Register *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n              component: Paper,\n              variant: \"outlined\",\n              sx: {\n                mb: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Table, {\n                children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                  children: /*#__PURE__*/_jsxDEV(TableRow, {\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Item Code\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 397,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Description\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 398,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Quantity\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 399,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Unit\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 400,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Unit Price\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 401,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Classification\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 402,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Action\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 403,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 396,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                  children: items.map((item, index) => {\n                    var _errors$items$index, _errors$items$index2, _errors$items$index3, _errors$items$index4, _errors$items$index5, _errors$items$index6, _errors$items$index7, _errors$items$index8;\n                    return /*#__PURE__*/_jsxDEV(TableRow, {\n                      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                        children: /*#__PURE__*/_jsxDEV(TextField, {\n                          fullWidth: true,\n                          value: item.itemCode,\n                          disabled: true,\n                          size: \"small\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 410,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 409,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: /*#__PURE__*/_jsxDEV(TextField, {\n                          fullWidth: true,\n                          placeholder: \"Description\",\n                          value: item.description,\n                          onChange: e => handleItemChange(index, 'description', e.target.value),\n                          required: true,\n                          size: \"small\",\n                          error: errors.items && ((_errors$items$index = errors.items[index]) === null || _errors$items$index === void 0 ? void 0 : _errors$items$index.description),\n                          helperText: errors.items && ((_errors$items$index2 = errors.items[index]) === null || _errors$items$index2 === void 0 ? void 0 : _errors$items$index2.description)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 418,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 417,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: /*#__PURE__*/_jsxDEV(TextField, {\n                          type: \"number\",\n                          value: item.quantity,\n                          onChange: e => handleItemChange(index, 'quantity', parseInt(e.target.value, 10) || 0),\n                          inputProps: {\n                            min: 1\n                          },\n                          size: \"small\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 430,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 429,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: /*#__PURE__*/_jsxDEV(FormControl, {\n                          fullWidth: true,\n                          size: \"small\",\n                          error: errors.items && ((_errors$items$index3 = errors.items[index]) === null || _errors$items$index3 === void 0 ? void 0 : _errors$items$index3.unit),\n                          children: [/*#__PURE__*/_jsxDEV(Select, {\n                            value: item.unit,\n                            onChange: e => handleItemChange(index, 'unit', e.target.value),\n                            displayEmpty: true,\n                            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                              value: \"\",\n                              children: \"Select Unit\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 445,\n                              columnNumber: 31\n                            }, this), unitOfMeasures.map(unit => /*#__PURE__*/_jsxDEV(MenuItem, {\n                              value: unit.id,\n                              children: unit.name\n                            }, unit.id, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 447,\n                              columnNumber: 33\n                            }, this))]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 440,\n                            columnNumber: 29\n                          }, this), errors.items && ((_errors$items$index4 = errors.items[index]) === null || _errors$items$index4 === void 0 ? void 0 : _errors$items$index4.unit) && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                            children: errors.items && ((_errors$items$index5 = errors.items[index]) === null || _errors$items$index5 === void 0 ? void 0 : _errors$items$index5.unit)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 453,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 439,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 438,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: /*#__PURE__*/_jsxDEV(TextField, {\n                          type: \"number\",\n                          value: item.unitPrice,\n                          onChange: e => handleItemChange(index, 'unitPrice', parseFloat(e.target.value) || 0),\n                          inputProps: {\n                            min: 0,\n                            step: 0.01\n                          },\n                          size: \"small\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 458,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 457,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: /*#__PURE__*/_jsxDEV(FormControl, {\n                          fullWidth: true,\n                          size: \"small\",\n                          error: errors.items && ((_errors$items$index6 = errors.items[index]) === null || _errors$items$index6 === void 0 ? void 0 : _errors$items$index6.classification),\n                          children: [/*#__PURE__*/_jsxDEV(Select, {\n                            value: item.classification,\n                            onChange: e => handleItemChange(index, 'classification', e.target.value),\n                            displayEmpty: true,\n                            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                              value: \"\",\n                              children: \"Select Classification\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 473,\n                              columnNumber: 31\n                            }, this), classifications.map(classification => /*#__PURE__*/_jsxDEV(MenuItem, {\n                              value: classification.id,\n                              children: classification.name\n                            }, classification.id, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 475,\n                              columnNumber: 33\n                            }, this))]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 468,\n                            columnNumber: 29\n                          }, this), errors.items && ((_errors$items$index7 = errors.items[index]) === null || _errors$items$index7 === void 0 ? void 0 : _errors$items$index7.classification) && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                            children: errors.items && ((_errors$items$index8 = errors.items[index]) === null || _errors$items$index8 === void 0 ? void 0 : _errors$items$index8.classification)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 481,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 467,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 466,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: /*#__PURE__*/_jsxDEV(IconButton, {\n                          color: \"error\",\n                          onClick: () => handleRemoveItem(index),\n                          disabled: items.length === 1,\n                          children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 491,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 486,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 485,\n                        columnNumber: 25\n                      }, this)]\n                    }, item.id, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 408,\n                      columnNumber: 23\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 28\n              }, this),\n              onClick: handleAddItem,\n              sx: {\n                mb: 3\n              },\n              children: \"Add Item\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Technical Specifications (If Applicable)\",\n              name: \"technicalSpecs\",\n              value: formData.technicalSpecs,\n              onChange: handleInputChange,\n              multiline: true,\n              rows: 4,\n              placeholder: \"Detailed specs, model numbers, compliance requirements...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 13\n          }, this), showInspectionWarning && /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"warning\",\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                children: \"Inspection Required\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: \"One or more items in this pre-registration will require technical inspection upon delivery. The inspection committee will be notified automatically.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sx: {\n              display: 'flex',\n              justifyContent: 'flex-end',\n              gap: 2,\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 28\n              }, this),\n              onClick: e => handleSubmit(e, true),\n              disabled: loading,\n              children: \"Save Draft\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"submit\",\n              variant: \"contained\",\n              startIcon: /*#__PURE__*/_jsxDEV(SendIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 28\n              }, this),\n              disabled: loading,\n              children: \"Submit for Pre-Registration\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 546,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 254,\n    columnNumber: 5\n  }, this);\n};\n_s(PreRegistrationForm, \"ExtkM1GWQFeMvB42+KUvUUNHIYI=\", false, function () {\n  return [useNavigate, useSnackbar];\n});\n_c = PreRegistrationForm;\nexport default PreRegistrationForm;\nvar _c;\n$RefreshReg$(_c, \"PreRegistrationForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "Box", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Divider", "Grid", "TextField", "Typography", "MenuItem", "FormControl", "InputLabel", "Select", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "IconButton", "<PERSON><PERSON>", "Snackbar", "FormHelperText", "List", "ListItem", "ListItemIcon", "ListItemText", "Add", "AddIcon", "Delete", "DeleteIcon", "Save", "SaveIcon", "Send", "SendIcon", "CloudUpload", "UploadIcon", "Description", "DescriptionIcon", "useSnackbar", "format", "getSuppliers", "getMainClassifications", "getUnitsOfMeasure", "createPreRegistration", "updatePreRegistration", "jsxDEV", "_jsxDEV", "PreRegistrationForm", "_s", "navigate", "enqueueSnackbar", "loading", "setLoading", "suppliers", "setSuppliers", "classifications", "setClassifications", "unitOfMeasures", "setUnitOfMeasures", "formData", "setFormData", "requestDate", "Date", "poNumber", "supplier", "description", "technicalSpecs", "items", "setItems", "id", "itemCode", "Math", "floor", "random", "quantity", "unit", "unitPrice", "classification", "isNew", "files", "setFiles", "errors", "setErrors", "showInspectionWarning", "setShowInspectionWarning", "fetchData", "suppliersData", "classificationsData", "unitOfMeasuresData", "Promise", "all", "results", "error", "console", "variant", "handleInputChange", "e", "name", "value", "target", "handleItemChange", "index", "field", "newItems", "find", "c", "toLowerCase", "includes", "handleAddItem", "now", "handleRemoveItem", "splice", "handleFileChange", "selectedFiles", "Array", "from", "handleRemoveFile", "newFiles", "validateForm", "newErrors", "trim", "itemErrors", "map", "item", "some", "Object", "keys", "length", "handleSubmit", "isDraft", "preventDefault", "formPayload", "item_code", "unit_price", "is_draft", "formDataWithFiles", "FormData", "append", "JSON", "stringify", "for<PERSON>ach", "file", "log", "resolve", "setTimeout", "sx", "p", "max<PERSON><PERSON><PERSON>", "mx", "children", "mb", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "onSubmit", "container", "spacing", "xs", "md", "fullWidth", "label", "type", "InputLabelProps", "shrink", "disabled", "onChange", "placeholder", "required", "helperText", "multiline", "rows", "border", "textAlign", "cursor", "onClick", "document", "getElementById", "click", "multiple", "style", "display", "accept", "fontSize", "secondaryAction", "edge", "primary", "secondary", "size", "toFixed", "component", "_errors$items$index", "_errors$items$index2", "_errors$items$index3", "_errors$items$index4", "_errors$items$index5", "_errors$items$index6", "_errors$items$index7", "_errors$items$index8", "parseInt", "inputProps", "min", "displayEmpty", "parseFloat", "step", "startIcon", "severity", "justifyContent", "gap", "mt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/project ai/Store Management System/asset management/frontend/src/features/itemReceive/PreRegistrationForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  Box,\n  <PERSON>ton,\n  Card,\n  CardContent,\n  CardHeader,\n  Divider,\n  Grid,\n  TextField,\n  Typography,\n  MenuItem,\n  FormControl,\n  InputLabel,\n  Select,\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  IconButton,\n  Alert,\n  Snackbar,\n  FormHelperText,\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Delete as DeleteIcon,\n  Save as SaveIcon,\n  Send as SendIcon,\n  CloudUpload as UploadIcon,\n  Description as DescriptionIcon,\n} from '@mui/icons-material';\nimport { useSnackbar } from 'notistack';\nimport { format } from 'date-fns';\nimport { getSuppliers } from '../../services/supplier';\nimport { getMainClassifications } from '../../services/classification';\nimport { getUnitsOfMeasure } from '../../services/specifications';\nimport { createPreRegistration, updatePreRegistration } from '../../services/itemReceive';\n\nconst PreRegistrationForm = () => {\n  const navigate = useNavigate();\n  const { enqueueSnackbar } = useSnackbar();\n  const [loading, setLoading] = useState(false);\n  const [suppliers, setSuppliers] = useState([]);\n  const [classifications, setClassifications] = useState([]);\n  const [unitOfMeasures, setUnitOfMeasures] = useState([]);\n  const [formData, setFormData] = useState({\n    requestDate: format(new Date(), 'yyyy-MM-dd'),\n    poNumber: '',\n    supplier: '',\n    description: '',\n    technicalSpecs: '',\n  });\n  const [items, setItems] = useState([\n    {\n      id: 'temp-1',\n      itemCode: `PRE-${Math.floor(1000 + Math.random() * 9000)}`,\n      description: '',\n      quantity: 1,\n      unit: '',\n      unitPrice: 0,\n      classification: '',\n      isNew: true,\n    },\n  ]);\n  const [files, setFiles] = useState([]);\n  const [errors, setErrors] = useState({});\n  const [showInspectionWarning, setShowInspectionWarning] = useState(false);\n\n  useEffect(() => {\n    const fetchData = async () => {\n      setLoading(true);\n      try {\n        const [suppliersData, classificationsData, unitOfMeasuresData] = await Promise.all([\n          getSuppliers(),\n          getMainClassifications(),\n          getUnitsOfMeasure(),\n        ]);\n\n        setSuppliers(suppliersData.results || suppliersData);\n        setClassifications(classificationsData.results || classificationsData);\n        setUnitOfMeasures(unitOfMeasuresData.results || unitOfMeasuresData);\n      } catch (error) {\n        console.error('Error fetching form data:', error);\n        enqueueSnackbar('Failed to load form data', { variant: 'error' });\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, [enqueueSnackbar]);\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value,\n    });\n\n    // Clear error when field is updated\n    if (errors[name]) {\n      setErrors({\n        ...errors,\n        [name]: '',\n      });\n    }\n  };\n\n  const handleItemChange = (index, field, value) => {\n    const newItems = [...items];\n    newItems[index][field] = value;\n\n    // Check if inspection warning should be shown\n    if (field === 'classification') {\n      const classification = classifications.find(c => c.id === value);\n      if (classification && (classification.name.toLowerCase().includes('medical') ||\n          classification.name.toLowerCase().includes('lab'))) {\n        setShowInspectionWarning(true);\n      }\n    }\n\n    setItems(newItems);\n  };\n\n  const handleAddItem = () => {\n    setItems([\n      ...items,\n      {\n        id: `temp-${Date.now()}`,\n        itemCode: `PRE-${Math.floor(1000 + Math.random() * 9000)}`,\n        description: '',\n        quantity: 1,\n        unit: '',\n        unitPrice: 0,\n        classification: '',\n        isNew: true,\n      },\n    ]);\n  };\n\n  const handleRemoveItem = (index) => {\n    const newItems = [...items];\n    newItems.splice(index, 1);\n    setItems(newItems);\n  };\n\n  const handleFileChange = (e) => {\n    const selectedFiles = Array.from(e.target.files);\n    setFiles([...files, ...selectedFiles]);\n  };\n\n  const handleRemoveFile = (index) => {\n    const newFiles = [...files];\n    newFiles.splice(index, 1);\n    setFiles(newFiles);\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formData.poNumber.trim()) {\n      newErrors.poNumber = 'PO Number is required';\n    }\n\n    if (!formData.supplier) {\n      newErrors.supplier = 'Supplier is required';\n    }\n\n    const itemErrors = items.map(item => {\n      const errors = {};\n      if (!item.description.trim()) {\n        errors.description = 'Description is required';\n      }\n      if (!item.unit) {\n        errors.unit = 'Unit is required';\n      }\n      if (!item.classification) {\n        errors.classification = 'Classification is required';\n      }\n      return errors;\n    });\n\n    if (itemErrors.some(error => Object.keys(error).length > 0)) {\n      newErrors.items = itemErrors;\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e, isDraft = false) => {\n    e.preventDefault();\n\n    if (!isDraft && !validateForm()) {\n      enqueueSnackbar('Please fix the errors in the form', { variant: 'error' });\n      return;\n    }\n\n    setLoading(true);\n    try {\n      // Prepare form data for submission\n      const formPayload = {\n        ...formData,\n        items: items.map(item => ({\n          item_code: item.itemCode,\n          description: item.description,\n          quantity: item.quantity,\n          unit: item.unit,\n          unit_price: item.unitPrice,\n          classification: item.classification,\n        })),\n        is_draft: isDraft,\n      };\n\n      // Create FormData for file uploads\n      const formDataWithFiles = new FormData();\n      formDataWithFiles.append('data', JSON.stringify(formPayload));\n      files.forEach((file, index) => {\n        formDataWithFiles.append(`file_${index}`, file);\n      });\n\n      // TODO: Replace with actual API call\n      console.log('Submitting form data:', formPayload);\n      console.log('Files:', files);\n\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      enqueueSnackbar(\n        isDraft ? 'Pre-registration saved as draft' : 'Pre-registration submitted successfully',\n        { variant: 'success' }\n      );\n\n      // Navigate back to the dashboard\n      navigate('/item-receive');\n    } catch (error) {\n      console.error('Error submitting pre-registration:', error);\n      enqueueSnackbar('Failed to submit pre-registration', { variant: 'error' });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Box sx={{ p: 3, maxWidth: 1200, mx: 'auto' }}>\n      <Paper sx={{ p: 3, mb: 4 }}>\n        <Typography variant=\"h4\" gutterBottom>\n          Item Entry Request\n        </Typography>\n        <Typography variant=\"subtitle1\" color=\"textSecondary\" gutterBottom>\n          Pre-Registration for Inventory Receiving\n        </Typography>\n        <Divider sx={{ mb: 3 }} />\n\n        <form onSubmit={(e) => handleSubmit(e, false)}>\n          <Grid container spacing={3}>\n            {/* Basic Information */}\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Request Date\"\n                type=\"date\"\n                name=\"requestDate\"\n                value={formData.requestDate}\n                InputLabelProps={{ shrink: true }}\n                disabled\n              />\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Purchase Order (PO) Number\"\n                name=\"poNumber\"\n                value={formData.poNumber}\n                onChange={handleInputChange}\n                placeholder=\"PO-2024-XXXX\"\n                required\n                error={!!errors.poNumber}\n                helperText={errors.poNumber}\n              />\n            </Grid>\n\n            <Grid item xs={12}>\n              <FormControl fullWidth error={!!errors.supplier}>\n                <InputLabel>Supplier *</InputLabel>\n                <Select\n                  name=\"supplier\"\n                  value={formData.supplier}\n                  onChange={handleInputChange}\n                  label=\"Supplier *\"\n                  required\n                >\n                  <MenuItem value=\"\">-- Select Supplier --</MenuItem>\n                  {suppliers.map((supplier) => (\n                    <MenuItem key={supplier.id} value={supplier.id}>\n                      {supplier.name}\n                    </MenuItem>\n                  ))}\n                </Select>\n                {errors.supplier && <FormHelperText>{errors.supplier}</FormHelperText>}\n              </FormControl>\n            </Grid>\n\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"Description\"\n                name=\"description\"\n                value={formData.description}\n                onChange={handleInputChange}\n                multiline\n                rows={2}\n              />\n            </Grid>\n\n            {/* Document Uploads */}\n            <Grid item xs={12}>\n              <Typography variant=\"subtitle1\" gutterBottom>\n                Supporting Documents\n              </Typography>\n              <Paper\n                variant=\"outlined\"\n                sx={{\n                  p: 3,\n                  border: '2px dashed #ccc',\n                  textAlign: 'center',\n                  cursor: 'pointer',\n                  mb: 2,\n                }}\n                onClick={() => document.getElementById('file-upload').click()}\n              >\n                <input\n                  type=\"file\"\n                  id=\"file-upload\"\n                  multiple\n                  style={{ display: 'none' }}\n                  onChange={handleFileChange}\n                  accept=\".pdf,.doc,.docx,.xls,.xlsx\"\n                />\n                <UploadIcon sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />\n                <Typography variant=\"body1\" gutterBottom>\n                  Drag & drop files here or click to browse\n                </Typography>\n                <Typography variant=\"caption\" color=\"textSecondary\">\n                  Accepted: PO copy, bid documents, specifications (PDF/Word/Excel)\n                </Typography>\n              </Paper>\n\n              {files.length > 0 && (\n                <Box sx={{ mb: 3 }}>\n                  <Typography variant=\"subtitle2\" gutterBottom>\n                    Uploaded Files:\n                  </Typography>\n                  <List>\n                    {files.map((file, index) => (\n                      <ListItem\n                        key={index}\n                        secondaryAction={\n                          <IconButton edge=\"end\" onClick={() => handleRemoveFile(index)}>\n                            <DeleteIcon />\n                          </IconButton>\n                        }\n                      >\n                        <ListItemIcon>\n                          <DescriptionIcon />\n                        </ListItemIcon>\n                        <ListItemText\n                          primary={file.name}\n                          secondary={`${(file.size / 1024).toFixed(2)} KB`}\n                        />\n                      </ListItem>\n                    ))}\n                  </List>\n                </Box>\n              )}\n            </Grid>\n\n            {/* Items Table */}\n            <Grid item xs={12}>\n              <Typography variant=\"subtitle1\" gutterBottom>\n                Items to Pre-Register *\n              </Typography>\n              <TableContainer component={Paper} variant=\"outlined\" sx={{ mb: 2 }}>\n                <Table>\n                  <TableHead>\n                    <TableRow>\n                      <TableCell>Item Code</TableCell>\n                      <TableCell>Description</TableCell>\n                      <TableCell>Quantity</TableCell>\n                      <TableCell>Unit</TableCell>\n                      <TableCell>Unit Price</TableCell>\n                      <TableCell>Classification</TableCell>\n                      <TableCell>Action</TableCell>\n                    </TableRow>\n                  </TableHead>\n                  <TableBody>\n                    {items.map((item, index) => (\n                      <TableRow key={item.id}>\n                        <TableCell>\n                          <TextField\n                            fullWidth\n                            value={item.itemCode}\n                            disabled\n                            size=\"small\"\n                          />\n                        </TableCell>\n                        <TableCell>\n                          <TextField\n                            fullWidth\n                            placeholder=\"Description\"\n                            value={item.description}\n                            onChange={(e) => handleItemChange(index, 'description', e.target.value)}\n                            required\n                            size=\"small\"\n                            error={errors.items && errors.items[index]?.description}\n                            helperText={errors.items && errors.items[index]?.description}\n                          />\n                        </TableCell>\n                        <TableCell>\n                          <TextField\n                            type=\"number\"\n                            value={item.quantity}\n                            onChange={(e) => handleItemChange(index, 'quantity', parseInt(e.target.value, 10) || 0)}\n                            inputProps={{ min: 1 }}\n                            size=\"small\"\n                          />\n                        </TableCell>\n                        <TableCell>\n                          <FormControl fullWidth size=\"small\" error={errors.items && errors.items[index]?.unit}>\n                            <Select\n                              value={item.unit}\n                              onChange={(e) => handleItemChange(index, 'unit', e.target.value)}\n                              displayEmpty\n                            >\n                              <MenuItem value=\"\">Select Unit</MenuItem>\n                              {unitOfMeasures.map((unit) => (\n                                <MenuItem key={unit.id} value={unit.id}>\n                                  {unit.name}\n                                </MenuItem>\n                              ))}\n                            </Select>\n                            {errors.items && errors.items[index]?.unit && (\n                              <FormHelperText>{errors.items && errors.items[index]?.unit}</FormHelperText>\n                            )}\n                          </FormControl>\n                        </TableCell>\n                        <TableCell>\n                          <TextField\n                            type=\"number\"\n                            value={item.unitPrice}\n                            onChange={(e) => handleItemChange(index, 'unitPrice', parseFloat(e.target.value) || 0)}\n                            inputProps={{ min: 0, step: 0.01 }}\n                            size=\"small\"\n                          />\n                        </TableCell>\n                        <TableCell>\n                          <FormControl fullWidth size=\"small\" error={errors.items && errors.items[index]?.classification}>\n                            <Select\n                              value={item.classification}\n                              onChange={(e) => handleItemChange(index, 'classification', e.target.value)}\n                              displayEmpty\n                            >\n                              <MenuItem value=\"\">Select Classification</MenuItem>\n                              {classifications.map((classification) => (\n                                <MenuItem key={classification.id} value={classification.id}>\n                                  {classification.name}\n                                </MenuItem>\n                              ))}\n                            </Select>\n                            {errors.items && errors.items[index]?.classification && (\n                              <FormHelperText>{errors.items && errors.items[index]?.classification}</FormHelperText>\n                            )}\n                          </FormControl>\n                        </TableCell>\n                        <TableCell>\n                          <IconButton\n                            color=\"error\"\n                            onClick={() => handleRemoveItem(index)}\n                            disabled={items.length === 1}\n                          >\n                            <DeleteIcon />\n                          </IconButton>\n                        </TableCell>\n                      </TableRow>\n                    ))}\n                  </TableBody>\n                </Table>\n              </TableContainer>\n              <Button\n                variant=\"outlined\"\n                startIcon={<AddIcon />}\n                onClick={handleAddItem}\n                sx={{ mb: 3 }}\n              >\n                Add Item\n              </Button>\n            </Grid>\n\n            {/* Technical Specifications */}\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"Technical Specifications (If Applicable)\"\n                name=\"technicalSpecs\"\n                value={formData.technicalSpecs}\n                onChange={handleInputChange}\n                multiline\n                rows={4}\n                placeholder=\"Detailed specs, model numbers, compliance requirements...\"\n              />\n            </Grid>\n\n            {/* Inspection Warning */}\n            {showInspectionWarning && (\n              <Grid item xs={12}>\n                <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                  <Typography variant=\"subtitle2\">Inspection Required</Typography>\n                  <Typography variant=\"body2\">\n                    One or more items in this pre-registration will require technical inspection upon delivery.\n                    The inspection committee will be notified automatically.\n                  </Typography>\n                </Alert>\n              </Grid>\n            )}\n\n            {/* Submission Buttons */}\n            <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 2 }}>\n              <Button\n                variant=\"outlined\"\n                startIcon={<SaveIcon />}\n                onClick={(e) => handleSubmit(e, true)}\n                disabled={loading}\n              >\n                Save Draft\n              </Button>\n              <Button\n                type=\"submit\"\n                variant=\"contained\"\n                startIcon={<SendIcon />}\n                disabled={loading}\n              >\n                Submit for Pre-Registration\n              </Button>\n            </Grid>\n          </Grid>\n        </form>\n      </Paper>\n    </Box>\n  );\n};\n\nexport default PreRegistrationForm;\n\n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,OAAO,EACPC,IAAI,EACJC,SAAS,EACTC,UAAU,EACVC,QAAQ,EACRC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVC,KAAK,EACLC,QAAQ,EACRC,cAAc,EACdC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,QACP,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,IAAI,IAAIC,QAAQ,EAChBC,WAAW,IAAIC,UAAU,EACzBC,WAAW,IAAIC,eAAe,QACzB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,WAAW;AACvC,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAASC,sBAAsB,QAAQ,+BAA+B;AACtE,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,qBAAqB,EAAEC,qBAAqB,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1F,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAMC,QAAQ,GAAGpD,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEqD;EAAgB,CAAC,GAAGZ,WAAW,CAAC,CAAC;EACzC,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0D,SAAS,EAAEC,YAAY,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC4D,eAAe,EAAEC,kBAAkB,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC8D,cAAc,EAAEC,iBAAiB,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACgE,QAAQ,EAAEC,WAAW,CAAC,GAAGjE,QAAQ,CAAC;IACvCkE,WAAW,EAAEtB,MAAM,CAAC,IAAIuB,IAAI,CAAC,CAAC,EAAE,YAAY,CAAC;IAC7CC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGzE,QAAQ,CAAC,CACjC;IACE0E,EAAE,EAAE,QAAQ;IACZC,QAAQ,EAAE,OAAOC,IAAI,CAACC,KAAK,CAAC,IAAI,GAAGD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE;IAC1DR,WAAW,EAAE,EAAE;IACfS,QAAQ,EAAE,CAAC;IACXC,IAAI,EAAE,EAAE;IACRC,SAAS,EAAE,CAAC;IACZC,cAAc,EAAE,EAAE;IAClBC,KAAK,EAAE;EACT,CAAC,CACF,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGrF,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACsF,MAAM,EAAEC,SAAS,CAAC,GAAGvF,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACwF,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGzF,QAAQ,CAAC,KAAK,CAAC;EAEzEC,SAAS,CAAC,MAAM;IACd,MAAMyF,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5BjC,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF,MAAM,CAACkC,aAAa,EAAEC,mBAAmB,EAAEC,kBAAkB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACjFlD,YAAY,CAAC,CAAC,EACdC,sBAAsB,CAAC,CAAC,EACxBC,iBAAiB,CAAC,CAAC,CACpB,CAAC;QAEFY,YAAY,CAACgC,aAAa,CAACK,OAAO,IAAIL,aAAa,CAAC;QACpD9B,kBAAkB,CAAC+B,mBAAmB,CAACI,OAAO,IAAIJ,mBAAmB,CAAC;QACtE7B,iBAAiB,CAAC8B,kBAAkB,CAACG,OAAO,IAAIH,kBAAkB,CAAC;MACrE,CAAC,CAAC,OAAOI,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD1C,eAAe,CAAC,0BAA0B,EAAE;UAAE4C,OAAO,EAAE;QAAQ,CAAC,CAAC;MACnE,CAAC,SAAS;QACR1C,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDiC,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACnC,eAAe,CAAC,CAAC;EAErB,MAAM6C,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCvC,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACsC,IAAI,GAAGC;IACV,CAAC,CAAC;;IAEF;IACA,IAAIjB,MAAM,CAACgB,IAAI,CAAC,EAAE;MAChBf,SAAS,CAAC;QACR,GAAGD,MAAM;QACT,CAACgB,IAAI,GAAG;MACV,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMG,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEJ,KAAK,KAAK;IAChD,MAAMK,QAAQ,GAAG,CAAC,GAAGpC,KAAK,CAAC;IAC3BoC,QAAQ,CAACF,KAAK,CAAC,CAACC,KAAK,CAAC,GAAGJ,KAAK;;IAE9B;IACA,IAAII,KAAK,KAAK,gBAAgB,EAAE;MAC9B,MAAMzB,cAAc,GAAGtB,eAAe,CAACiD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpC,EAAE,KAAK6B,KAAK,CAAC;MAChE,IAAIrB,cAAc,KAAKA,cAAc,CAACoB,IAAI,CAACS,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,SAAS,CAAC,IACxE9B,cAAc,CAACoB,IAAI,CAACS,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;QACtDvB,wBAAwB,CAAC,IAAI,CAAC;MAChC;IACF;IAEAhB,QAAQ,CAACmC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMK,aAAa,GAAGA,CAAA,KAAM;IAC1BxC,QAAQ,CAAC,CACP,GAAGD,KAAK,EACR;MACEE,EAAE,EAAE,QAAQP,IAAI,CAAC+C,GAAG,CAAC,CAAC,EAAE;MACxBvC,QAAQ,EAAE,OAAOC,IAAI,CAACC,KAAK,CAAC,IAAI,GAAGD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE;MAC1DR,WAAW,EAAE,EAAE;MACfS,QAAQ,EAAE,CAAC;MACXC,IAAI,EAAE,EAAE;MACRC,SAAS,EAAE,CAAC;MACZC,cAAc,EAAE,EAAE;MAClBC,KAAK,EAAE;IACT,CAAC,CACF,CAAC;EACJ,CAAC;EAED,MAAMgC,gBAAgB,GAAIT,KAAK,IAAK;IAClC,MAAME,QAAQ,GAAG,CAAC,GAAGpC,KAAK,CAAC;IAC3BoC,QAAQ,CAACQ,MAAM,CAACV,KAAK,EAAE,CAAC,CAAC;IACzBjC,QAAQ,CAACmC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMS,gBAAgB,GAAIhB,CAAC,IAAK;IAC9B,MAAMiB,aAAa,GAAGC,KAAK,CAACC,IAAI,CAACnB,CAAC,CAACG,MAAM,CAACpB,KAAK,CAAC;IAChDC,QAAQ,CAAC,CAAC,GAAGD,KAAK,EAAE,GAAGkC,aAAa,CAAC,CAAC;EACxC,CAAC;EAED,MAAMG,gBAAgB,GAAIf,KAAK,IAAK;IAClC,MAAMgB,QAAQ,GAAG,CAAC,GAAGtC,KAAK,CAAC;IAC3BsC,QAAQ,CAACN,MAAM,CAACV,KAAK,EAAE,CAAC,CAAC;IACzBrB,QAAQ,CAACqC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAAC5D,QAAQ,CAACI,QAAQ,CAACyD,IAAI,CAAC,CAAC,EAAE;MAC7BD,SAAS,CAACxD,QAAQ,GAAG,uBAAuB;IAC9C;IAEA,IAAI,CAACJ,QAAQ,CAACK,QAAQ,EAAE;MACtBuD,SAAS,CAACvD,QAAQ,GAAG,sBAAsB;IAC7C;IAEA,MAAMyD,UAAU,GAAGtD,KAAK,CAACuD,GAAG,CAACC,IAAI,IAAI;MACnC,MAAM1C,MAAM,GAAG,CAAC,CAAC;MACjB,IAAI,CAAC0C,IAAI,CAAC1D,WAAW,CAACuD,IAAI,CAAC,CAAC,EAAE;QAC5BvC,MAAM,CAAChB,WAAW,GAAG,yBAAyB;MAChD;MACA,IAAI,CAAC0D,IAAI,CAAChD,IAAI,EAAE;QACdM,MAAM,CAACN,IAAI,GAAG,kBAAkB;MAClC;MACA,IAAI,CAACgD,IAAI,CAAC9C,cAAc,EAAE;QACxBI,MAAM,CAACJ,cAAc,GAAG,4BAA4B;MACtD;MACA,OAAOI,MAAM;IACf,CAAC,CAAC;IAEF,IAAIwC,UAAU,CAACG,IAAI,CAAChC,KAAK,IAAIiC,MAAM,CAACC,IAAI,CAAClC,KAAK,CAAC,CAACmC,MAAM,GAAG,CAAC,CAAC,EAAE;MAC3DR,SAAS,CAACpD,KAAK,GAAGsD,UAAU;IAC9B;IAEAvC,SAAS,CAACqC,SAAS,CAAC;IACpB,OAAOM,MAAM,CAACC,IAAI,CAACP,SAAS,CAAC,CAACQ,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMC,YAAY,GAAG,MAAAA,CAAOhC,CAAC,EAAEiC,OAAO,GAAG,KAAK,KAAK;IACjDjC,CAAC,CAACkC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACD,OAAO,IAAI,CAACX,YAAY,CAAC,CAAC,EAAE;MAC/BpE,eAAe,CAAC,mCAAmC,EAAE;QAAE4C,OAAO,EAAE;MAAQ,CAAC,CAAC;MAC1E;IACF;IAEA1C,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA,MAAM+E,WAAW,GAAG;QAClB,GAAGxE,QAAQ;QACXQ,KAAK,EAAEA,KAAK,CAACuD,GAAG,CAACC,IAAI,KAAK;UACxBS,SAAS,EAAET,IAAI,CAACrD,QAAQ;UACxBL,WAAW,EAAE0D,IAAI,CAAC1D,WAAW;UAC7BS,QAAQ,EAAEiD,IAAI,CAACjD,QAAQ;UACvBC,IAAI,EAAEgD,IAAI,CAAChD,IAAI;UACf0D,UAAU,EAAEV,IAAI,CAAC/C,SAAS;UAC1BC,cAAc,EAAE8C,IAAI,CAAC9C;QACvB,CAAC,CAAC,CAAC;QACHyD,QAAQ,EAAEL;MACZ,CAAC;;MAED;MACA,MAAMM,iBAAiB,GAAG,IAAIC,QAAQ,CAAC,CAAC;MACxCD,iBAAiB,CAACE,MAAM,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACR,WAAW,CAAC,CAAC;MAC7DpD,KAAK,CAAC6D,OAAO,CAAC,CAACC,IAAI,EAAExC,KAAK,KAAK;QAC7BkC,iBAAiB,CAACE,MAAM,CAAC,QAAQpC,KAAK,EAAE,EAAEwC,IAAI,CAAC;MACjD,CAAC,CAAC;;MAEF;MACAhD,OAAO,CAACiD,GAAG,CAAC,uBAAuB,EAAEX,WAAW,CAAC;MACjDtC,OAAO,CAACiD,GAAG,CAAC,QAAQ,EAAE/D,KAAK,CAAC;;MAE5B;MACA,MAAM,IAAIU,OAAO,CAACsD,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvD7F,eAAe,CACb+E,OAAO,GAAG,iCAAiC,GAAG,yCAAyC,EACvF;QAAEnC,OAAO,EAAE;MAAU,CACvB,CAAC;;MAED;MACA7C,QAAQ,CAAC,eAAe,CAAC;IAC3B,CAAC,CAAC,OAAO2C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D1C,eAAe,CAAC,mCAAmC,EAAE;QAAE4C,OAAO,EAAE;MAAQ,CAAC,CAAC;IAC5E,CAAC,SAAS;MACR1C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEN,OAAA,CAAChD,GAAG;IAACmJ,EAAE,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,QAAQ,EAAE,IAAI;MAAEC,EAAE,EAAE;IAAO,CAAE;IAAAC,QAAA,eAC5CvG,OAAA,CAACnC,KAAK;MAACsI,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,gBACzBvG,OAAA,CAACxC,UAAU;QAACwF,OAAO,EAAC,IAAI;QAACyD,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb7G,OAAA,CAACxC,UAAU;QAACwF,OAAO,EAAC,WAAW;QAAC8D,KAAK,EAAC,eAAe;QAACL,YAAY;QAAAF,QAAA,EAAC;MAEnE;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb7G,OAAA,CAAC3C,OAAO;QAAC8I,EAAE,EAAE;UAAEK,EAAE,EAAE;QAAE;MAAE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE1B7G,OAAA;QAAM+G,QAAQ,EAAG7D,CAAC,IAAKgC,YAAY,CAAChC,CAAC,EAAE,KAAK,CAAE;QAAAqD,QAAA,eAC5CvG,OAAA,CAAC1C,IAAI;UAAC0J,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAV,QAAA,gBAEzBvG,OAAA,CAAC1C,IAAI;YAACuH,IAAI;YAACqC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAZ,QAAA,eACvBvG,OAAA,CAACzC,SAAS;cACR6J,SAAS;cACTC,KAAK,EAAC,cAAc;cACpBC,IAAI,EAAC,MAAM;cACXnE,IAAI,EAAC,aAAa;cAClBC,KAAK,EAAEvC,QAAQ,CAACE,WAAY;cAC5BwG,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK,CAAE;cAClCC,QAAQ;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP7G,OAAA,CAAC1C,IAAI;YAACuH,IAAI;YAACqC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAZ,QAAA,eACvBvG,OAAA,CAACzC,SAAS;cACR6J,SAAS;cACTC,KAAK,EAAC,4BAA4B;cAClClE,IAAI,EAAC,UAAU;cACfC,KAAK,EAAEvC,QAAQ,CAACI,QAAS;cACzByG,QAAQ,EAAEzE,iBAAkB;cAC5B0E,WAAW,EAAC,cAAc;cAC1BC,QAAQ;cACR9E,KAAK,EAAE,CAAC,CAACX,MAAM,CAAClB,QAAS;cACzB4G,UAAU,EAAE1F,MAAM,CAAClB;YAAS;cAAAyF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP7G,OAAA,CAAC1C,IAAI;YAACuH,IAAI;YAACqC,EAAE,EAAE,EAAG;YAAAX,QAAA,eAChBvG,OAAA,CAACtC,WAAW;cAAC0J,SAAS;cAACtE,KAAK,EAAE,CAAC,CAACX,MAAM,CAACjB,QAAS;cAAAqF,QAAA,gBAC9CvG,OAAA,CAACrC,UAAU;gBAAA4I,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnC7G,OAAA,CAACpC,MAAM;gBACLuF,IAAI,EAAC,UAAU;gBACfC,KAAK,EAAEvC,QAAQ,CAACK,QAAS;gBACzBwG,QAAQ,EAAEzE,iBAAkB;gBAC5BoE,KAAK,EAAC,YAAY;gBAClBO,QAAQ;gBAAArB,QAAA,gBAERvG,OAAA,CAACvC,QAAQ;kBAAC2F,KAAK,EAAC,EAAE;kBAAAmD,QAAA,EAAC;gBAAqB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,EAClDtG,SAAS,CAACqE,GAAG,CAAE1D,QAAQ,iBACtBlB,OAAA,CAACvC,QAAQ;kBAAmB2F,KAAK,EAAElC,QAAQ,CAACK,EAAG;kBAAAgF,QAAA,EAC5CrF,QAAQ,CAACiC;gBAAI,GADDjC,QAAQ,CAACK,EAAE;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,EACR1E,MAAM,CAACjB,QAAQ,iBAAIlB,OAAA,CAACzB,cAAc;gBAAAgI,QAAA,EAAEpE,MAAM,CAACjB;cAAQ;gBAAAwF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEP7G,OAAA,CAAC1C,IAAI;YAACuH,IAAI;YAACqC,EAAE,EAAE,EAAG;YAAAX,QAAA,eAChBvG,OAAA,CAACzC,SAAS;cACR6J,SAAS;cACTC,KAAK,EAAC,aAAa;cACnBlE,IAAI,EAAC,aAAa;cAClBC,KAAK,EAAEvC,QAAQ,CAACM,WAAY;cAC5BuG,QAAQ,EAAEzE,iBAAkB;cAC5B6E,SAAS;cACTC,IAAI,EAAE;YAAE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGP7G,OAAA,CAAC1C,IAAI;YAACuH,IAAI;YAACqC,EAAE,EAAE,EAAG;YAAAX,QAAA,gBAChBvG,OAAA,CAACxC,UAAU;cAACwF,OAAO,EAAC,WAAW;cAACyD,YAAY;cAAAF,QAAA,EAAC;YAE7C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb7G,OAAA,CAACnC,KAAK;cACJmF,OAAO,EAAC,UAAU;cAClBmD,EAAE,EAAE;gBACFC,CAAC,EAAE,CAAC;gBACJ4B,MAAM,EAAE,iBAAiB;gBACzBC,SAAS,EAAE,QAAQ;gBACnBC,MAAM,EAAE,SAAS;gBACjB1B,EAAE,EAAE;cACN,CAAE;cACF2B,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC,CAACC,KAAK,CAAC,CAAE;cAAA/B,QAAA,gBAE9DvG,OAAA;gBACEsH,IAAI,EAAC,MAAM;gBACX/F,EAAE,EAAC,aAAa;gBAChBgH,QAAQ;gBACRC,KAAK,EAAE;kBAAEC,OAAO,EAAE;gBAAO,CAAE;gBAC3Bf,QAAQ,EAAExD,gBAAiB;gBAC3BwE,MAAM,EAAC;cAA4B;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eACF7G,OAAA,CAACX,UAAU;gBAAC8G,EAAE,EAAE;kBAAEwC,QAAQ,EAAE,EAAE;kBAAE7B,KAAK,EAAE,cAAc;kBAAEN,EAAE,EAAE;gBAAE;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClE7G,OAAA,CAACxC,UAAU;gBAACwF,OAAO,EAAC,OAAO;gBAACyD,YAAY;gBAAAF,QAAA,EAAC;cAEzC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb7G,OAAA,CAACxC,UAAU;gBAACwF,OAAO,EAAC,SAAS;gBAAC8D,KAAK,EAAC,eAAe;gBAAAP,QAAA,EAAC;cAEpD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,EAEP5E,KAAK,CAACgD,MAAM,GAAG,CAAC,iBACfjF,OAAA,CAAChD,GAAG;cAACmJ,EAAE,EAAE;gBAAEK,EAAE,EAAE;cAAE,CAAE;cAAAD,QAAA,gBACjBvG,OAAA,CAACxC,UAAU;gBAACwF,OAAO,EAAC,WAAW;gBAACyD,YAAY;gBAAAF,QAAA,EAAC;cAE7C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb7G,OAAA,CAACxB,IAAI;gBAAA+H,QAAA,EACFtE,KAAK,CAAC2C,GAAG,CAAC,CAACmB,IAAI,EAAExC,KAAK,kBACrBvD,OAAA,CAACvB,QAAQ;kBAEPmK,eAAe,eACb5I,OAAA,CAAC5B,UAAU;oBAACyK,IAAI,EAAC,KAAK;oBAACV,OAAO,EAAEA,CAAA,KAAM7D,gBAAgB,CAACf,KAAK,CAAE;oBAAAgD,QAAA,eAC5DvG,OAAA,CAACjB,UAAU;sBAAA2H,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CACb;kBAAAN,QAAA,gBAEDvG,OAAA,CAACtB,YAAY;oBAAA6H,QAAA,eACXvG,OAAA,CAACT,eAAe;sBAAAmH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACf7G,OAAA,CAACrB,YAAY;oBACXmK,OAAO,EAAE/C,IAAI,CAAC5C,IAAK;oBACnB4F,SAAS,EAAE,GAAG,CAAChD,IAAI,CAACiD,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC;kBAAM;oBAAAvC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC;gBAAA,GAbGtD,KAAK;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAcF,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAGP7G,OAAA,CAAC1C,IAAI;YAACuH,IAAI;YAACqC,EAAE,EAAE,EAAG;YAAAX,QAAA,gBAChBvG,OAAA,CAACxC,UAAU;cAACwF,OAAO,EAAC,WAAW;cAACyD,YAAY;cAAAF,QAAA,EAAC;YAE7C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb7G,OAAA,CAAC/B,cAAc;cAACiL,SAAS,EAAErL,KAAM;cAACmF,OAAO,EAAC,UAAU;cAACmD,EAAE,EAAE;gBAAEK,EAAE,EAAE;cAAE,CAAE;cAAAD,QAAA,eACjEvG,OAAA,CAAClC,KAAK;gBAAAyI,QAAA,gBACJvG,OAAA,CAAC9B,SAAS;kBAAAqI,QAAA,eACRvG,OAAA,CAAC7B,QAAQ;oBAAAoI,QAAA,gBACPvG,OAAA,CAAChC,SAAS;sBAAAuI,QAAA,EAAC;oBAAS;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAChC7G,OAAA,CAAChC,SAAS;sBAAAuI,QAAA,EAAC;oBAAW;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAClC7G,OAAA,CAAChC,SAAS;sBAAAuI,QAAA,EAAC;oBAAQ;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC/B7G,OAAA,CAAChC,SAAS;sBAAAuI,QAAA,EAAC;oBAAI;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC3B7G,OAAA,CAAChC,SAAS;sBAAAuI,QAAA,EAAC;oBAAU;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eACjC7G,OAAA,CAAChC,SAAS;sBAAAuI,QAAA,EAAC;oBAAc;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eACrC7G,OAAA,CAAChC,SAAS;sBAAAuI,QAAA,EAAC;oBAAM;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACZ7G,OAAA,CAACjC,SAAS;kBAAAwI,QAAA,EACPlF,KAAK,CAACuD,GAAG,CAAC,CAACC,IAAI,EAAEtB,KAAK;oBAAA,IAAA4F,mBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA;oBAAA,oBACrB1J,OAAA,CAAC7B,QAAQ;sBAAAoI,QAAA,gBACPvG,OAAA,CAAChC,SAAS;wBAAAuI,QAAA,eACRvG,OAAA,CAACzC,SAAS;0BACR6J,SAAS;0BACThE,KAAK,EAAEyB,IAAI,CAACrD,QAAS;0BACrBiG,QAAQ;0BACRuB,IAAI,EAAC;wBAAO;0BAAAtC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACb;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACO,CAAC,eACZ7G,OAAA,CAAChC,SAAS;wBAAAuI,QAAA,eACRvG,OAAA,CAACzC,SAAS;0BACR6J,SAAS;0BACTO,WAAW,EAAC,aAAa;0BACzBvE,KAAK,EAAEyB,IAAI,CAAC1D,WAAY;0BACxBuG,QAAQ,EAAGxE,CAAC,IAAKI,gBAAgB,CAACC,KAAK,EAAE,aAAa,EAAEL,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;0BACxEwE,QAAQ;0BACRoB,IAAI,EAAC,OAAO;0BACZlG,KAAK,EAAEX,MAAM,CAACd,KAAK,MAAA8H,mBAAA,GAAIhH,MAAM,CAACd,KAAK,CAACkC,KAAK,CAAC,cAAA4F,mBAAA,uBAAnBA,mBAAA,CAAqBhI,WAAW,CAAC;0BACxD0G,UAAU,EAAE1F,MAAM,CAACd,KAAK,MAAA+H,oBAAA,GAAIjH,MAAM,CAACd,KAAK,CAACkC,KAAK,CAAC,cAAA6F,oBAAA,uBAAnBA,oBAAA,CAAqBjI,WAAW;wBAAC;0BAAAuF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9D;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACO,CAAC,eACZ7G,OAAA,CAAChC,SAAS;wBAAAuI,QAAA,eACRvG,OAAA,CAACzC,SAAS;0BACR+J,IAAI,EAAC,QAAQ;0BACblE,KAAK,EAAEyB,IAAI,CAACjD,QAAS;0BACrB8F,QAAQ,EAAGxE,CAAC,IAAKI,gBAAgB,CAACC,KAAK,EAAE,UAAU,EAAEoG,QAAQ,CAACzG,CAAC,CAACG,MAAM,CAACD,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,CAAE;0BACxFwG,UAAU,EAAE;4BAAEC,GAAG,EAAE;0BAAE,CAAE;0BACvBb,IAAI,EAAC;wBAAO;0BAAAtC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACb;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACO,CAAC,eACZ7G,OAAA,CAAChC,SAAS;wBAAAuI,QAAA,eACRvG,OAAA,CAACtC,WAAW;0BAAC0J,SAAS;0BAAC4B,IAAI,EAAC,OAAO;0BAAClG,KAAK,EAAEX,MAAM,CAACd,KAAK,MAAAgI,oBAAA,GAAIlH,MAAM,CAACd,KAAK,CAACkC,KAAK,CAAC,cAAA8F,oBAAA,uBAAnBA,oBAAA,CAAqBxH,IAAI,CAAC;0BAAA0E,QAAA,gBACnFvG,OAAA,CAACpC,MAAM;4BACLwF,KAAK,EAAEyB,IAAI,CAAChD,IAAK;4BACjB6F,QAAQ,EAAGxE,CAAC,IAAKI,gBAAgB,CAACC,KAAK,EAAE,MAAM,EAAEL,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;4BACjE0G,YAAY;4BAAAvD,QAAA,gBAEZvG,OAAA,CAACvC,QAAQ;8BAAC2F,KAAK,EAAC,EAAE;8BAAAmD,QAAA,EAAC;4BAAW;8BAAAG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAU,CAAC,EACxClG,cAAc,CAACiE,GAAG,CAAE/C,IAAI,iBACvB7B,OAAA,CAACvC,QAAQ;8BAAe2F,KAAK,EAAEvB,IAAI,CAACN,EAAG;8BAAAgF,QAAA,EACpC1E,IAAI,CAACsB;4BAAI,GADGtB,IAAI,CAACN,EAAE;8BAAAmF,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAEZ,CACX,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACI,CAAC,EACR1E,MAAM,CAACd,KAAK,MAAAiI,oBAAA,GAAInH,MAAM,CAACd,KAAK,CAACkC,KAAK,CAAC,cAAA+F,oBAAA,uBAAnBA,oBAAA,CAAqBzH,IAAI,kBACxC7B,OAAA,CAACzB,cAAc;4BAAAgI,QAAA,EAAEpE,MAAM,CAACd,KAAK,MAAAkI,oBAAA,GAAIpH,MAAM,CAACd,KAAK,CAACkC,KAAK,CAAC,cAAAgG,oBAAA,uBAAnBA,oBAAA,CAAqB1H,IAAI;0BAAA;4BAAA6E,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAiB,CAC5E;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACU;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACZ7G,OAAA,CAAChC,SAAS;wBAAAuI,QAAA,eACRvG,OAAA,CAACzC,SAAS;0BACR+J,IAAI,EAAC,QAAQ;0BACblE,KAAK,EAAEyB,IAAI,CAAC/C,SAAU;0BACtB4F,QAAQ,EAAGxE,CAAC,IAAKI,gBAAgB,CAACC,KAAK,EAAE,WAAW,EAAEwG,UAAU,CAAC7G,CAAC,CAACG,MAAM,CAACD,KAAK,CAAC,IAAI,CAAC,CAAE;0BACvFwG,UAAU,EAAE;4BAAEC,GAAG,EAAE,CAAC;4BAAEG,IAAI,EAAE;0BAAK,CAAE;0BACnChB,IAAI,EAAC;wBAAO;0BAAAtC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACb;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACO,CAAC,eACZ7G,OAAA,CAAChC,SAAS;wBAAAuI,QAAA,eACRvG,OAAA,CAACtC,WAAW;0BAAC0J,SAAS;0BAAC4B,IAAI,EAAC,OAAO;0BAAClG,KAAK,EAAEX,MAAM,CAACd,KAAK,MAAAmI,oBAAA,GAAIrH,MAAM,CAACd,KAAK,CAACkC,KAAK,CAAC,cAAAiG,oBAAA,uBAAnBA,oBAAA,CAAqBzH,cAAc,CAAC;0BAAAwE,QAAA,gBAC7FvG,OAAA,CAACpC,MAAM;4BACLwF,KAAK,EAAEyB,IAAI,CAAC9C,cAAe;4BAC3B2F,QAAQ,EAAGxE,CAAC,IAAKI,gBAAgB,CAACC,KAAK,EAAE,gBAAgB,EAAEL,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;4BAC3E0G,YAAY;4BAAAvD,QAAA,gBAEZvG,OAAA,CAACvC,QAAQ;8BAAC2F,KAAK,EAAC,EAAE;8BAAAmD,QAAA,EAAC;4BAAqB;8BAAAG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAU,CAAC,EAClDpG,eAAe,CAACmE,GAAG,CAAE7C,cAAc,iBAClC/B,OAAA,CAACvC,QAAQ;8BAAyB2F,KAAK,EAAErB,cAAc,CAACR,EAAG;8BAAAgF,QAAA,EACxDxE,cAAc,CAACoB;4BAAI,GADPpB,cAAc,CAACR,EAAE;8BAAAmF,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAEtB,CACX,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACI,CAAC,EACR1E,MAAM,CAACd,KAAK,MAAAoI,oBAAA,GAAItH,MAAM,CAACd,KAAK,CAACkC,KAAK,CAAC,cAAAkG,oBAAA,uBAAnBA,oBAAA,CAAqB1H,cAAc,kBAClD/B,OAAA,CAACzB,cAAc;4BAAAgI,QAAA,EAAEpE,MAAM,CAACd,KAAK,MAAAqI,oBAAA,GAAIvH,MAAM,CAACd,KAAK,CAACkC,KAAK,CAAC,cAAAmG,oBAAA,uBAAnBA,oBAAA,CAAqB3H,cAAc;0BAAA;4BAAA2E,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAiB,CACtF;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACU;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACZ7G,OAAA,CAAChC,SAAS;wBAAAuI,QAAA,eACRvG,OAAA,CAAC5B,UAAU;0BACT0I,KAAK,EAAC,OAAO;0BACbqB,OAAO,EAAEA,CAAA,KAAMnE,gBAAgB,CAACT,KAAK,CAAE;0BACvCkE,QAAQ,EAAEpG,KAAK,CAAC4D,MAAM,KAAK,CAAE;0BAAAsB,QAAA,eAE7BvG,OAAA,CAACjB,UAAU;4BAAA2H,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA,GArFChC,IAAI,CAACtD,EAAE;sBAAAmF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAsFZ,CAAC;kBAAA,CACZ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACjB7G,OAAA,CAAC/C,MAAM;cACL+F,OAAO,EAAC,UAAU;cAClBiH,SAAS,eAAEjK,OAAA,CAACnB,OAAO;gBAAA6H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBsB,OAAO,EAAErE,aAAc;cACvBqC,EAAE,EAAE;gBAAEK,EAAE,EAAE;cAAE,CAAE;cAAAD,QAAA,EACf;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGP7G,OAAA,CAAC1C,IAAI;YAACuH,IAAI;YAACqC,EAAE,EAAE,EAAG;YAAAX,QAAA,eAChBvG,OAAA,CAACzC,SAAS;cACR6J,SAAS;cACTC,KAAK,EAAC,0CAA0C;cAChDlE,IAAI,EAAC,gBAAgB;cACrBC,KAAK,EAAEvC,QAAQ,CAACO,cAAe;cAC/BsG,QAAQ,EAAEzE,iBAAkB;cAC5B6E,SAAS;cACTC,IAAI,EAAE,CAAE;cACRJ,WAAW,EAAC;YAA2D;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAGNxE,qBAAqB,iBACpBrC,OAAA,CAAC1C,IAAI;YAACuH,IAAI;YAACqC,EAAE,EAAE,EAAG;YAAAX,QAAA,eAChBvG,OAAA,CAAC3B,KAAK;cAAC6L,QAAQ,EAAC,SAAS;cAAC/D,EAAE,EAAE;gBAAEK,EAAE,EAAE;cAAE,CAAE;cAAAD,QAAA,gBACtCvG,OAAA,CAACxC,UAAU;gBAACwF,OAAO,EAAC,WAAW;gBAAAuD,QAAA,EAAC;cAAmB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChE7G,OAAA,CAACxC,UAAU;gBAACwF,OAAO,EAAC,OAAO;gBAAAuD,QAAA,EAAC;cAG5B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACP,eAGD7G,OAAA,CAAC1C,IAAI;YAACuH,IAAI;YAACqC,EAAE,EAAE,EAAG;YAACf,EAAE,EAAE;cAAEsC,OAAO,EAAE,MAAM;cAAE0B,cAAc,EAAE,UAAU;cAAEC,GAAG,EAAE,CAAC;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAA9D,QAAA,gBACpFvG,OAAA,CAAC/C,MAAM;cACL+F,OAAO,EAAC,UAAU;cAClBiH,SAAS,eAAEjK,OAAA,CAACf,QAAQ;gBAAAyH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBsB,OAAO,EAAGjF,CAAC,IAAKgC,YAAY,CAAChC,CAAC,EAAE,IAAI,CAAE;cACtCuE,QAAQ,EAAEpH,OAAQ;cAAAkG,QAAA,EACnB;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT7G,OAAA,CAAC/C,MAAM;cACLqK,IAAI,EAAC,QAAQ;cACbtE,OAAO,EAAC,WAAW;cACnBiH,SAAS,eAAEjK,OAAA,CAACb,QAAQ;gBAAAuH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBY,QAAQ,EAAEpH,OAAQ;cAAAkG,QAAA,EACnB;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC3G,EAAA,CAhgBID,mBAAmB;EAAA,QACNlD,WAAW,EACAyC,WAAW;AAAA;AAAA8K,EAAA,GAFnCrK,mBAAmB;AAkgBzB,eAAeA,mBAAmB;AAAC,IAAAqK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}