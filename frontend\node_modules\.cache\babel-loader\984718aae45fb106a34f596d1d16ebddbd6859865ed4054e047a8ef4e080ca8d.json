{"ast": null, "code": "import api from '../utils/axios';\nimport { prepareDropdownData } from '../utils/filters';\n\n// Organization Types\nexport const getOrganizationTypes = async (params = {}, includeInactive = false) => {\n  // If not explicitly requesting inactive items, only get active ones\n  if (!includeInactive && !params.is_active) {\n    params = {\n      ...params,\n      is_active: true\n    };\n  }\n  const response = await api.get('/organization-types/', {\n    params\n  });\n  return prepareDropdownData(response.data, includeInactive);\n};\nexport const getOrganizationType = async id => {\n  const response = await api.get(`/organization-types/${id}/`);\n  return response.data;\n};\nexport const createOrganizationType = async data => {\n  const response = await api.post('/organization-types/', data);\n  return response.data;\n};\nexport const updateOrganizationType = async (id, data) => {\n  const response = await api.put(`/organization-types/${id}/`, data);\n  return response.data;\n};\nexport const deleteOrganizationType = async id => {\n  await api.delete(`/organization-types/${id}/`);\n  return true;\n};\n\n// Organizations\nexport const getOrganizations = async (params = {}, includeInactive = false) => {\n  // If not explicitly requesting inactive items, only get active ones\n  if (!includeInactive && !params.is_active) {\n    params = {\n      ...params,\n      is_active: true\n    };\n  }\n  const response = await api.get('/organizations/', {\n    params\n  });\n  return prepareDropdownData(response.data, includeInactive);\n};\nexport const getOrganization = async id => {\n  const response = await api.get(`/organizations/${id}/`);\n  return response.data;\n};\nexport const createOrganization = async data => {\n  const response = await api.post('/organizations/', data);\n  return response.data;\n};\nexport const updateOrganization = async (id, data) => {\n  const response = await api.put(`/organizations/${id}/`, data);\n  return response.data;\n};\nexport const deleteOrganization = async id => {\n  await api.delete(`/organizations/${id}/`);\n  return true;\n};\n\n// Get main organization (first active organization or fallback)\nexport const getMainOrganization = async () => {\n  try {\n    const response = await api.get('/organizations/', {\n      params: {\n        is_active: true,\n        limit: 1\n      }\n    });\n    const organizations = response.data.results || response.data;\n    if (organizations && organizations.length > 0) {\n      return organizations[0];\n    }\n\n    // If no organizations found, return fallback data\n    return {\n      id: null,\n      name: 'University of Gondar',\n      motto: 'Stock Management System',\n      logo_url: '/assets/images/uog-logo.png',\n      website: '',\n      phone: '',\n      email: '',\n      is_active: true\n    };\n  } catch (error) {\n    console.error('Error fetching main organization:', error);\n\n    // Return fallback data on error\n    return {\n      id: null,\n      name: 'University of Gondar',\n      motto: 'Stock Management System',\n      logo_url: '/assets/images/uog-logo.png',\n      website: '',\n      phone: '',\n      email: '',\n      is_active: true\n    };\n  }\n};\n\n// Offices\nexport const getOffices = async (params = {}, includeInactive = false) => {\n  // If not explicitly requesting inactive items, only get active ones\n  if (!includeInactive && !params.is_active) {\n    params = {\n      ...params,\n      is_active: true\n    };\n  }\n  const response = await api.get('/offices/', {\n    params\n  });\n  return prepareDropdownData(response.data, includeInactive);\n};\nexport const getOffice = async id => {\n  const response = await api.get(`/offices/${id}/`);\n  return response.data;\n};\nexport const createOffice = async data => {\n  const response = await api.post('/offices/', data);\n  return response.data;\n};\nexport const updateOffice = async (id, data) => {\n  const response = await api.put(`/offices/${id}/`, data);\n  return response.data;\n};\nexport const deleteOffice = async id => {\n  await api.delete(`/offices/${id}/`);\n  return true;\n};", "map": {"version": 3, "names": ["api", "prepareDropdownData", "getOrganizationTypes", "params", "includeInactive", "is_active", "response", "get", "data", "getOrganizationType", "id", "createOrganizationType", "post", "updateOrganizationType", "put", "deleteOrganizationType", "delete", "getOrganizations", "getOrganization", "createOrganization", "updateOrganization", "deleteOrganization", "getMainOrganization", "limit", "organizations", "results", "length", "name", "motto", "logo_url", "website", "phone", "email", "error", "console", "getOffices", "getOffice", "createOffice", "updateOffice", "deleteOffice"], "sources": ["C:/Users/<USER>/Desktop/project ai/Store Management System/asset management/frontend/src/services/organizations.js"], "sourcesContent": ["import api from '../utils/axios';\nimport { prepareDropdownData } from '../utils/filters';\n\n// Organization Types\nexport const getOrganizationTypes = async (params = {}, includeInactive = false) => {\n  // If not explicitly requesting inactive items, only get active ones\n  if (!includeInactive && !params.is_active) {\n    params = { ...params, is_active: true };\n  }\n\n  const response = await api.get('/organization-types/', { params });\n  return prepareDropdownData(response.data, includeInactive);\n};\n\nexport const getOrganizationType = async (id) => {\n  const response = await api.get(`/organization-types/${id}/`);\n  return response.data;\n};\n\nexport const createOrganizationType = async (data) => {\n  const response = await api.post('/organization-types/', data);\n  return response.data;\n};\n\nexport const updateOrganizationType = async (id, data) => {\n  const response = await api.put(`/organization-types/${id}/`, data);\n  return response.data;\n};\n\nexport const deleteOrganizationType = async (id) => {\n  await api.delete(`/organization-types/${id}/`);\n  return true;\n};\n\n// Organizations\nexport const getOrganizations = async (params = {}, includeInactive = false) => {\n  // If not explicitly requesting inactive items, only get active ones\n  if (!includeInactive && !params.is_active) {\n    params = { ...params, is_active: true };\n  }\n\n  const response = await api.get('/organizations/', { params });\n  return prepareDropdownData(response.data, includeInactive);\n};\n\nexport const getOrganization = async (id) => {\n  const response = await api.get(`/organizations/${id}/`);\n  return response.data;\n};\n\nexport const createOrganization = async (data) => {\n  const response = await api.post('/organizations/', data);\n  return response.data;\n};\n\nexport const updateOrganization = async (id, data) => {\n  const response = await api.put(`/organizations/${id}/`, data);\n  return response.data;\n};\n\nexport const deleteOrganization = async (id) => {\n  await api.delete(`/organizations/${id}/`);\n  return true;\n};\n\n// Get main organization (first active organization or fallback)\nexport const getMainOrganization = async () => {\n  try {\n    const response = await api.get('/organizations/', {\n      params: {\n        is_active: true,\n        limit: 1\n      }\n    });\n\n    const organizations = response.data.results || response.data;\n\n    if (organizations && organizations.length > 0) {\n      return organizations[0];\n    }\n\n    // If no organizations found, return fallback data\n    return {\n      id: null,\n      name: 'University of Gondar',\n      motto: 'Stock Management System',\n      logo_url: '/assets/images/uog-logo.png',\n      website: '',\n      phone: '',\n      email: '',\n      is_active: true\n    };\n  } catch (error) {\n    console.error('Error fetching main organization:', error);\n\n    // Return fallback data on error\n    return {\n      id: null,\n      name: 'University of Gondar',\n      motto: 'Stock Management System',\n      logo_url: '/assets/images/uog-logo.png',\n      website: '',\n      phone: '',\n      email: '',\n      is_active: true\n    };\n  }\n};\n\n// Offices\nexport const getOffices = async (params = {}, includeInactive = false) => {\n  // If not explicitly requesting inactive items, only get active ones\n  if (!includeInactive && !params.is_active) {\n    params = { ...params, is_active: true };\n  }\n\n  const response = await api.get('/offices/', { params });\n  return prepareDropdownData(response.data, includeInactive);\n};\n\nexport const getOffice = async (id) => {\n  const response = await api.get(`/offices/${id}/`);\n  return response.data;\n};\n\nexport const createOffice = async (data) => {\n  const response = await api.post('/offices/', data);\n  return response.data;\n};\n\nexport const updateOffice = async (id, data) => {\n  const response = await api.put(`/offices/${id}/`, data);\n  return response.data;\n};\n\nexport const deleteOffice = async (id) => {\n  await api.delete(`/offices/${id}/`);\n  return true;\n};\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,gBAAgB;AAChC,SAASC,mBAAmB,QAAQ,kBAAkB;;AAEtD;AACA,OAAO,MAAMC,oBAAoB,GAAG,MAAAA,CAAOC,MAAM,GAAG,CAAC,CAAC,EAAEC,eAAe,GAAG,KAAK,KAAK;EAClF;EACA,IAAI,CAACA,eAAe,IAAI,CAACD,MAAM,CAACE,SAAS,EAAE;IACzCF,MAAM,GAAG;MAAE,GAAGA,MAAM;MAAEE,SAAS,EAAE;IAAK,CAAC;EACzC;EAEA,MAAMC,QAAQ,GAAG,MAAMN,GAAG,CAACO,GAAG,CAAC,sBAAsB,EAAE;IAAEJ;EAAO,CAAC,CAAC;EAClE,OAAOF,mBAAmB,CAACK,QAAQ,CAACE,IAAI,EAAEJ,eAAe,CAAC;AAC5D,CAAC;AAED,OAAO,MAAMK,mBAAmB,GAAG,MAAOC,EAAE,IAAK;EAC/C,MAAMJ,QAAQ,GAAG,MAAMN,GAAG,CAACO,GAAG,CAAC,uBAAuBG,EAAE,GAAG,CAAC;EAC5D,OAAOJ,QAAQ,CAACE,IAAI;AACtB,CAAC;AAED,OAAO,MAAMG,sBAAsB,GAAG,MAAOH,IAAI,IAAK;EACpD,MAAMF,QAAQ,GAAG,MAAMN,GAAG,CAACY,IAAI,CAAC,sBAAsB,EAAEJ,IAAI,CAAC;EAC7D,OAAOF,QAAQ,CAACE,IAAI;AACtB,CAAC;AAED,OAAO,MAAMK,sBAAsB,GAAG,MAAAA,CAAOH,EAAE,EAAEF,IAAI,KAAK;EACxD,MAAMF,QAAQ,GAAG,MAAMN,GAAG,CAACc,GAAG,CAAC,uBAAuBJ,EAAE,GAAG,EAAEF,IAAI,CAAC;EAClE,OAAOF,QAAQ,CAACE,IAAI;AACtB,CAAC;AAED,OAAO,MAAMO,sBAAsB,GAAG,MAAOL,EAAE,IAAK;EAClD,MAAMV,GAAG,CAACgB,MAAM,CAAC,uBAAuBN,EAAE,GAAG,CAAC;EAC9C,OAAO,IAAI;AACb,CAAC;;AAED;AACA,OAAO,MAAMO,gBAAgB,GAAG,MAAAA,CAAOd,MAAM,GAAG,CAAC,CAAC,EAAEC,eAAe,GAAG,KAAK,KAAK;EAC9E;EACA,IAAI,CAACA,eAAe,IAAI,CAACD,MAAM,CAACE,SAAS,EAAE;IACzCF,MAAM,GAAG;MAAE,GAAGA,MAAM;MAAEE,SAAS,EAAE;IAAK,CAAC;EACzC;EAEA,MAAMC,QAAQ,GAAG,MAAMN,GAAG,CAACO,GAAG,CAAC,iBAAiB,EAAE;IAAEJ;EAAO,CAAC,CAAC;EAC7D,OAAOF,mBAAmB,CAACK,QAAQ,CAACE,IAAI,EAAEJ,eAAe,CAAC;AAC5D,CAAC;AAED,OAAO,MAAMc,eAAe,GAAG,MAAOR,EAAE,IAAK;EAC3C,MAAMJ,QAAQ,GAAG,MAAMN,GAAG,CAACO,GAAG,CAAC,kBAAkBG,EAAE,GAAG,CAAC;EACvD,OAAOJ,QAAQ,CAACE,IAAI;AACtB,CAAC;AAED,OAAO,MAAMW,kBAAkB,GAAG,MAAOX,IAAI,IAAK;EAChD,MAAMF,QAAQ,GAAG,MAAMN,GAAG,CAACY,IAAI,CAAC,iBAAiB,EAAEJ,IAAI,CAAC;EACxD,OAAOF,QAAQ,CAACE,IAAI;AACtB,CAAC;AAED,OAAO,MAAMY,kBAAkB,GAAG,MAAAA,CAAOV,EAAE,EAAEF,IAAI,KAAK;EACpD,MAAMF,QAAQ,GAAG,MAAMN,GAAG,CAACc,GAAG,CAAC,kBAAkBJ,EAAE,GAAG,EAAEF,IAAI,CAAC;EAC7D,OAAOF,QAAQ,CAACE,IAAI;AACtB,CAAC;AAED,OAAO,MAAMa,kBAAkB,GAAG,MAAOX,EAAE,IAAK;EAC9C,MAAMV,GAAG,CAACgB,MAAM,CAAC,kBAAkBN,EAAE,GAAG,CAAC;EACzC,OAAO,IAAI;AACb,CAAC;;AAED;AACA,OAAO,MAAMY,mBAAmB,GAAG,MAAAA,CAAA,KAAY;EAC7C,IAAI;IACF,MAAMhB,QAAQ,GAAG,MAAMN,GAAG,CAACO,GAAG,CAAC,iBAAiB,EAAE;MAChDJ,MAAM,EAAE;QACNE,SAAS,EAAE,IAAI;QACfkB,KAAK,EAAE;MACT;IACF,CAAC,CAAC;IAEF,MAAMC,aAAa,GAAGlB,QAAQ,CAACE,IAAI,CAACiB,OAAO,IAAInB,QAAQ,CAACE,IAAI;IAE5D,IAAIgB,aAAa,IAAIA,aAAa,CAACE,MAAM,GAAG,CAAC,EAAE;MAC7C,OAAOF,aAAa,CAAC,CAAC,CAAC;IACzB;;IAEA;IACA,OAAO;MACLd,EAAE,EAAE,IAAI;MACRiB,IAAI,EAAE,sBAAsB;MAC5BC,KAAK,EAAE,yBAAyB;MAChCC,QAAQ,EAAE,6BAA6B;MACvCC,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACT3B,SAAS,EAAE;IACb,CAAC;EACH,CAAC,CAAC,OAAO4B,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;;IAEzD;IACA,OAAO;MACLvB,EAAE,EAAE,IAAI;MACRiB,IAAI,EAAE,sBAAsB;MAC5BC,KAAK,EAAE,yBAAyB;MAChCC,QAAQ,EAAE,6BAA6B;MACvCC,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACT3B,SAAS,EAAE;IACb,CAAC;EACH;AACF,CAAC;;AAED;AACA,OAAO,MAAM8B,UAAU,GAAG,MAAAA,CAAOhC,MAAM,GAAG,CAAC,CAAC,EAAEC,eAAe,GAAG,KAAK,KAAK;EACxE;EACA,IAAI,CAACA,eAAe,IAAI,CAACD,MAAM,CAACE,SAAS,EAAE;IACzCF,MAAM,GAAG;MAAE,GAAGA,MAAM;MAAEE,SAAS,EAAE;IAAK,CAAC;EACzC;EAEA,MAAMC,QAAQ,GAAG,MAAMN,GAAG,CAACO,GAAG,CAAC,WAAW,EAAE;IAAEJ;EAAO,CAAC,CAAC;EACvD,OAAOF,mBAAmB,CAACK,QAAQ,CAACE,IAAI,EAAEJ,eAAe,CAAC;AAC5D,CAAC;AAED,OAAO,MAAMgC,SAAS,GAAG,MAAO1B,EAAE,IAAK;EACrC,MAAMJ,QAAQ,GAAG,MAAMN,GAAG,CAACO,GAAG,CAAC,YAAYG,EAAE,GAAG,CAAC;EACjD,OAAOJ,QAAQ,CAACE,IAAI;AACtB,CAAC;AAED,OAAO,MAAM6B,YAAY,GAAG,MAAO7B,IAAI,IAAK;EAC1C,MAAMF,QAAQ,GAAG,MAAMN,GAAG,CAACY,IAAI,CAAC,WAAW,EAAEJ,IAAI,CAAC;EAClD,OAAOF,QAAQ,CAACE,IAAI;AACtB,CAAC;AAED,OAAO,MAAM8B,YAAY,GAAG,MAAAA,CAAO5B,EAAE,EAAEF,IAAI,KAAK;EAC9C,MAAMF,QAAQ,GAAG,MAAMN,GAAG,CAACc,GAAG,CAAC,YAAYJ,EAAE,GAAG,EAAEF,IAAI,CAAC;EACvD,OAAOF,QAAQ,CAACE,IAAI;AACtB,CAAC;AAED,OAAO,MAAM+B,YAAY,GAAG,MAAO7B,EAAE,IAAK;EACxC,MAAMV,GAAG,CAACgB,MAAM,CAAC,YAAYN,EAAE,GAAG,CAAC;EACnC,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}