{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project ai\\\\Store Management System\\\\asset management\\\\frontend\\\\src\\\\components\\\\Layout.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, useTheme, CssBaseline, Toolbar, Container, alpha, AppBar, IconButton, Typography, Avatar, Menu, MenuItem, ListItemIcon, Divider, Badge, Tooltip, Button, useMediaQuery } from '@mui/material';\nimport { styled } from '@mui/material/styles';\nimport { Menu as MenuIcon, Notifications as NotificationsIcon, Person as PersonIcon, Logout as LogoutIcon, Settings as SettingsIcon, Dashboard as DashboardIcon, Business as BusinessIcon, Category as CategoryIcon, Assignment as AssignmentIcon, Search as SearchIcon, ChevronRight as ChevronRightIcon, Inventory as InventoryIcon, LocalShipping as SupplierIcon, Warehouse as StorageIcon, ViewList as ItemTypeIcon, Style as ItemCategoryIcon, Group as GroupIcon, Add as AddIcon, Receipt as ReceiptIcon, List as ListIcon } from '@mui/icons-material';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { authService } from '../services/auth';\nimport { getMenuVisibility } from '../utils/permissions';\nimport api from '../utils/axios';\n\n// Styled components for the fancy layout\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst StyledAppBar = styled(AppBar)(({\n  theme\n}) => ({\n  backgroundColor: 'rgba(255, 255, 255, 0.8)',\n  backdropFilter: 'blur(10px)',\n  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)',\n  color: theme.palette.text.primary,\n  borderBottom: `1px solid ${alpha(theme.palette.divider, 0.08)}`\n}));\n_c = StyledAppBar;\nconst MainContent = styled(Box)(({\n  theme\n}) => ({\n  flexGrow: 1,\n  minHeight: 'calc(100vh - 64px)',\n  overflow: 'hidden',\n  backgroundImage: `\n    radial-gradient(at 90% 10%, ${alpha(theme.palette.primary.main, 0.05)} 0, transparent 50%),\n    radial-gradient(at 10% 90%, ${alpha(theme.palette.secondary.main, 0.05)} 0, transparent 50%)\n  `,\n  padding: theme.spacing(3),\n  paddingTop: theme.spacing(2)\n}));\n_c2 = MainContent;\nconst SideNav = styled(Box)(({\n  theme,\n  open\n}) => ({\n  position: 'fixed',\n  top: 64,\n  left: 0,\n  height: 'calc(100vh - 64px)',\n  width: open ? 240 : 0,\n  backgroundColor: theme.palette.background.paper,\n  boxShadow: '4px 0 20px rgba(0, 0, 0, 0.05)',\n  transition: theme.transitions.create('width', {\n    easing: theme.transitions.easing.sharp,\n    duration: theme.transitions.duration.enteringScreen\n  }),\n  overflow: 'hidden',\n  zIndex: 1200,\n  borderRight: `1px solid ${alpha(theme.palette.divider, 0.08)}`,\n  backgroundImage: `linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05)),\n                    radial-gradient(at top left, ${alpha(theme.palette.primary.main, 0.12)}, transparent 60%)`\n}));\n\n// Create a styled component that doesn't pass the active prop to the DOM\n_c3 = SideNav;\nconst StyledNavButton = styled(Button, {\n  shouldForwardProp: prop => prop !== 'isActive'\n})(({\n  theme,\n  isActive\n}) => ({\n  justifyContent: 'flex-start',\n  padding: theme.spacing(1.5, 3),\n  borderRadius: 0,\n  width: '100%',\n  textAlign: 'left',\n  textTransform: 'none',\n  fontWeight: 500,\n  fontSize: '0.9rem',\n  color: isActive ? theme.palette.primary.main : theme.palette.text.primary,\n  backgroundColor: isActive ? alpha(theme.palette.primary.main, 0.08) : 'transparent',\n  borderLeft: isActive ? `4px solid ${theme.palette.primary.main}` : '4px solid transparent',\n  '&:hover': {\n    backgroundColor: alpha(theme.palette.primary.main, 0.05)\n  }\n}));\n\n// Create a NavItem component that filters out the active prop\n_c4 = StyledNavButton;\nconst NavItem = ({\n  active,\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(StyledNavButton, {\n    isActive: active,\n    ...props\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 112,\n    columnNumber: 10\n  }, this);\n};\n_c5 = NavItem;\nconst ContentContainer = styled(Container)(({\n  theme,\n  sidenavopen\n}) => ({\n  transition: theme.transitions.create(['margin', 'width'], {\n    easing: theme.transitions.easing.sharp,\n    duration: theme.transitions.duration.enteringScreen\n  }),\n  marginLeft: sidenavopen === 'true' ? '240px' : 0,\n  width: sidenavopen === 'true' ? 'calc(100% - 240px)' : '100%',\n  maxWidth: '100%',\n  padding: theme.spacing(3)\n}));\n_c6 = ContentContainer;\nconst SearchBar = styled(Box)(({\n  theme\n}) => ({\n  position: 'relative',\n  borderRadius: theme.shape.borderRadius * 3,\n  backgroundColor: alpha(theme.palette.common.white, 0.15),\n  '&:hover': {\n    backgroundColor: alpha(theme.palette.common.white, 0.25)\n  },\n  marginRight: theme.spacing(2),\n  marginLeft: 0,\n  width: '100%',\n  [theme.breakpoints.up('sm')]: {\n    marginLeft: theme.spacing(3),\n    width: 'auto'\n  },\n  display: 'flex',\n  alignItems: 'center',\n  border: `1px solid ${alpha(theme.palette.divider, 0.15)}`\n}));\n_c7 = SearchBar;\nconst SearchIconWrapper = styled('div')(({\n  theme\n}) => ({\n  padding: theme.spacing(0, 2),\n  height: '100%',\n  position: 'absolute',\n  pointerEvents: 'none',\n  display: 'flex',\n  alignItems: 'center',\n  justifyContent: 'center',\n  color: alpha(theme.palette.text.primary, 0.5)\n}));\n_c8 = SearchIconWrapper;\nconst StyledInputBase = styled('input')(({\n  theme\n}) => ({\n  color: theme.palette.text.primary,\n  padding: theme.spacing(1, 1, 1, 0),\n  paddingLeft: `calc(1em + ${theme.spacing(4)})`,\n  transition: theme.transitions.create('width'),\n  width: '100%',\n  backgroundColor: 'transparent',\n  border: 'none',\n  outline: 'none',\n  fontSize: '0.9rem',\n  [theme.breakpoints.up('md')]: {\n    width: '20ch'\n  }\n}));\n_c9 = StyledInputBase;\nconst Layout = ({\n  children\n}) => {\n  _s();\n  const theme = useTheme();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n  const [sideNavOpen, setSideNavOpen] = useState(!isMobile);\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [notificationsAnchorEl, setNotificationsAnchorEl] = useState(null);\n  const [currentUser, setCurrentUser] = useState(null);\n  const [mainOrganization, setMainOrganization] = useState(null);\n  const [menuVisibility, setMenuVisibility] = useState({\n    dashboard: true,\n    organization: false,\n    classification: false,\n    specification: false,\n    storage: false,\n    supplier: false,\n    status: false,\n    inventory: false,\n    itemMasters: false,\n    batches: false,\n    items: false,\n    gatePasses: false,\n    requisition: false,\n    requisitionStatuses: false,\n    reports: false,\n    receiving: true,\n    // Enable by default for better visibility\n    itemReceive: true // Enable by default for better visibility\n  });\n  const handleProfileMenuOpen = event => {\n    setAnchorEl(event.currentTarget);\n  };\n  const handleNotificationsMenuOpen = event => {\n    setNotificationsAnchorEl(event.currentTarget);\n  };\n  const handleMenuClose = () => {\n    setAnchorEl(null);\n    setNotificationsAnchorEl(null);\n  };\n  const handleLogout = () => {\n    localStorage.removeItem('token');\n    navigate('/login');\n    handleMenuClose();\n  };\n  const toggleSideNav = () => {\n    setSideNavOpen(!sideNavOpen);\n  };\n\n  // Fetch main organization\n  const fetchMainOrganization = async () => {\n    try {\n      // First try to get the main organization\n      try {\n        const mainResponse = await api.get('/organizations/main/');\n        if (mainResponse.data) {\n          setMainOrganization(mainResponse.data);\n          return; // Exit if we found the main organization\n        }\n      } catch (mainError) {\n        console.log('No main organization set, falling back to first organization');\n      }\n\n      // Fallback to getting the first organization if no main is set\n      const response = await api.get('/organizations/');\n      if (Array.isArray(response.data) && response.data.length > 0) {\n        setMainOrganization(response.data[0]);\n      } else if (response.data && response.data.results && Array.isArray(response.data.results) && response.data.results.length > 0) {\n        setMainOrganization(response.data.results[0]);\n      } else {\n        console.error('No organizations found');\n      }\n    } catch (error) {\n      console.error('Error fetching organization:', error);\n    }\n  };\n\n  // Fetch current user and update menu visibility\n  useEffect(() => {\n    const fetchCurrentUser = async () => {\n      try {\n        const user = await authService.getCurrentUser();\n        setCurrentUser(user);\n        setMenuVisibility(getMenuVisibility(user));\n      } catch (error) {\n        console.error('Error fetching current user:', error);\n      }\n    };\n    fetchCurrentUser();\n    fetchMainOrganization();\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\n\n  // Group menu items by category\n  const dashboardItems = [{\n    text: 'Dashboard',\n    icon: /*#__PURE__*/_jsxDEV(DashboardIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 32\n    }, this),\n    path: '/dashboard'\n  }];\n  const organizationItems = [{\n    text: 'Organizations',\n    icon: /*#__PURE__*/_jsxDEV(BusinessIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 36\n    }, this),\n    path: '/organizations'\n  }, {\n    text: 'Organization Types',\n    icon: /*#__PURE__*/_jsxDEV(BusinessIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 41\n    }, this),\n    path: '/organization-types'\n  }, {\n    text: 'Offices',\n    icon: /*#__PURE__*/_jsxDEV(BusinessIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 30\n    }, this),\n    path: '/offices'\n  }];\n  const itemReceiveItems = [{\n    text: 'Item Receive Dashboard',\n    icon: /*#__PURE__*/_jsxDEV(DashboardIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 281,\n      columnNumber: 45\n    }, this),\n    path: '/item-receive'\n  }, {\n    text: 'Pre-Registration',\n    icon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 39\n    }, this),\n    path: '/item-receive/pre-registration'\n  }, {\n    text: 'Pre-Registrations List',\n    icon: /*#__PURE__*/_jsxDEV(ListIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 45\n    }, this),\n    path: '/item-receive/pre-registrations'\n  }];\n  const receivingItems = [{\n    text: 'Receiving Dashboard',\n    icon: /*#__PURE__*/_jsxDEV(DashboardIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 42\n    }, this),\n    path: '/receiving-dashboard'\n  }, {\n    text: 'Delivery Receipts',\n    icon: /*#__PURE__*/_jsxDEV(ReceiptIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 288,\n      columnNumber: 40\n    }, this),\n    path: '/delivery-receipts'\n  }, {\n    text: 'Create Delivery Receipt',\n    icon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 46\n    }, this),\n    path: '/delivery-receipt-form'\n  }, {\n    text: 'Inspections',\n    icon: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 290,\n      columnNumber: 34\n    }, this),\n    path: '/inspections'\n  }, {\n    text: 'Model 19 Receipts',\n    icon: /*#__PURE__*/_jsxDEV(AssignmentIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 40\n    }, this),\n    path: '/model19-receipts'\n  }, {\n    text: 'Create Model 19',\n    icon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 38\n    }, this),\n    path: '/model19-form'\n  }, {\n    text: 'Item Received Vouchers',\n    icon: /*#__PURE__*/_jsxDEV(ReceiptIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 45\n    }, this),\n    path: '/serial-vouchers'\n  }];\n  const classificationItems = [{\n    text: 'Main Classifications',\n    icon: /*#__PURE__*/_jsxDEV(CategoryIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 43\n    }, this),\n    path: '/main-classifications'\n  }, {\n    text: 'Sub Classifications',\n    icon: /*#__PURE__*/_jsxDEV(CategoryIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 42\n    }, this),\n    path: '/sub-classifications'\n  }];\n  const specificationItems = [{\n    text: 'Specifications Dashboard',\n    icon: /*#__PURE__*/_jsxDEV(DashboardIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 302,\n      columnNumber: 47\n    }, this),\n    path: '/specifications'\n  }];\n  const storageItems = [{\n    text: 'Storage Dashboard',\n    icon: /*#__PURE__*/_jsxDEV(DashboardIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 306,\n      columnNumber: 40\n    }, this),\n    path: '/storage-dashboard'\n  }];\n  const supplierItems = [{\n    text: 'Suppliers',\n    icon: /*#__PURE__*/_jsxDEV(SupplierIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 32\n    }, this),\n    path: '/suppliers'\n  }];\n  const statusItems = [{\n    text: 'Status Dashboard',\n    icon: /*#__PURE__*/_jsxDEV(DashboardIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 314,\n      columnNumber: 39\n    }, this),\n    path: '/status-dashboard'\n  }];\n  const gatePassItems = [{\n    text: 'Gate Passes',\n    icon: /*#__PURE__*/_jsxDEV(AssignmentIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 34\n    }, this),\n    path: '/gate-passes'\n  }];\n\n  // Reports menu removed as requested\n\n  const requisitionItems = [{\n    text: 'Requisition Dashboard',\n    icon: /*#__PURE__*/_jsxDEV(DashboardIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 44\n    }, this),\n    path: '/requisition-dashboard'\n  }];\n  const inventoryItems = [{\n    text: 'Inventory Dashboard',\n    icon: /*#__PURE__*/_jsxDEV(DashboardIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 42\n    }, this),\n    path: '/inventory-dashboard'\n  }];\n  const isMenuOpen = Boolean(anchorEl);\n  const isNotificationsOpen = Boolean(notificationsAnchorEl);\n  const renderMenu = /*#__PURE__*/_jsxDEV(Menu, {\n    anchorEl: anchorEl,\n    id: \"profile-menu\",\n    keepMounted: true,\n    open: isMenuOpen,\n    onClose: handleMenuClose,\n    sx: {\n      '& .MuiPaper-root': {\n        borderRadius: 2,\n        minWidth: 180,\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',\n        mt: 1.5\n      }\n    },\n    transformOrigin: {\n      horizontal: 'right',\n      vertical: 'top'\n    },\n    anchorOrigin: {\n      horizontal: 'right',\n      vertical: 'bottom'\n    },\n    children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n      onClick: handleMenuClose,\n      children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n        children: /*#__PURE__*/_jsxDEV(PersonIcon, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 9\n      }, this), \"Profile\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 352,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n      onClick: handleMenuClose,\n      children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n        children: /*#__PURE__*/_jsxDEV(SettingsIcon, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 359,\n        columnNumber: 9\n      }, this), \"Settings\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 358,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 364,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n      onClick: handleLogout,\n      children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n        children: /*#__PURE__*/_jsxDEV(LogoutIcon, {\n          fontSize: \"small\",\n          color: \"error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        color: \"error\",\n        children: \"Logout\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 365,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 335,\n    columnNumber: 5\n  }, this);\n  const renderNotificationsMenu = /*#__PURE__*/_jsxDEV(Menu, {\n    anchorEl: notificationsAnchorEl,\n    id: \"notifications-menu\",\n    keepMounted: true,\n    open: isNotificationsOpen,\n    onClose: handleMenuClose,\n    sx: {\n      '& .MuiPaper-root': {\n        borderRadius: 2,\n        minWidth: 280,\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',\n        mt: 1.5\n      }\n    },\n    transformOrigin: {\n      horizontal: 'right',\n      vertical: 'top'\n    },\n    anchorOrigin: {\n      horizontal: 'right',\n      vertical: 'bottom'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        fontWeight: 600,\n        children: \"Notifications\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 392,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n      onClick: handleMenuClose,\n      sx: {\n        py: 1.5\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          fontWeight: 600,\n          children: \"New gate pass request\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: \"A new gate pass has been requested\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 399,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 398,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n      onClick: handleMenuClose,\n      sx: {\n        py: 1.5\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          fontWeight: 600,\n          children: \"Organization updated\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: \"An organization has been updated\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 409,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 408,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 418,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 1,\n        display: 'flex',\n        justifyContent: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        size: \"small\",\n        endIcon: /*#__PURE__*/_jsxDEV(ChevronRightIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 20\n        }, this),\n        sx: {\n          textTransform: 'none',\n          fontWeight: 500\n        },\n        children: \"View all notifications\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 419,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 375,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex',\n      minHeight: '100vh',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 433,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(StyledAppBar, {\n      position: \"fixed\",\n      children: /*#__PURE__*/_jsxDEV(Toolbar, {\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          edge: \"start\",\n          color: \"inherit\",\n          \"aria-label\": \"menu\",\n          onClick: toggleSideNav,\n          sx: {\n            mr: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(MenuIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 438,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [mainOrganization && mainOrganization.logo_url ? /*#__PURE__*/_jsxDEV(Box, {\n            component: \"img\",\n            src: mainOrganization.logo_url,\n            alt: mainOrganization.name,\n            sx: {\n              height: 32,\n              maxWidth: 120,\n              objectFit: 'contain',\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Avatar, {\n            sx: {\n              width: 32,\n              height: 32,\n              mr: 1,\n              bgcolor: theme.palette.primary.main\n            },\n            children: mainOrganization ? mainOrganization.name.charAt(0) : 'A'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            noWrap: true,\n            component: \"div\",\n            sx: {\n              display: {\n                xs: 'none',\n                sm: 'block'\n              },\n              fontWeight: 700,\n              background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,\n              WebkitBackgroundClip: 'text',\n              WebkitTextFillColor: 'transparent'\n            },\n            children: mainOrganization ? mainOrganization.name : 'AssetManager'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SearchBar, {\n          children: [/*#__PURE__*/_jsxDEV(SearchIconWrapper, {\n            children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StyledInputBase, {\n            placeholder: \"Search\\u2026\",\n            inputProps: {\n              'aria-label': 'search'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 493,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 489,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            flexGrow: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n            title: \"Notifications\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"large\",\n              \"aria-label\": \"show notifications\",\n              color: \"inherit\",\n              onClick: handleNotificationsMenuOpen,\n              sx: {\n                mr: 1,\n                '&:hover': {\n                  backgroundColor: alpha(theme.palette.primary.main, 0.08)\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(Badge, {\n                badgeContent: 3,\n                color: \"error\",\n                children: /*#__PURE__*/_jsxDEV(NotificationsIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 516,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: \"Account\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"large\",\n              edge: \"end\",\n              \"aria-label\": \"account of current user\",\n              \"aria-haspopup\": \"true\",\n              onClick: handleProfileMenuOpen,\n              color: \"inherit\",\n              sx: {\n                '&:hover': {\n                  backgroundColor: alpha(theme.palette.primary.main, 0.08)\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  width: 32,\n                  height: 32,\n                  bgcolor: alpha(theme.palette.primary.main, 0.1),\n                  color: theme.palette.primary.main\n                },\n                children: /*#__PURE__*/_jsxDEV(PersonIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 543,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 535,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 501,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 437,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 436,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SideNav, {\n      open: sideNavOpen,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 2,\n          pt: 3,\n          overflowY: 'auto',\n          height: '100%'\n        },\n        children: [menuVisibility.dashboard && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            fontWeight: 700,\n            sx: {\n              mb: 1,\n              pl: 1,\n              color: 'text.secondary',\n              fontSize: '0.75rem',\n              letterSpacing: '0.1em'\n            },\n            children: \"DASHBOARD\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 557,\n            columnNumber: 15\n          }, this), dashboardItems.map(item => /*#__PURE__*/_jsxDEV(NavItem, {\n            startIcon: item.icon,\n            active: location.pathname === item.path,\n            onClick: () => navigate(item.path),\n            children: item.text\n          }, item.text, false, {\n            fileName: _jsxFileName,\n            lineNumber: 561,\n            columnNumber: 17\n          }, this))]\n        }, void 0, true), menuVisibility.itemReceive && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            fontWeight: 700,\n            sx: {\n              mt: 3,\n              mb: 1,\n              pl: 1,\n              color: 'text.secondary',\n              fontSize: '0.75rem',\n              letterSpacing: '0.1em'\n            },\n            children: \"ITEM RECEIVE\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 15\n          }, this), itemReceiveItems.map(item => /*#__PURE__*/_jsxDEV(NavItem, {\n            startIcon: item.icon,\n            active: location.pathname.startsWith(item.path),\n            onClick: () => navigate(item.path),\n            children: item.text\n          }, item.text, false, {\n            fileName: _jsxFileName,\n            lineNumber: 580,\n            columnNumber: 17\n          }, this))]\n        }, void 0, true), menuVisibility.receiving && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            fontWeight: 700,\n            sx: {\n              mt: 3,\n              mb: 1,\n              pl: 1,\n              color: 'text.secondary',\n              fontSize: '0.75rem',\n              letterSpacing: '0.1em'\n            },\n            children: \"RECEIVING & INSPECTION\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 595,\n            columnNumber: 15\n          }, this), receivingItems.map(item => /*#__PURE__*/_jsxDEV(NavItem, {\n            startIcon: item.icon,\n            active: location.pathname === item.path,\n            onClick: () => navigate(item.path),\n            children: item.text\n          }, item.text, false, {\n            fileName: _jsxFileName,\n            lineNumber: 599,\n            columnNumber: 17\n          }, this))]\n        }, void 0, true), menuVisibility.organization && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            fontWeight: 700,\n            sx: {\n              mt: 3,\n              mb: 1,\n              pl: 1,\n              color: 'text.secondary',\n              fontSize: '0.75rem',\n              letterSpacing: '0.1em'\n            },\n            children: \"ORGANIZATION\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 614,\n            columnNumber: 15\n          }, this), organizationItems.map(item => /*#__PURE__*/_jsxDEV(NavItem, {\n            startIcon: item.icon,\n            active: location.pathname === item.path,\n            onClick: () => navigate(item.path),\n            children: item.text\n          }, item.text, false, {\n            fileName: _jsxFileName,\n            lineNumber: 618,\n            columnNumber: 17\n          }, this))]\n        }, void 0, true), menuVisibility.classification && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            fontWeight: 700,\n            sx: {\n              mt: 3,\n              mb: 1,\n              pl: 1,\n              color: 'text.secondary',\n              fontSize: '0.75rem',\n              letterSpacing: '0.1em'\n            },\n            children: \"CLASSIFICATION\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 633,\n            columnNumber: 15\n          }, this), classificationItems.map(item => /*#__PURE__*/_jsxDEV(NavItem, {\n            startIcon: item.icon,\n            active: location.pathname === item.path,\n            onClick: () => navigate(item.path),\n            children: item.text\n          }, item.text, false, {\n            fileName: _jsxFileName,\n            lineNumber: 637,\n            columnNumber: 17\n          }, this))]\n        }, void 0, true), menuVisibility.specification && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            fontWeight: 700,\n            sx: {\n              mt: 3,\n              mb: 1,\n              pl: 1,\n              color: 'text.secondary',\n              fontSize: '0.75rem',\n              letterSpacing: '0.1em'\n            },\n            children: \"SPECIFICATIONS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 652,\n            columnNumber: 15\n          }, this), specificationItems.map(item => /*#__PURE__*/_jsxDEV(NavItem, {\n            startIcon: item.icon,\n            active: location.pathname === item.path,\n            onClick: () => navigate(item.path),\n            children: item.text\n          }, item.text, false, {\n            fileName: _jsxFileName,\n            lineNumber: 656,\n            columnNumber: 17\n          }, this))]\n        }, void 0, true), menuVisibility.storage && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            fontWeight: 700,\n            sx: {\n              mt: 3,\n              mb: 1,\n              pl: 1,\n              color: 'text.secondary',\n              fontSize: '0.75rem',\n              letterSpacing: '0.1em'\n            },\n            children: \"STORAGE\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 671,\n            columnNumber: 15\n          }, this), storageItems.map(item => /*#__PURE__*/_jsxDEV(NavItem, {\n            startIcon: item.icon,\n            active: location.pathname === item.path,\n            onClick: () => navigate(item.path),\n            children: item.text\n          }, item.text, false, {\n            fileName: _jsxFileName,\n            lineNumber: 675,\n            columnNumber: 17\n          }, this))]\n        }, void 0, true), menuVisibility.supplier && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            fontWeight: 700,\n            sx: {\n              mt: 3,\n              mb: 1,\n              pl: 1,\n              color: 'text.secondary',\n              fontSize: '0.75rem',\n              letterSpacing: '0.1em'\n            },\n            children: \"SUPPLIERS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 690,\n            columnNumber: 15\n          }, this), supplierItems.map(item => /*#__PURE__*/_jsxDEV(NavItem, {\n            startIcon: item.icon,\n            active: location.pathname === item.path,\n            onClick: () => navigate(item.path),\n            children: item.text\n          }, item.text, false, {\n            fileName: _jsxFileName,\n            lineNumber: 694,\n            columnNumber: 17\n          }, this))]\n        }, void 0, true), menuVisibility.status && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            fontWeight: 700,\n            sx: {\n              mt: 3,\n              mb: 1,\n              pl: 1,\n              color: 'text.secondary',\n              fontSize: '0.75rem',\n              letterSpacing: '0.1em'\n            },\n            children: \"STATUS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 709,\n            columnNumber: 15\n          }, this), statusItems.map(item => /*#__PURE__*/_jsxDEV(NavItem, {\n            startIcon: item.icon,\n            active: location.pathname === item.path,\n            onClick: () => navigate(item.path),\n            children: item.text\n          }, item.text, false, {\n            fileName: _jsxFileName,\n            lineNumber: 713,\n            columnNumber: 17\n          }, this))]\n        }, void 0, true), menuVisibility.inventory && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            fontWeight: 700,\n            sx: {\n              mt: 3,\n              mb: 1,\n              pl: 1,\n              color: 'text.secondary',\n              fontSize: '0.75rem',\n              letterSpacing: '0.1em'\n            },\n            children: \"INVENTORY\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 728,\n            columnNumber: 15\n          }, this), inventoryItems.map(item => {\n            // Check specific permissions for inventory items\n            if (item.text === 'Item Masters' && !menuVisibility.itemMasters || item.text === 'Batches' && !menuVisibility.batches || item.text === 'Items' && !menuVisibility.items) {\n              return null;\n            }\n            return /*#__PURE__*/_jsxDEV(NavItem, {\n              startIcon: item.icon,\n              active: location.pathname === item.path,\n              onClick: () => navigate(item.path),\n              children: item.text\n            }, item.text, false, {\n              fileName: _jsxFileName,\n              lineNumber: 739,\n              columnNumber: 19\n            }, this);\n          })]\n        }, void 0, true), menuVisibility.gatePasses && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            fontWeight: 700,\n            sx: {\n              mt: 3,\n              mb: 1,\n              pl: 1,\n              color: 'text.secondary',\n              fontSize: '0.75rem',\n              letterSpacing: '0.1em'\n            },\n            children: \"GATE PASSES\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 755,\n            columnNumber: 15\n          }, this), gatePassItems.map(item => /*#__PURE__*/_jsxDEV(NavItem, {\n            startIcon: item.icon,\n            active: location.pathname === item.path,\n            onClick: () => navigate(item.path),\n            children: item.text\n          }, item.text, false, {\n            fileName: _jsxFileName,\n            lineNumber: 759,\n            columnNumber: 17\n          }, this))]\n        }, void 0, true), menuVisibility.requisition && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            fontWeight: 700,\n            sx: {\n              mt: 3,\n              mb: 1,\n              pl: 1,\n              color: 'text.secondary',\n              fontSize: '0.75rem',\n              letterSpacing: '0.1em'\n            },\n            children: \"REQUISITIONS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 774,\n            columnNumber: 15\n          }, this), requisitionItems.map(item => {\n            // Check specific permissions for requisition items\n            if (item.text === 'Requisition Statuses' && !menuVisibility.requisitionStatuses) {\n              return null;\n            }\n            return /*#__PURE__*/_jsxDEV(NavItem, {\n              startIcon: item.icon,\n              active: location.pathname === item.path,\n              onClick: () => navigate(item.path),\n              children: item.text\n            }, item.text, false, {\n              fileName: _jsxFileName,\n              lineNumber: 783,\n              columnNumber: 19\n            }, this);\n          })]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 553,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 552,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n      children: [/*#__PURE__*/_jsxDEV(Toolbar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 802,\n        columnNumber: 9\n      }, this), \" \", /*#__PURE__*/_jsxDEV(ContentContainer, {\n        sidenavopen: sideNavOpen.toString(),\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 803,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 801,\n      columnNumber: 7\n    }, this), renderMenu, renderNotificationsMenu]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 432,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"ZoNu6olwqZSX00BnDnrOInzn07w=\", false, function () {\n  return [useTheme, useNavigate, useLocation, useMediaQuery];\n});\n_c10 = Layout;\nexport default Layout;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10;\n$RefreshReg$(_c, \"StyledAppBar\");\n$RefreshReg$(_c2, \"MainContent\");\n$RefreshReg$(_c3, \"SideNav\");\n$RefreshReg$(_c4, \"StyledNavButton\");\n$RefreshReg$(_c5, \"NavItem\");\n$RefreshReg$(_c6, \"ContentContainer\");\n$RefreshReg$(_c7, \"SearchBar\");\n$RefreshReg$(_c8, \"SearchIconWrapper\");\n$RefreshReg$(_c9, \"StyledInputBase\");\n$RefreshReg$(_c10, \"Layout\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "useTheme", "CssBaseline", "<PERSON><PERSON><PERSON>", "Container", "alpha", "AppBar", "IconButton", "Typography", "Avatar", "<PERSON><PERSON>", "MenuItem", "ListItemIcon", "Divider", "Badge", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "useMediaQuery", "styled", "MenuIcon", "Notifications", "NotificationsIcon", "Person", "PersonIcon", "Logout", "LogoutIcon", "Settings", "SettingsIcon", "Dashboard", "DashboardIcon", "Business", "BusinessIcon", "Category", "CategoryIcon", "Assignment", "AssignmentIcon", "Search", "SearchIcon", "ChevronRight", "ChevronRightIcon", "Inventory", "InventoryIcon", "LocalShipping", "SupplierIcon", "Warehouse", "StorageIcon", "ViewList", "ItemTypeIcon", "Style", "ItemCategoryIcon", "Group", "GroupIcon", "Add", "AddIcon", "Receipt", "ReceiptIcon", "List", "ListIcon", "useNavigate", "useLocation", "authService", "getMenuVisibility", "api", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "StyledAppBar", "theme", "backgroundColor", "<PERSON><PERSON>ilter", "boxShadow", "color", "palette", "text", "primary", "borderBottom", "divider", "_c", "MainContent", "flexGrow", "minHeight", "overflow", "backgroundImage", "main", "secondary", "padding", "spacing", "paddingTop", "_c2", "SideNav", "open", "position", "top", "left", "height", "width", "background", "paper", "transition", "transitions", "create", "easing", "sharp", "duration", "enteringScreen", "zIndex", "borderRight", "_c3", "StyledNavButton", "shouldForwardProp", "prop", "isActive", "justifyContent", "borderRadius", "textAlign", "textTransform", "fontWeight", "fontSize", "borderLeft", "_c4", "NavItem", "active", "props", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c5", "ContentContainer", "sidenavopen", "marginLeft", "max<PERSON><PERSON><PERSON>", "_c6", "SearchBar", "shape", "common", "white", "marginRight", "breakpoints", "up", "display", "alignItems", "border", "_c7", "SearchIconWrapper", "pointerEvents", "_c8", "StyledInputBase", "paddingLeft", "outline", "_c9", "Layout", "children", "_s", "navigate", "location", "isMobile", "down", "sideNavOpen", "setSideNavOpen", "anchorEl", "setAnchorEl", "notificationsAnchorEl", "setNotificationsAnchorEl", "currentUser", "setCurrentUser", "mainOrganization", "setMainOrganization", "menuVisibility", "setMenuVisibility", "dashboard", "organization", "classification", "specification", "storage", "supplier", "status", "inventory", "itemMasters", "batches", "items", "gatePasses", "requisition", "requisitionStatuses", "reports", "receiving", "itemReceive", "handleProfileMenuOpen", "event", "currentTarget", "handleNotificationsMenuOpen", "handleMenuClose", "handleLogout", "localStorage", "removeItem", "toggleSideNav", "fetchMainOrganization", "mainResponse", "get", "data", "mainError", "console", "log", "response", "Array", "isArray", "length", "results", "error", "fetchCurrentUser", "user", "getCurrentUser", "dashboardItems", "icon", "path", "organizationItems", "itemReceiveItems", "receivingItems", "classificationItems", "specificationItems", "storageItems", "supplierItems", "statusItems", "gatePassItems", "requisitionItems", "inventoryItems", "isMenuOpen", "Boolean", "isNotificationsOpen", "renderMenu", "id", "keepMounted", "onClose", "sx", "min<PERSON><PERSON><PERSON>", "mt", "transform<PERSON><PERSON>in", "horizontal", "vertical", "anchor<PERSON><PERSON><PERSON>", "onClick", "renderNotificationsMenu", "p", "variant", "py", "flexDirection", "size", "endIcon", "edge", "mr", "logo_url", "component", "src", "alt", "name", "objectFit", "bgcolor", "char<PERSON>t", "noWrap", "xs", "sm", "WebkitBackgroundClip", "WebkitTextFillColor", "placeholder", "inputProps", "title", "badgeContent", "pt", "overflowY", "mb", "pl", "letterSpacing", "map", "item", "startIcon", "pathname", "startsWith", "toString", "_c10", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/project ai/Store Management System/asset management/frontend/src/components/Layout.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  useTheme,\n  CssBaseline,\n  Toolbar,\n  Container,\n  alpha,\n  AppBar,\n  IconButton,\n  Typography,\n  Avatar,\n  Menu,\n  MenuItem,\n  ListItemIcon,\n  Divider,\n  Badge,\n  Tooltip,\n  Button,\n  useMediaQuery\n} from '@mui/material';\nimport { styled } from '@mui/material/styles';\nimport {\n  Menu as MenuIcon,\n  Notifications as NotificationsIcon,\n  Person as PersonIcon,\n  Logout as LogoutIcon,\n  Settings as SettingsIcon,\n  Dashboard as DashboardIcon,\n  Business as BusinessIcon,\n  Category as CategoryIcon,\n  Assignment as AssignmentIcon,\n  Search as SearchIcon,\n  ChevronRight as ChevronRightIcon,\n  Inventory as InventoryIcon,\n  LocalShipping as SupplierIcon,\n  Warehouse as StorageIcon,\n  ViewList as ItemTypeIcon,\n  Style as ItemCategoryIcon,\n  Group as GroupIcon,\n  Add as AddIcon,\n  Receipt as ReceiptIcon,\n  List as ListIcon\n} from '@mui/icons-material';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { authService } from '../services/auth';\nimport { getMenuVisibility } from '../utils/permissions';\nimport api from '../utils/axios';\n\n// Styled components for the fancy layout\nconst StyledAppBar = styled(AppBar)(({ theme }) => ({\n  backgroundColor: 'rgba(255, 255, 255, 0.8)',\n  backdropFilter: 'blur(10px)',\n  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)',\n  color: theme.palette.text.primary,\n  borderBottom: `1px solid ${alpha(theme.palette.divider, 0.08)}`,\n}));\n\nconst MainContent = styled(Box)(({ theme }) => ({\n  flexGrow: 1,\n  minHeight: 'calc(100vh - 64px)',\n  overflow: 'hidden',\n  backgroundImage: `\n    radial-gradient(at 90% 10%, ${alpha(theme.palette.primary.main, 0.05)} 0, transparent 50%),\n    radial-gradient(at 10% 90%, ${alpha(theme.palette.secondary.main, 0.05)} 0, transparent 50%)\n  `,\n  padding: theme.spacing(3),\n  paddingTop: theme.spacing(2),\n}));\n\nconst SideNav = styled(Box)(({ theme, open }) => ({\n  position: 'fixed',\n  top: 64,\n  left: 0,\n  height: 'calc(100vh - 64px)',\n  width: open ? 240 : 0,\n  backgroundColor: theme.palette.background.paper,\n  boxShadow: '4px 0 20px rgba(0, 0, 0, 0.05)',\n  transition: theme.transitions.create('width', {\n    easing: theme.transitions.easing.sharp,\n    duration: theme.transitions.duration.enteringScreen,\n  }),\n  overflow: 'hidden',\n  zIndex: 1200,\n  borderRight: `1px solid ${alpha(theme.palette.divider, 0.08)}`,\n  backgroundImage: `linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05)),\n                    radial-gradient(at top left, ${alpha(theme.palette.primary.main, 0.12)}, transparent 60%)`,\n}));\n\n// Create a styled component that doesn't pass the active prop to the DOM\nconst StyledNavButton = styled(Button, {\n  shouldForwardProp: (prop) => prop !== 'isActive',\n})(({ theme, isActive }) => ({\n  justifyContent: 'flex-start',\n  padding: theme.spacing(1.5, 3),\n  borderRadius: 0,\n  width: '100%',\n  textAlign: 'left',\n  textTransform: 'none',\n  fontWeight: 500,\n  fontSize: '0.9rem',\n  color: isActive ? theme.palette.primary.main : theme.palette.text.primary,\n  backgroundColor: isActive ? alpha(theme.palette.primary.main, 0.08) : 'transparent',\n  borderLeft: isActive ? `4px solid ${theme.palette.primary.main}` : '4px solid transparent',\n  '&:hover': {\n    backgroundColor: alpha(theme.palette.primary.main, 0.05),\n  },\n}));\n\n// Create a NavItem component that filters out the active prop\nconst NavItem = ({ active, ...props }) => {\n  return <StyledNavButton isActive={active} {...props} />;\n};\n\nconst ContentContainer = styled(Container)(({ theme, sidenavopen }) => ({\n  transition: theme.transitions.create(['margin', 'width'], {\n    easing: theme.transitions.easing.sharp,\n    duration: theme.transitions.duration.enteringScreen,\n  }),\n  marginLeft: sidenavopen === 'true' ? '240px' : 0,\n  width: sidenavopen === 'true' ? 'calc(100% - 240px)' : '100%',\n  maxWidth: '100%',\n  padding: theme.spacing(3),\n}));\n\nconst SearchBar = styled(Box)(({ theme }) => ({\n  position: 'relative',\n  borderRadius: theme.shape.borderRadius * 3,\n  backgroundColor: alpha(theme.palette.common.white, 0.15),\n  '&:hover': {\n    backgroundColor: alpha(theme.palette.common.white, 0.25),\n  },\n  marginRight: theme.spacing(2),\n  marginLeft: 0,\n  width: '100%',\n  [theme.breakpoints.up('sm')]: {\n    marginLeft: theme.spacing(3),\n    width: 'auto',\n  },\n  display: 'flex',\n  alignItems: 'center',\n  border: `1px solid ${alpha(theme.palette.divider, 0.15)}`,\n}));\n\nconst SearchIconWrapper = styled('div')(({ theme }) => ({\n  padding: theme.spacing(0, 2),\n  height: '100%',\n  position: 'absolute',\n  pointerEvents: 'none',\n  display: 'flex',\n  alignItems: 'center',\n  justifyContent: 'center',\n  color: alpha(theme.palette.text.primary, 0.5),\n}));\n\nconst StyledInputBase = styled('input')(({ theme }) => ({\n  color: theme.palette.text.primary,\n  padding: theme.spacing(1, 1, 1, 0),\n  paddingLeft: `calc(1em + ${theme.spacing(4)})`,\n  transition: theme.transitions.create('width'),\n  width: '100%',\n  backgroundColor: 'transparent',\n  border: 'none',\n  outline: 'none',\n  fontSize: '0.9rem',\n  [theme.breakpoints.up('md')]: {\n    width: '20ch',\n  },\n}));\n\nconst Layout = ({ children }) => {\n  const theme = useTheme();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n\n  const [sideNavOpen, setSideNavOpen] = useState(!isMobile);\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [notificationsAnchorEl, setNotificationsAnchorEl] = useState(null);\n  const [currentUser, setCurrentUser] = useState(null);\n  const [mainOrganization, setMainOrganization] = useState(null);\n  const [menuVisibility, setMenuVisibility] = useState({\n    dashboard: true,\n    organization: false,\n    classification: false,\n    specification: false,\n    storage: false,\n    supplier: false,\n    status: false,\n    inventory: false,\n    itemMasters: false,\n    batches: false,\n    items: false,\n    gatePasses: false,\n    requisition: false,\n    requisitionStatuses: false,\n    reports: false,\n    receiving: true, // Enable by default for better visibility\n    itemReceive: true, // Enable by default for better visibility\n  });\n\n  const handleProfileMenuOpen = (event) => {\n    setAnchorEl(event.currentTarget);\n  };\n\n  const handleNotificationsMenuOpen = (event) => {\n    setNotificationsAnchorEl(event.currentTarget);\n  };\n\n  const handleMenuClose = () => {\n    setAnchorEl(null);\n    setNotificationsAnchorEl(null);\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem('token');\n    navigate('/login');\n    handleMenuClose();\n  };\n\n  const toggleSideNav = () => {\n    setSideNavOpen(!sideNavOpen);\n  };\n\n  // Fetch main organization\n  const fetchMainOrganization = async () => {\n    try {\n      // First try to get the main organization\n      try {\n        const mainResponse = await api.get('/organizations/main/');\n        if (mainResponse.data) {\n          setMainOrganization(mainResponse.data);\n          return; // Exit if we found the main organization\n        }\n      } catch (mainError) {\n        console.log('No main organization set, falling back to first organization');\n      }\n\n      // Fallback to getting the first organization if no main is set\n      const response = await api.get('/organizations/');\n      if (Array.isArray(response.data) && response.data.length > 0) {\n        setMainOrganization(response.data[0]);\n      } else if (response.data && response.data.results && Array.isArray(response.data.results) && response.data.results.length > 0) {\n        setMainOrganization(response.data.results[0]);\n      } else {\n        console.error('No organizations found');\n      }\n    } catch (error) {\n      console.error('Error fetching organization:', error);\n    }\n  };\n\n  // Fetch current user and update menu visibility\n  useEffect(() => {\n    const fetchCurrentUser = async () => {\n      try {\n        const user = await authService.getCurrentUser();\n        setCurrentUser(user);\n        setMenuVisibility(getMenuVisibility(user));\n      } catch (error) {\n        console.error('Error fetching current user:', error);\n      }\n    };\n\n    fetchCurrentUser();\n    fetchMainOrganization();\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\n\n  // Group menu items by category\n  const dashboardItems = [\n    { text: 'Dashboard', icon: <DashboardIcon />, path: '/dashboard' },\n  ];\n\n  const organizationItems = [\n    { text: 'Organizations', icon: <BusinessIcon />, path: '/organizations' },\n    { text: 'Organization Types', icon: <BusinessIcon />, path: '/organization-types' },\n    { text: 'Offices', icon: <BusinessIcon />, path: '/offices' },\n  ];\n\n  const itemReceiveItems = [\n    { text: 'Item Receive Dashboard', icon: <DashboardIcon />, path: '/item-receive' },\n    { text: 'Pre-Registration', icon: <AddIcon />, path: '/item-receive/pre-registration' },\n    { text: 'Pre-Registrations List', icon: <ListIcon />, path: '/item-receive/pre-registrations' },\n  ];\n\n  const receivingItems = [\n    { text: 'Receiving Dashboard', icon: <DashboardIcon />, path: '/receiving-dashboard' },\n    { text: 'Delivery Receipts', icon: <ReceiptIcon />, path: '/delivery-receipts' },\n    { text: 'Create Delivery Receipt', icon: <AddIcon />, path: '/delivery-receipt-form' },\n    { text: 'Inspections', icon: <SearchIcon />, path: '/inspections' },\n    { text: 'Model 19 Receipts', icon: <AssignmentIcon />, path: '/model19-receipts' },\n    { text: 'Create Model 19', icon: <AddIcon />, path: '/model19-form' },\n    { text: 'Item Received Vouchers', icon: <ReceiptIcon />, path: '/serial-vouchers' },\n  ];\n\n  const classificationItems = [\n    { text: 'Main Classifications', icon: <CategoryIcon />, path: '/main-classifications' },\n    { text: 'Sub Classifications', icon: <CategoryIcon />, path: '/sub-classifications' },\n  ];\n\n  const specificationItems = [\n    { text: 'Specifications Dashboard', icon: <DashboardIcon />, path: '/specifications' },\n  ];\n\n  const storageItems = [\n    { text: 'Storage Dashboard', icon: <DashboardIcon />, path: '/storage-dashboard' },\n  ];\n\n  const supplierItems = [\n    { text: 'Suppliers', icon: <SupplierIcon />, path: '/suppliers' },\n  ];\n\n  const statusItems = [\n    { text: 'Status Dashboard', icon: <DashboardIcon />, path: '/status-dashboard' },\n  ];\n\n  const gatePassItems = [\n    { text: 'Gate Passes', icon: <AssignmentIcon />, path: '/gate-passes' },\n  ];\n\n  // Reports menu removed as requested\n\n  const requisitionItems = [\n    { text: 'Requisition Dashboard', icon: <DashboardIcon />, path: '/requisition-dashboard' },\n  ];\n\n  const inventoryItems = [\n    { text: 'Inventory Dashboard', icon: <DashboardIcon />, path: '/inventory-dashboard' },\n  ];\n\n  const isMenuOpen = Boolean(anchorEl);\n  const isNotificationsOpen = Boolean(notificationsAnchorEl);\n\n  const renderMenu = (\n    <Menu\n      anchorEl={anchorEl}\n      id=\"profile-menu\"\n      keepMounted\n      open={isMenuOpen}\n      onClose={handleMenuClose}\n      sx={{\n        '& .MuiPaper-root': {\n          borderRadius: 2,\n          minWidth: 180,\n          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',\n          mt: 1.5,\n        },\n      }}\n      transformOrigin={{ horizontal: 'right', vertical: 'top' }}\n      anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}\n    >\n      <MenuItem onClick={handleMenuClose}>\n        <ListItemIcon>\n          <PersonIcon fontSize=\"small\" />\n        </ListItemIcon>\n        Profile\n      </MenuItem>\n      <MenuItem onClick={handleMenuClose}>\n        <ListItemIcon>\n          <SettingsIcon fontSize=\"small\" />\n        </ListItemIcon>\n        Settings\n      </MenuItem>\n      <Divider />\n      <MenuItem onClick={handleLogout}>\n        <ListItemIcon>\n          <LogoutIcon fontSize=\"small\" color=\"error\" />\n        </ListItemIcon>\n        <Typography color=\"error\">Logout</Typography>\n      </MenuItem>\n    </Menu>\n  );\n\n  const renderNotificationsMenu = (\n    <Menu\n      anchorEl={notificationsAnchorEl}\n      id=\"notifications-menu\"\n      keepMounted\n      open={isNotificationsOpen}\n      onClose={handleMenuClose}\n      sx={{\n        '& .MuiPaper-root': {\n          borderRadius: 2,\n          minWidth: 280,\n          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',\n          mt: 1.5,\n        },\n      }}\n      transformOrigin={{ horizontal: 'right', vertical: 'top' }}\n      anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}\n    >\n      <Box sx={{ p: 2 }}>\n        <Typography variant=\"h6\" fontWeight={600}>\n          Notifications\n        </Typography>\n      </Box>\n      <Divider />\n      <MenuItem onClick={handleMenuClose} sx={{ py: 1.5 }}>\n        <Box sx={{ display: 'flex', flexDirection: 'column' }}>\n          <Typography variant=\"subtitle2\" fontWeight={600}>\n            New gate pass request\n          </Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            A new gate pass has been requested\n          </Typography>\n        </Box>\n      </MenuItem>\n      <MenuItem onClick={handleMenuClose} sx={{ py: 1.5 }}>\n        <Box sx={{ display: 'flex', flexDirection: 'column' }}>\n          <Typography variant=\"subtitle2\" fontWeight={600}>\n            Organization updated\n          </Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            An organization has been updated\n          </Typography>\n        </Box>\n      </MenuItem>\n      <Divider />\n      <Box sx={{ p: 1, display: 'flex', justifyContent: 'center' }}>\n        <Button\n          size=\"small\"\n          endIcon={<ChevronRightIcon />}\n          sx={{ textTransform: 'none', fontWeight: 500 }}\n        >\n          View all notifications\n        </Button>\n      </Box>\n    </Menu>\n  );\n\n  return (\n    <Box sx={{ display: 'flex', minHeight: '100vh', overflow: 'hidden' }}>\n      <CssBaseline />\n\n      {/* Top App Bar */}\n      <StyledAppBar position=\"fixed\">\n        <Toolbar>\n          <IconButton\n            edge=\"start\"\n            color=\"inherit\"\n            aria-label=\"menu\"\n            onClick={toggleSideNav}\n            sx={{ mr: 2 }}\n          >\n            <MenuIcon />\n          </IconButton>\n\n          <Box sx={{ display: 'flex', alignItems: 'center' }}>\n            {mainOrganization && mainOrganization.logo_url ? (\n              <Box\n                component=\"img\"\n                src={mainOrganization.logo_url}\n                alt={mainOrganization.name}\n                sx={{\n                  height: 32,\n                  maxWidth: 120,\n                  objectFit: 'contain',\n                  mr: 1\n                }}\n              />\n            ) : (\n              <Avatar\n                sx={{\n                  width: 32,\n                  height: 32,\n                  mr: 1,\n                  bgcolor: theme.palette.primary.main,\n                }}\n              >\n                {mainOrganization ? mainOrganization.name.charAt(0) : 'A'}\n              </Avatar>\n            )}\n            <Typography\n              variant=\"h6\"\n              noWrap\n              component=\"div\"\n              sx={{\n                display: { xs: 'none', sm: 'block' },\n                fontWeight: 700,\n                background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,\n                WebkitBackgroundClip: 'text',\n                WebkitTextFillColor: 'transparent',\n              }}\n            >\n              {mainOrganization ? mainOrganization.name : 'AssetManager'}\n            </Typography>\n          </Box>\n\n          <SearchBar>\n            <SearchIconWrapper>\n              <SearchIcon />\n            </SearchIconWrapper>\n            <StyledInputBase\n              placeholder=\"Search…\"\n              inputProps={{ 'aria-label': 'search' }}\n            />\n          </SearchBar>\n\n          <Box sx={{ flexGrow: 1 }} />\n\n          <Box sx={{ display: 'flex', alignItems: 'center' }}>\n            <Tooltip title=\"Notifications\">\n              <IconButton\n                size=\"large\"\n                aria-label=\"show notifications\"\n                color=\"inherit\"\n                onClick={handleNotificationsMenuOpen}\n                sx={{\n                  mr: 1,\n                  '&:hover': {\n                    backgroundColor: alpha(theme.palette.primary.main, 0.08),\n                  },\n                }}\n              >\n                <Badge badgeContent={3} color=\"error\">\n                  <NotificationsIcon />\n                </Badge>\n              </IconButton>\n            </Tooltip>\n\n            <Tooltip title=\"Account\">\n              <IconButton\n                size=\"large\"\n                edge=\"end\"\n                aria-label=\"account of current user\"\n                aria-haspopup=\"true\"\n                onClick={handleProfileMenuOpen}\n                color=\"inherit\"\n                sx={{\n                  '&:hover': {\n                    backgroundColor: alpha(theme.palette.primary.main, 0.08),\n                  },\n                }}\n              >\n                <Avatar\n                  sx={{\n                    width: 32,\n                    height: 32,\n                    bgcolor: alpha(theme.palette.primary.main, 0.1),\n                    color: theme.palette.primary.main,\n                  }}\n                >\n                  <PersonIcon fontSize=\"small\" />\n                </Avatar>\n              </IconButton>\n            </Tooltip>\n          </Box>\n        </Toolbar>\n      </StyledAppBar>\n\n      {/* Side Navigation */}\n      <SideNav open={sideNavOpen}>\n        <Box sx={{ p: 2, pt: 3, overflowY: 'auto', height: '100%' }}>\n          {/* Dashboard Section */}\n          {menuVisibility.dashboard && (\n            <>\n              <Typography variant=\"subtitle2\" fontWeight={700} sx={{ mb: 1, pl: 1, color: 'text.secondary', fontSize: '0.75rem', letterSpacing: '0.1em' }}>\n                DASHBOARD\n              </Typography>\n              {dashboardItems.map((item) => (\n                <NavItem\n                  key={item.text}\n                  startIcon={item.icon}\n                  active={location.pathname === item.path}\n                  onClick={() => navigate(item.path)}\n                >\n                  {item.text}\n                </NavItem>\n              ))}\n            </>\n          )}\n\n          {/* Item Receive Section */}\n          {menuVisibility.itemReceive && (\n            <>\n              <Typography variant=\"subtitle2\" fontWeight={700} sx={{ mt: 3, mb: 1, pl: 1, color: 'text.secondary', fontSize: '0.75rem', letterSpacing: '0.1em' }}>\n                ITEM RECEIVE\n              </Typography>\n              {itemReceiveItems.map((item) => (\n                <NavItem\n                  key={item.text}\n                  startIcon={item.icon}\n                  active={location.pathname.startsWith(item.path)}\n                  onClick={() => navigate(item.path)}\n                >\n                  {item.text}\n                </NavItem>\n              ))}\n            </>\n          )}\n\n          {/* Receiving & Inspection Section */}\n          {menuVisibility.receiving && (\n            <>\n              <Typography variant=\"subtitle2\" fontWeight={700} sx={{ mt: 3, mb: 1, pl: 1, color: 'text.secondary', fontSize: '0.75rem', letterSpacing: '0.1em' }}>\n                RECEIVING & INSPECTION\n              </Typography>\n              {receivingItems.map((item) => (\n                <NavItem\n                  key={item.text}\n                  startIcon={item.icon}\n                  active={location.pathname === item.path}\n                  onClick={() => navigate(item.path)}\n                >\n                  {item.text}\n                </NavItem>\n              ))}\n            </>\n          )}\n\n          {/* Organization Section */}\n          {menuVisibility.organization && (\n            <>\n              <Typography variant=\"subtitle2\" fontWeight={700} sx={{ mt: 3, mb: 1, pl: 1, color: 'text.secondary', fontSize: '0.75rem', letterSpacing: '0.1em' }}>\n                ORGANIZATION\n              </Typography>\n              {organizationItems.map((item) => (\n                <NavItem\n                  key={item.text}\n                  startIcon={item.icon}\n                  active={location.pathname === item.path}\n                  onClick={() => navigate(item.path)}\n                >\n                  {item.text}\n                </NavItem>\n              ))}\n            </>\n          )}\n\n          {/* Classification Section */}\n          {menuVisibility.classification && (\n            <>\n              <Typography variant=\"subtitle2\" fontWeight={700} sx={{ mt: 3, mb: 1, pl: 1, color: 'text.secondary', fontSize: '0.75rem', letterSpacing: '0.1em' }}>\n                CLASSIFICATION\n              </Typography>\n              {classificationItems.map((item) => (\n                <NavItem\n                  key={item.text}\n                  startIcon={item.icon}\n                  active={location.pathname === item.path}\n                  onClick={() => navigate(item.path)}\n                >\n                  {item.text}\n                </NavItem>\n              ))}\n            </>\n          )}\n\n          {/* Specification Section */}\n          {menuVisibility.specification && (\n            <>\n              <Typography variant=\"subtitle2\" fontWeight={700} sx={{ mt: 3, mb: 1, pl: 1, color: 'text.secondary', fontSize: '0.75rem', letterSpacing: '0.1em' }}>\n                SPECIFICATIONS\n              </Typography>\n              {specificationItems.map((item) => (\n                <NavItem\n                  key={item.text}\n                  startIcon={item.icon}\n                  active={location.pathname === item.path}\n                  onClick={() => navigate(item.path)}\n                >\n                  {item.text}\n                </NavItem>\n              ))}\n            </>\n          )}\n\n          {/* Storage Section */}\n          {menuVisibility.storage && (\n            <>\n              <Typography variant=\"subtitle2\" fontWeight={700} sx={{ mt: 3, mb: 1, pl: 1, color: 'text.secondary', fontSize: '0.75rem', letterSpacing: '0.1em' }}>\n                STORAGE\n              </Typography>\n              {storageItems.map((item) => (\n                <NavItem\n                  key={item.text}\n                  startIcon={item.icon}\n                  active={location.pathname === item.path}\n                  onClick={() => navigate(item.path)}\n                >\n                  {item.text}\n                </NavItem>\n              ))}\n            </>\n          )}\n\n          {/* Supplier Section */}\n          {menuVisibility.supplier && (\n            <>\n              <Typography variant=\"subtitle2\" fontWeight={700} sx={{ mt: 3, mb: 1, pl: 1, color: 'text.secondary', fontSize: '0.75rem', letterSpacing: '0.1em' }}>\n                SUPPLIERS\n              </Typography>\n              {supplierItems.map((item) => (\n                <NavItem\n                  key={item.text}\n                  startIcon={item.icon}\n                  active={location.pathname === item.path}\n                  onClick={() => navigate(item.path)}\n                >\n                  {item.text}\n                </NavItem>\n              ))}\n            </>\n          )}\n\n          {/* Status Section */}\n          {menuVisibility.status && (\n            <>\n              <Typography variant=\"subtitle2\" fontWeight={700} sx={{ mt: 3, mb: 1, pl: 1, color: 'text.secondary', fontSize: '0.75rem', letterSpacing: '0.1em' }}>\n                STATUS\n              </Typography>\n              {statusItems.map((item) => (\n                <NavItem\n                  key={item.text}\n                  startIcon={item.icon}\n                  active={location.pathname === item.path}\n                  onClick={() => navigate(item.path)}\n                >\n                  {item.text}\n                </NavItem>\n              ))}\n            </>\n          )}\n\n          {/* Inventory Section */}\n          {menuVisibility.inventory && (\n            <>\n              <Typography variant=\"subtitle2\" fontWeight={700} sx={{ mt: 3, mb: 1, pl: 1, color: 'text.secondary', fontSize: '0.75rem', letterSpacing: '0.1em' }}>\n                INVENTORY\n              </Typography>\n              {inventoryItems.map((item) => {\n                // Check specific permissions for inventory items\n                if ((item.text === 'Item Masters' && !menuVisibility.itemMasters) ||\n                    (item.text === 'Batches' && !menuVisibility.batches) ||\n                    (item.text === 'Items' && !menuVisibility.items)) {\n                  return null;\n                }\n                return (\n                  <NavItem\n                    key={item.text}\n                    startIcon={item.icon}\n                    active={location.pathname === item.path}\n                    onClick={() => navigate(item.path)}\n                  >\n                    {item.text}\n                  </NavItem>\n                );\n              })}\n            </>\n          )}\n\n          {/* Gate Pass Section */}\n          {menuVisibility.gatePasses && (\n            <>\n              <Typography variant=\"subtitle2\" fontWeight={700} sx={{ mt: 3, mb: 1, pl: 1, color: 'text.secondary', fontSize: '0.75rem', letterSpacing: '0.1em' }}>\n                GATE PASSES\n              </Typography>\n              {gatePassItems.map((item) => (\n                <NavItem\n                  key={item.text}\n                  startIcon={item.icon}\n                  active={location.pathname === item.path}\n                  onClick={() => navigate(item.path)}\n                >\n                  {item.text}\n                </NavItem>\n              ))}\n            </>\n          )}\n\n          {/* Requisition Section */}\n          {menuVisibility.requisition && (\n            <>\n              <Typography variant=\"subtitle2\" fontWeight={700} sx={{ mt: 3, mb: 1, pl: 1, color: 'text.secondary', fontSize: '0.75rem', letterSpacing: '0.1em' }}>\n                REQUISITIONS\n              </Typography>\n              {requisitionItems.map((item) => {\n                // Check specific permissions for requisition items\n                if (item.text === 'Requisition Statuses' && !menuVisibility.requisitionStatuses) {\n                  return null;\n                }\n                return (\n                  <NavItem\n                    key={item.text}\n                    startIcon={item.icon}\n                    active={location.pathname === item.path}\n                    onClick={() => navigate(item.path)}\n                  >\n                    {item.text}\n                  </NavItem>\n                );\n              })}\n            </>\n          )}\n\n          {/* Reports Section removed as requested */}\n        </Box>\n      </SideNav>\n\n      {/* Main Content */}\n      <MainContent>\n        <Toolbar /> {/* Spacer for AppBar */}\n        <ContentContainer sidenavopen={sideNavOpen.toString()}>\n          {children}\n        </ContentContainer>\n      </MainContent>\n\n      {renderMenu}\n      {renderNotificationsMenu}\n    </Box>\n  );\n};\n\nexport default Layout;\n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,QAAQ,EACRC,WAAW,EACXC,OAAO,EACPC,SAAS,EACTC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,OAAO,EACPC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,aAAa,QACR,eAAe;AACtB,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SACER,IAAI,IAAIS,QAAQ,EAChBC,aAAa,IAAIC,iBAAiB,EAClCC,MAAM,IAAIC,UAAU,EACpBC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,SAAS,IAAIC,aAAa,EAC1BC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,EAC5BC,MAAM,IAAIC,UAAU,EACpBC,YAAY,IAAIC,gBAAgB,EAChCC,SAAS,IAAIC,aAAa,EAC1BC,aAAa,IAAIC,YAAY,EAC7BC,SAAS,IAAIC,WAAW,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,KAAK,IAAIC,gBAAgB,EACzBC,KAAK,IAAIC,SAAS,EAClBC,GAAG,IAAIC,OAAO,EACdC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,OAAOC,GAAG,MAAM,gBAAgB;;AAEhC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,YAAY,GAAGjD,MAAM,CAACZ,MAAM,CAAC,CAAC,CAAC;EAAE8D;AAAM,CAAC,MAAM;EAClDC,eAAe,EAAE,0BAA0B;EAC3CC,cAAc,EAAE,YAAY;EAC5BC,SAAS,EAAE,gCAAgC;EAC3CC,KAAK,EAAEJ,KAAK,CAACK,OAAO,CAACC,IAAI,CAACC,OAAO;EACjCC,YAAY,EAAE,aAAavE,KAAK,CAAC+D,KAAK,CAACK,OAAO,CAACI,OAAO,EAAE,IAAI,CAAC;AAC/D,CAAC,CAAC,CAAC;AAACC,EAAA,GANEX,YAAY;AAQlB,MAAMY,WAAW,GAAG7D,MAAM,CAAClB,GAAG,CAAC,CAAC,CAAC;EAAEoE;AAAM,CAAC,MAAM;EAC9CY,QAAQ,EAAE,CAAC;EACXC,SAAS,EAAE,oBAAoB;EAC/BC,QAAQ,EAAE,QAAQ;EAClBC,eAAe,EAAE;AACnB,kCAAkC9E,KAAK,CAAC+D,KAAK,CAACK,OAAO,CAACE,OAAO,CAACS,IAAI,EAAE,IAAI,CAAC;AACzE,kCAAkC/E,KAAK,CAAC+D,KAAK,CAACK,OAAO,CAACY,SAAS,CAACD,IAAI,EAAE,IAAI,CAAC;AAC3E,GAAG;EACDE,OAAO,EAAElB,KAAK,CAACmB,OAAO,CAAC,CAAC,CAAC;EACzBC,UAAU,EAAEpB,KAAK,CAACmB,OAAO,CAAC,CAAC;AAC7B,CAAC,CAAC,CAAC;AAACE,GAAA,GAVEV,WAAW;AAYjB,MAAMW,OAAO,GAAGxE,MAAM,CAAClB,GAAG,CAAC,CAAC,CAAC;EAAEoE,KAAK;EAAEuB;AAAK,CAAC,MAAM;EAChDC,QAAQ,EAAE,OAAO;EACjBC,GAAG,EAAE,EAAE;EACPC,IAAI,EAAE,CAAC;EACPC,MAAM,EAAE,oBAAoB;EAC5BC,KAAK,EAAEL,IAAI,GAAG,GAAG,GAAG,CAAC;EACrBtB,eAAe,EAAED,KAAK,CAACK,OAAO,CAACwB,UAAU,CAACC,KAAK;EAC/C3B,SAAS,EAAE,gCAAgC;EAC3C4B,UAAU,EAAE/B,KAAK,CAACgC,WAAW,CAACC,MAAM,CAAC,OAAO,EAAE;IAC5CC,MAAM,EAAElC,KAAK,CAACgC,WAAW,CAACE,MAAM,CAACC,KAAK;IACtCC,QAAQ,EAAEpC,KAAK,CAACgC,WAAW,CAACI,QAAQ,CAACC;EACvC,CAAC,CAAC;EACFvB,QAAQ,EAAE,QAAQ;EAClBwB,MAAM,EAAE,IAAI;EACZC,WAAW,EAAE,aAAatG,KAAK,CAAC+D,KAAK,CAACK,OAAO,CAACI,OAAO,EAAE,IAAI,CAAC,EAAE;EAC9DM,eAAe,EAAE;AACnB,mDAAmD9E,KAAK,CAAC+D,KAAK,CAACK,OAAO,CAACE,OAAO,CAACS,IAAI,EAAE,IAAI,CAAC;AAC1F,CAAC,CAAC,CAAC;;AAEH;AAAAwB,GAAA,GAnBMlB,OAAO;AAoBb,MAAMmB,eAAe,GAAG3F,MAAM,CAACF,MAAM,EAAE;EACrC8F,iBAAiB,EAAGC,IAAI,IAAKA,IAAI,KAAK;AACxC,CAAC,CAAC,CAAC,CAAC;EAAE3C,KAAK;EAAE4C;AAAS,CAAC,MAAM;EAC3BC,cAAc,EAAE,YAAY;EAC5B3B,OAAO,EAAElB,KAAK,CAACmB,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;EAC9B2B,YAAY,EAAE,CAAC;EACflB,KAAK,EAAE,MAAM;EACbmB,SAAS,EAAE,MAAM;EACjBC,aAAa,EAAE,MAAM;EACrBC,UAAU,EAAE,GAAG;EACfC,QAAQ,EAAE,QAAQ;EAClB9C,KAAK,EAAEwC,QAAQ,GAAG5C,KAAK,CAACK,OAAO,CAACE,OAAO,CAACS,IAAI,GAAGhB,KAAK,CAACK,OAAO,CAACC,IAAI,CAACC,OAAO;EACzEN,eAAe,EAAE2C,QAAQ,GAAG3G,KAAK,CAAC+D,KAAK,CAACK,OAAO,CAACE,OAAO,CAACS,IAAI,EAAE,IAAI,CAAC,GAAG,aAAa;EACnFmC,UAAU,EAAEP,QAAQ,GAAG,aAAa5C,KAAK,CAACK,OAAO,CAACE,OAAO,CAACS,IAAI,EAAE,GAAG,uBAAuB;EAC1F,SAAS,EAAE;IACTf,eAAe,EAAEhE,KAAK,CAAC+D,KAAK,CAACK,OAAO,CAACE,OAAO,CAACS,IAAI,EAAE,IAAI;EACzD;AACF,CAAC,CAAC,CAAC;;AAEH;AAAAoC,GAAA,GAnBMX,eAAe;AAoBrB,MAAMY,OAAO,GAAGA,CAAC;EAAEC,MAAM;EAAE,GAAGC;AAAM,CAAC,KAAK;EACxC,oBAAO3D,OAAA,CAAC6C,eAAe;IAACG,QAAQ,EAAEU,MAAO;IAAA,GAAKC;EAAK;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;AACzD,CAAC;AAACC,GAAA,GAFIP,OAAO;AAIb,MAAMQ,gBAAgB,GAAG/G,MAAM,CAACd,SAAS,CAAC,CAAC,CAAC;EAAEgE,KAAK;EAAE8D;AAAY,CAAC,MAAM;EACtE/B,UAAU,EAAE/B,KAAK,CAACgC,WAAW,CAACC,MAAM,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE;IACxDC,MAAM,EAAElC,KAAK,CAACgC,WAAW,CAACE,MAAM,CAACC,KAAK;IACtCC,QAAQ,EAAEpC,KAAK,CAACgC,WAAW,CAACI,QAAQ,CAACC;EACvC,CAAC,CAAC;EACF0B,UAAU,EAAED,WAAW,KAAK,MAAM,GAAG,OAAO,GAAG,CAAC;EAChDlC,KAAK,EAAEkC,WAAW,KAAK,MAAM,GAAG,oBAAoB,GAAG,MAAM;EAC7DE,QAAQ,EAAE,MAAM;EAChB9C,OAAO,EAAElB,KAAK,CAACmB,OAAO,CAAC,CAAC;AAC1B,CAAC,CAAC,CAAC;AAAC8C,GAAA,GATEJ,gBAAgB;AAWtB,MAAMK,SAAS,GAAGpH,MAAM,CAAClB,GAAG,CAAC,CAAC,CAAC;EAAEoE;AAAM,CAAC,MAAM;EAC5CwB,QAAQ,EAAE,UAAU;EACpBsB,YAAY,EAAE9C,KAAK,CAACmE,KAAK,CAACrB,YAAY,GAAG,CAAC;EAC1C7C,eAAe,EAAEhE,KAAK,CAAC+D,KAAK,CAACK,OAAO,CAAC+D,MAAM,CAACC,KAAK,EAAE,IAAI,CAAC;EACxD,SAAS,EAAE;IACTpE,eAAe,EAAEhE,KAAK,CAAC+D,KAAK,CAACK,OAAO,CAAC+D,MAAM,CAACC,KAAK,EAAE,IAAI;EACzD,CAAC;EACDC,WAAW,EAAEtE,KAAK,CAACmB,OAAO,CAAC,CAAC,CAAC;EAC7B4C,UAAU,EAAE,CAAC;EACbnC,KAAK,EAAE,MAAM;EACb,CAAC5B,KAAK,CAACuE,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,GAAG;IAC5BT,UAAU,EAAE/D,KAAK,CAACmB,OAAO,CAAC,CAAC,CAAC;IAC5BS,KAAK,EAAE;EACT,CAAC;EACD6C,OAAO,EAAE,MAAM;EACfC,UAAU,EAAE,QAAQ;EACpBC,MAAM,EAAE,aAAa1I,KAAK,CAAC+D,KAAK,CAACK,OAAO,CAACI,OAAO,EAAE,IAAI,CAAC;AACzD,CAAC,CAAC,CAAC;AAACmE,GAAA,GAjBEV,SAAS;AAmBf,MAAMW,iBAAiB,GAAG/H,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;EAAEkD;AAAM,CAAC,MAAM;EACtDkB,OAAO,EAAElB,KAAK,CAACmB,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EAC5BQ,MAAM,EAAE,MAAM;EACdH,QAAQ,EAAE,UAAU;EACpBsD,aAAa,EAAE,MAAM;EACrBL,OAAO,EAAE,MAAM;EACfC,UAAU,EAAE,QAAQ;EACpB7B,cAAc,EAAE,QAAQ;EACxBzC,KAAK,EAAEnE,KAAK,CAAC+D,KAAK,CAACK,OAAO,CAACC,IAAI,CAACC,OAAO,EAAE,GAAG;AAC9C,CAAC,CAAC,CAAC;AAACwE,GAAA,GATEF,iBAAiB;AAWvB,MAAMG,eAAe,GAAGlI,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;EAAEkD;AAAM,CAAC,MAAM;EACtDI,KAAK,EAAEJ,KAAK,CAACK,OAAO,CAACC,IAAI,CAACC,OAAO;EACjCW,OAAO,EAAElB,KAAK,CAACmB,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAClC8D,WAAW,EAAE,cAAcjF,KAAK,CAACmB,OAAO,CAAC,CAAC,CAAC,GAAG;EAC9CY,UAAU,EAAE/B,KAAK,CAACgC,WAAW,CAACC,MAAM,CAAC,OAAO,CAAC;EAC7CL,KAAK,EAAE,MAAM;EACb3B,eAAe,EAAE,aAAa;EAC9B0E,MAAM,EAAE,MAAM;EACdO,OAAO,EAAE,MAAM;EACfhC,QAAQ,EAAE,QAAQ;EAClB,CAAClD,KAAK,CAACuE,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,GAAG;IAC5B5C,KAAK,EAAE;EACT;AACF,CAAC,CAAC,CAAC;AAACuD,GAAA,GAbEH,eAAe;AAerB,MAAMI,MAAM,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC/B,MAAMtF,KAAK,GAAGnE,QAAQ,CAAC,CAAC;EACxB,MAAM0J,QAAQ,GAAGjG,WAAW,CAAC,CAAC;EAC9B,MAAMkG,QAAQ,GAAGjG,WAAW,CAAC,CAAC;EAC9B,MAAMkG,QAAQ,GAAG5I,aAAa,CAACmD,KAAK,CAACuE,WAAW,CAACmB,IAAI,CAAC,IAAI,CAAC,CAAC;EAE5D,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGlK,QAAQ,CAAC,CAAC+J,QAAQ,CAAC;EACzD,MAAM,CAACI,QAAQ,EAAEC,WAAW,CAAC,GAAGpK,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACqK,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGtK,QAAQ,CAAC,IAAI,CAAC;EACxE,MAAM,CAACuK,WAAW,EAAEC,cAAc,CAAC,GAAGxK,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACyK,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1K,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC2K,cAAc,EAAEC,iBAAiB,CAAC,GAAG5K,QAAQ,CAAC;IACnD6K,SAAS,EAAE,IAAI;IACfC,YAAY,EAAE,KAAK;IACnBC,cAAc,EAAE,KAAK;IACrBC,aAAa,EAAE,KAAK;IACpBC,OAAO,EAAE,KAAK;IACdC,QAAQ,EAAE,KAAK;IACfC,MAAM,EAAE,KAAK;IACbC,SAAS,EAAE,KAAK;IAChBC,WAAW,EAAE,KAAK;IAClBC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE,KAAK;IACZC,UAAU,EAAE,KAAK;IACjBC,WAAW,EAAE,KAAK;IAClBC,mBAAmB,EAAE,KAAK;IAC1BC,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE,IAAI;IAAE;IACjBC,WAAW,EAAE,IAAI,CAAE;EACrB,CAAC,CAAC;EAEF,MAAMC,qBAAqB,GAAIC,KAAK,IAAK;IACvC3B,WAAW,CAAC2B,KAAK,CAACC,aAAa,CAAC;EAClC,CAAC;EAED,MAAMC,2BAA2B,GAAIF,KAAK,IAAK;IAC7CzB,wBAAwB,CAACyB,KAAK,CAACC,aAAa,CAAC;EAC/C,CAAC;EAED,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC5B9B,WAAW,CAAC,IAAI,CAAC;IACjBE,wBAAwB,CAAC,IAAI,CAAC;EAChC,CAAC;EAED,MAAM6B,YAAY,GAAGA,CAAA,KAAM;IACzBC,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChCxC,QAAQ,CAAC,QAAQ,CAAC;IAClBqC,eAAe,CAAC,CAAC;EACnB,CAAC;EAED,MAAMI,aAAa,GAAGA,CAAA,KAAM;IAC1BpC,cAAc,CAAC,CAACD,WAAW,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMsC,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACF;MACA,IAAI;QACF,MAAMC,YAAY,GAAG,MAAMxI,GAAG,CAACyI,GAAG,CAAC,sBAAsB,CAAC;QAC1D,IAAID,YAAY,CAACE,IAAI,EAAE;UACrBhC,mBAAmB,CAAC8B,YAAY,CAACE,IAAI,CAAC;UACtC,OAAO,CAAC;QACV;MACF,CAAC,CAAC,OAAOC,SAAS,EAAE;QAClBC,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;MAC7E;;MAEA;MACA,MAAMC,QAAQ,GAAG,MAAM9I,GAAG,CAACyI,GAAG,CAAC,iBAAiB,CAAC;MACjD,IAAIM,KAAK,CAACC,OAAO,CAACF,QAAQ,CAACJ,IAAI,CAAC,IAAII,QAAQ,CAACJ,IAAI,CAACO,MAAM,GAAG,CAAC,EAAE;QAC5DvC,mBAAmB,CAACoC,QAAQ,CAACJ,IAAI,CAAC,CAAC,CAAC,CAAC;MACvC,CAAC,MAAM,IAAII,QAAQ,CAACJ,IAAI,IAAII,QAAQ,CAACJ,IAAI,CAACQ,OAAO,IAAIH,KAAK,CAACC,OAAO,CAACF,QAAQ,CAACJ,IAAI,CAACQ,OAAO,CAAC,IAAIJ,QAAQ,CAACJ,IAAI,CAACQ,OAAO,CAACD,MAAM,GAAG,CAAC,EAAE;QAC7HvC,mBAAmB,CAACoC,QAAQ,CAACJ,IAAI,CAACQ,OAAO,CAAC,CAAC,CAAC,CAAC;MAC/C,CAAC,MAAM;QACLN,OAAO,CAACO,KAAK,CAAC,wBAAwB,CAAC;MACzC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;;EAED;EACAlN,SAAS,CAAC,MAAM;IACd,MAAMmN,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACF,MAAMC,IAAI,GAAG,MAAMvJ,WAAW,CAACwJ,cAAc,CAAC,CAAC;QAC/C9C,cAAc,CAAC6C,IAAI,CAAC;QACpBzC,iBAAiB,CAAC7G,iBAAiB,CAACsJ,IAAI,CAAC,CAAC;MAC5C,CAAC,CAAC,OAAOF,KAAK,EAAE;QACdP,OAAO,CAACO,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD;IACF,CAAC;IAEDC,gBAAgB,CAAC,CAAC;IAClBb,qBAAqB,CAAC,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACA,MAAMgB,cAAc,GAAG,CACrB;IAAE3I,IAAI,EAAE,WAAW;IAAE4I,IAAI,eAAEtJ,OAAA,CAACnC,aAAa;MAAA+F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEwF,IAAI,EAAE;EAAa,CAAC,CACnE;EAED,MAAMC,iBAAiB,GAAG,CACxB;IAAE9I,IAAI,EAAE,eAAe;IAAE4I,IAAI,eAAEtJ,OAAA,CAACjC,YAAY;MAAA6F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEwF,IAAI,EAAE;EAAiB,CAAC,EACzE;IAAE7I,IAAI,EAAE,oBAAoB;IAAE4I,IAAI,eAAEtJ,OAAA,CAACjC,YAAY;MAAA6F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEwF,IAAI,EAAE;EAAsB,CAAC,EACnF;IAAE7I,IAAI,EAAE,SAAS;IAAE4I,IAAI,eAAEtJ,OAAA,CAACjC,YAAY;MAAA6F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEwF,IAAI,EAAE;EAAW,CAAC,CAC9D;EAED,MAAME,gBAAgB,GAAG,CACvB;IAAE/I,IAAI,EAAE,wBAAwB;IAAE4I,IAAI,eAAEtJ,OAAA,CAACnC,aAAa;MAAA+F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEwF,IAAI,EAAE;EAAgB,CAAC,EAClF;IAAE7I,IAAI,EAAE,kBAAkB;IAAE4I,IAAI,eAAEtJ,OAAA,CAACX,OAAO;MAAAuE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEwF,IAAI,EAAE;EAAiC,CAAC,EACvF;IAAE7I,IAAI,EAAE,wBAAwB;IAAE4I,IAAI,eAAEtJ,OAAA,CAACP,QAAQ;MAAAmE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEwF,IAAI,EAAE;EAAkC,CAAC,CAChG;EAED,MAAMG,cAAc,GAAG,CACrB;IAAEhJ,IAAI,EAAE,qBAAqB;IAAE4I,IAAI,eAAEtJ,OAAA,CAACnC,aAAa;MAAA+F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEwF,IAAI,EAAE;EAAuB,CAAC,EACtF;IAAE7I,IAAI,EAAE,mBAAmB;IAAE4I,IAAI,eAAEtJ,OAAA,CAACT,WAAW;MAAAqE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEwF,IAAI,EAAE;EAAqB,CAAC,EAChF;IAAE7I,IAAI,EAAE,yBAAyB;IAAE4I,IAAI,eAAEtJ,OAAA,CAACX,OAAO;MAAAuE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEwF,IAAI,EAAE;EAAyB,CAAC,EACtF;IAAE7I,IAAI,EAAE,aAAa;IAAE4I,IAAI,eAAEtJ,OAAA,CAAC3B,UAAU;MAAAuF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEwF,IAAI,EAAE;EAAe,CAAC,EACnE;IAAE7I,IAAI,EAAE,mBAAmB;IAAE4I,IAAI,eAAEtJ,OAAA,CAAC7B,cAAc;MAAAyF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEwF,IAAI,EAAE;EAAoB,CAAC,EAClF;IAAE7I,IAAI,EAAE,iBAAiB;IAAE4I,IAAI,eAAEtJ,OAAA,CAACX,OAAO;MAAAuE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEwF,IAAI,EAAE;EAAgB,CAAC,EACrE;IAAE7I,IAAI,EAAE,wBAAwB;IAAE4I,IAAI,eAAEtJ,OAAA,CAACT,WAAW;MAAAqE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEwF,IAAI,EAAE;EAAmB,CAAC,CACpF;EAED,MAAMI,mBAAmB,GAAG,CAC1B;IAAEjJ,IAAI,EAAE,sBAAsB;IAAE4I,IAAI,eAAEtJ,OAAA,CAAC/B,YAAY;MAAA2F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEwF,IAAI,EAAE;EAAwB,CAAC,EACvF;IAAE7I,IAAI,EAAE,qBAAqB;IAAE4I,IAAI,eAAEtJ,OAAA,CAAC/B,YAAY;MAAA2F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEwF,IAAI,EAAE;EAAuB,CAAC,CACtF;EAED,MAAMK,kBAAkB,GAAG,CACzB;IAAElJ,IAAI,EAAE,0BAA0B;IAAE4I,IAAI,eAAEtJ,OAAA,CAACnC,aAAa;MAAA+F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEwF,IAAI,EAAE;EAAkB,CAAC,CACvF;EAED,MAAMM,YAAY,GAAG,CACnB;IAAEnJ,IAAI,EAAE,mBAAmB;IAAE4I,IAAI,eAAEtJ,OAAA,CAACnC,aAAa;MAAA+F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEwF,IAAI,EAAE;EAAqB,CAAC,CACnF;EAED,MAAMO,aAAa,GAAG,CACpB;IAAEpJ,IAAI,EAAE,WAAW;IAAE4I,IAAI,eAAEtJ,OAAA,CAACrB,YAAY;MAAAiF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEwF,IAAI,EAAE;EAAa,CAAC,CAClE;EAED,MAAMQ,WAAW,GAAG,CAClB;IAAErJ,IAAI,EAAE,kBAAkB;IAAE4I,IAAI,eAAEtJ,OAAA,CAACnC,aAAa;MAAA+F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEwF,IAAI,EAAE;EAAoB,CAAC,CACjF;EAED,MAAMS,aAAa,GAAG,CACpB;IAAEtJ,IAAI,EAAE,aAAa;IAAE4I,IAAI,eAAEtJ,OAAA,CAAC7B,cAAc;MAAAyF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEwF,IAAI,EAAE;EAAe,CAAC,CACxE;;EAED;;EAEA,MAAMU,gBAAgB,GAAG,CACvB;IAAEvJ,IAAI,EAAE,uBAAuB;IAAE4I,IAAI,eAAEtJ,OAAA,CAACnC,aAAa;MAAA+F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEwF,IAAI,EAAE;EAAyB,CAAC,CAC3F;EAED,MAAMW,cAAc,GAAG,CACrB;IAAExJ,IAAI,EAAE,qBAAqB;IAAE4I,IAAI,eAAEtJ,OAAA,CAACnC,aAAa;MAAA+F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEwF,IAAI,EAAE;EAAuB,CAAC,CACvF;EAED,MAAMY,UAAU,GAAGC,OAAO,CAACnE,QAAQ,CAAC;EACpC,MAAMoE,mBAAmB,GAAGD,OAAO,CAACjE,qBAAqB,CAAC;EAE1D,MAAMmE,UAAU,gBACdtK,OAAA,CAACtD,IAAI;IACHuJ,QAAQ,EAAEA,QAAS;IACnBsE,EAAE,EAAC,cAAc;IACjBC,WAAW;IACX7I,IAAI,EAAEwI,UAAW;IACjBM,OAAO,EAAEzC,eAAgB;IACzB0C,EAAE,EAAE;MACF,kBAAkB,EAAE;QAClBxH,YAAY,EAAE,CAAC;QACfyH,QAAQ,EAAE,GAAG;QACbpK,SAAS,EAAE,+BAA+B;QAC1CqK,EAAE,EAAE;MACN;IACF,CAAE;IACFC,eAAe,EAAE;MAAEC,UAAU,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAM,CAAE;IAC1DC,YAAY,EAAE;MAAEF,UAAU,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAS,CAAE;IAAAtF,QAAA,gBAE1DzF,OAAA,CAACrD,QAAQ;MAACsO,OAAO,EAAEjD,eAAgB;MAAAvC,QAAA,gBACjCzF,OAAA,CAACpD,YAAY;QAAA6I,QAAA,eACXzF,OAAA,CAACzC,UAAU;UAAC+F,QAAQ,EAAC;QAAO;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,WAEjB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAU,CAAC,eACX/D,OAAA,CAACrD,QAAQ;MAACsO,OAAO,EAAEjD,eAAgB;MAAAvC,QAAA,gBACjCzF,OAAA,CAACpD,YAAY;QAAA6I,QAAA,eACXzF,OAAA,CAACrC,YAAY;UAAC2F,QAAQ,EAAC;QAAO;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,YAEjB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAU,CAAC,eACX/D,OAAA,CAACnD,OAAO;MAAA+G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACX/D,OAAA,CAACrD,QAAQ;MAACsO,OAAO,EAAEhD,YAAa;MAAAxC,QAAA,gBAC9BzF,OAAA,CAACpD,YAAY;QAAA6I,QAAA,eACXzF,OAAA,CAACvC,UAAU;UAAC6F,QAAQ,EAAC,OAAO;UAAC9C,KAAK,EAAC;QAAO;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eACf/D,OAAA,CAACxD,UAAU;QAACgE,KAAK,EAAC,OAAO;QAAAiF,QAAA,EAAC;MAAM;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CACP;EAED,MAAMmH,uBAAuB,gBAC3BlL,OAAA,CAACtD,IAAI;IACHuJ,QAAQ,EAAEE,qBAAsB;IAChCoE,EAAE,EAAC,oBAAoB;IACvBC,WAAW;IACX7I,IAAI,EAAE0I,mBAAoB;IAC1BI,OAAO,EAAEzC,eAAgB;IACzB0C,EAAE,EAAE;MACF,kBAAkB,EAAE;QAClBxH,YAAY,EAAE,CAAC;QACfyH,QAAQ,EAAE,GAAG;QACbpK,SAAS,EAAE,+BAA+B;QAC1CqK,EAAE,EAAE;MACN;IACF,CAAE;IACFC,eAAe,EAAE;MAAEC,UAAU,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAM,CAAE;IAC1DC,YAAY,EAAE;MAAEF,UAAU,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAS,CAAE;IAAAtF,QAAA,gBAE1DzF,OAAA,CAAChE,GAAG;MAAC0O,EAAE,EAAE;QAAES,CAAC,EAAE;MAAE,CAAE;MAAA1F,QAAA,eAChBzF,OAAA,CAACxD,UAAU;QAAC4O,OAAO,EAAC,IAAI;QAAC/H,UAAU,EAAE,GAAI;QAAAoC,QAAA,EAAC;MAE1C;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eACN/D,OAAA,CAACnD,OAAO;MAAA+G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACX/D,OAAA,CAACrD,QAAQ;MAACsO,OAAO,EAAEjD,eAAgB;MAAC0C,EAAE,EAAE;QAAEW,EAAE,EAAE;MAAI,CAAE;MAAA5F,QAAA,eAClDzF,OAAA,CAAChE,GAAG;QAAC0O,EAAE,EAAE;UAAE7F,OAAO,EAAE,MAAM;UAAEyG,aAAa,EAAE;QAAS,CAAE;QAAA7F,QAAA,gBACpDzF,OAAA,CAACxD,UAAU;UAAC4O,OAAO,EAAC,WAAW;UAAC/H,UAAU,EAAE,GAAI;UAAAoC,QAAA,EAAC;QAEjD;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb/D,OAAA,CAACxD,UAAU;UAAC4O,OAAO,EAAC,OAAO;UAAC5K,KAAK,EAAC,gBAAgB;UAAAiF,QAAA,EAAC;QAEnD;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eACX/D,OAAA,CAACrD,QAAQ;MAACsO,OAAO,EAAEjD,eAAgB;MAAC0C,EAAE,EAAE;QAAEW,EAAE,EAAE;MAAI,CAAE;MAAA5F,QAAA,eAClDzF,OAAA,CAAChE,GAAG;QAAC0O,EAAE,EAAE;UAAE7F,OAAO,EAAE,MAAM;UAAEyG,aAAa,EAAE;QAAS,CAAE;QAAA7F,QAAA,gBACpDzF,OAAA,CAACxD,UAAU;UAAC4O,OAAO,EAAC,WAAW;UAAC/H,UAAU,EAAE,GAAI;UAAAoC,QAAA,EAAC;QAEjD;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb/D,OAAA,CAACxD,UAAU;UAAC4O,OAAO,EAAC,OAAO;UAAC5K,KAAK,EAAC,gBAAgB;UAAAiF,QAAA,EAAC;QAEnD;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eACX/D,OAAA,CAACnD,OAAO;MAAA+G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACX/D,OAAA,CAAChE,GAAG;MAAC0O,EAAE,EAAE;QAAES,CAAC,EAAE,CAAC;QAAEtG,OAAO,EAAE,MAAM;QAAE5B,cAAc,EAAE;MAAS,CAAE;MAAAwC,QAAA,eAC3DzF,OAAA,CAAChD,MAAM;QACLuO,IAAI,EAAC,OAAO;QACZC,OAAO,eAAExL,OAAA,CAACzB,gBAAgB;UAAAqF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC9B2G,EAAE,EAAE;UAAEtH,aAAa,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAI,CAAE;QAAAoC,QAAA,EAChD;MAED;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CACP;EAED,oBACE/D,OAAA,CAAChE,GAAG;IAAC0O,EAAE,EAAE;MAAE7F,OAAO,EAAE,MAAM;MAAE5D,SAAS,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAS,CAAE;IAAAuE,QAAA,gBACnEzF,OAAA,CAAC9D,WAAW;MAAA0H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGf/D,OAAA,CAACG,YAAY;MAACyB,QAAQ,EAAC,OAAO;MAAA6D,QAAA,eAC5BzF,OAAA,CAAC7D,OAAO;QAAAsJ,QAAA,gBACNzF,OAAA,CAACzD,UAAU;UACTkP,IAAI,EAAC,OAAO;UACZjL,KAAK,EAAC,SAAS;UACf,cAAW,MAAM;UACjByK,OAAO,EAAE7C,aAAc;UACvBsC,EAAE,EAAE;YAAEgB,EAAE,EAAE;UAAE,CAAE;UAAAjG,QAAA,eAEdzF,OAAA,CAAC7C,QAAQ;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEb/D,OAAA,CAAChE,GAAG;UAAC0O,EAAE,EAAE;YAAE7F,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAW,QAAA,GAChDc,gBAAgB,IAAIA,gBAAgB,CAACoF,QAAQ,gBAC5C3L,OAAA,CAAChE,GAAG;YACF4P,SAAS,EAAC,KAAK;YACfC,GAAG,EAAEtF,gBAAgB,CAACoF,QAAS;YAC/BG,GAAG,EAAEvF,gBAAgB,CAACwF,IAAK;YAC3BrB,EAAE,EAAE;cACF3I,MAAM,EAAE,EAAE;cACVqC,QAAQ,EAAE,GAAG;cACb4H,SAAS,EAAE,SAAS;cACpBN,EAAE,EAAE;YACN;UAAE;YAAA9H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAEF/D,OAAA,CAACvD,MAAM;YACLiO,EAAE,EAAE;cACF1I,KAAK,EAAE,EAAE;cACTD,MAAM,EAAE,EAAE;cACV2J,EAAE,EAAE,CAAC;cACLO,OAAO,EAAE7L,KAAK,CAACK,OAAO,CAACE,OAAO,CAACS;YACjC,CAAE;YAAAqE,QAAA,EAEDc,gBAAgB,GAAGA,gBAAgB,CAACwF,IAAI,CAACG,MAAM,CAAC,CAAC,CAAC,GAAG;UAAG;YAAAtI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CACT,eACD/D,OAAA,CAACxD,UAAU;YACT4O,OAAO,EAAC,IAAI;YACZe,MAAM;YACNP,SAAS,EAAC,KAAK;YACflB,EAAE,EAAE;cACF7F,OAAO,EAAE;gBAAEuH,EAAE,EAAE,MAAM;gBAAEC,EAAE,EAAE;cAAQ,CAAC;cACpChJ,UAAU,EAAE,GAAG;cACfpB,UAAU,EAAE,0BAA0B7B,KAAK,CAACK,OAAO,CAACE,OAAO,CAACS,IAAI,KAAKhB,KAAK,CAACK,OAAO,CAACY,SAAS,CAACD,IAAI,GAAG;cACpGkL,oBAAoB,EAAE,MAAM;cAC5BC,mBAAmB,EAAE;YACvB,CAAE;YAAA9G,QAAA,EAEDc,gBAAgB,GAAGA,gBAAgB,CAACwF,IAAI,GAAG;UAAc;YAAAnI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEN/D,OAAA,CAACsE,SAAS;UAAAmB,QAAA,gBACRzF,OAAA,CAACiF,iBAAiB;YAAAQ,QAAA,eAChBzF,OAAA,CAAC3B,UAAU;cAAAuF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eACpB/D,OAAA,CAACoF,eAAe;YACdoH,WAAW,EAAC,cAAS;YACrBC,UAAU,EAAE;cAAE,YAAY,EAAE;YAAS;UAAE;YAAA7I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ/D,OAAA,CAAChE,GAAG;UAAC0O,EAAE,EAAE;YAAE1J,QAAQ,EAAE;UAAE;QAAE;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE5B/D,OAAA,CAAChE,GAAG;UAAC0O,EAAE,EAAE;YAAE7F,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAW,QAAA,gBACjDzF,OAAA,CAACjD,OAAO;YAAC2P,KAAK,EAAC,eAAe;YAAAjH,QAAA,eAC5BzF,OAAA,CAACzD,UAAU;cACTgP,IAAI,EAAC,OAAO;cACZ,cAAW,oBAAoB;cAC/B/K,KAAK,EAAC,SAAS;cACfyK,OAAO,EAAElD,2BAA4B;cACrC2C,EAAE,EAAE;gBACFgB,EAAE,EAAE,CAAC;gBACL,SAAS,EAAE;kBACTrL,eAAe,EAAEhE,KAAK,CAAC+D,KAAK,CAACK,OAAO,CAACE,OAAO,CAACS,IAAI,EAAE,IAAI;gBACzD;cACF,CAAE;cAAAqE,QAAA,eAEFzF,OAAA,CAAClD,KAAK;gBAAC6P,YAAY,EAAE,CAAE;gBAACnM,KAAK,EAAC,OAAO;gBAAAiF,QAAA,eACnCzF,OAAA,CAAC3C,iBAAiB;kBAAAuG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEV/D,OAAA,CAACjD,OAAO;YAAC2P,KAAK,EAAC,SAAS;YAAAjH,QAAA,eACtBzF,OAAA,CAACzD,UAAU;cACTgP,IAAI,EAAC,OAAO;cACZE,IAAI,EAAC,KAAK;cACV,cAAW,yBAAyB;cACpC,iBAAc,MAAM;cACpBR,OAAO,EAAErD,qBAAsB;cAC/BpH,KAAK,EAAC,SAAS;cACfkK,EAAE,EAAE;gBACF,SAAS,EAAE;kBACTrK,eAAe,EAAEhE,KAAK,CAAC+D,KAAK,CAACK,OAAO,CAACE,OAAO,CAACS,IAAI,EAAE,IAAI;gBACzD;cACF,CAAE;cAAAqE,QAAA,eAEFzF,OAAA,CAACvD,MAAM;gBACLiO,EAAE,EAAE;kBACF1I,KAAK,EAAE,EAAE;kBACTD,MAAM,EAAE,EAAE;kBACVkK,OAAO,EAAE5P,KAAK,CAAC+D,KAAK,CAACK,OAAO,CAACE,OAAO,CAACS,IAAI,EAAE,GAAG,CAAC;kBAC/CZ,KAAK,EAAEJ,KAAK,CAACK,OAAO,CAACE,OAAO,CAACS;gBAC/B,CAAE;gBAAAqE,QAAA,eAEFzF,OAAA,CAACzC,UAAU;kBAAC+F,QAAQ,EAAC;gBAAO;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGf/D,OAAA,CAAC0B,OAAO;MAACC,IAAI,EAAEoE,WAAY;MAAAN,QAAA,eACzBzF,OAAA,CAAChE,GAAG;QAAC0O,EAAE,EAAE;UAAES,CAAC,EAAE,CAAC;UAAEyB,EAAE,EAAE,CAAC;UAAEC,SAAS,EAAE,MAAM;UAAE9K,MAAM,EAAE;QAAO,CAAE;QAAA0D,QAAA,GAEzDgB,cAAc,CAACE,SAAS,iBACvB3G,OAAA,CAAAE,SAAA;UAAAuF,QAAA,gBACEzF,OAAA,CAACxD,UAAU;YAAC4O,OAAO,EAAC,WAAW;YAAC/H,UAAU,EAAE,GAAI;YAACqH,EAAE,EAAE;cAAEoC,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE,CAAC;cAAEvM,KAAK,EAAE,gBAAgB;cAAE8C,QAAQ,EAAE,SAAS;cAAE0J,aAAa,EAAE;YAAQ,CAAE;YAAAvH,QAAA,EAAC;UAE7I;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZsF,cAAc,CAAC4D,GAAG,CAAEC,IAAI,iBACvBlN,OAAA,CAACyD,OAAO;YAEN0J,SAAS,EAAED,IAAI,CAAC5D,IAAK;YACrB5F,MAAM,EAAEkC,QAAQ,CAACwH,QAAQ,KAAKF,IAAI,CAAC3D,IAAK;YACxC0B,OAAO,EAAEA,CAAA,KAAMtF,QAAQ,CAACuH,IAAI,CAAC3D,IAAI,CAAE;YAAA9D,QAAA,EAElCyH,IAAI,CAACxM;UAAI,GALLwM,IAAI,CAACxM,IAAI;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMP,CACV,CAAC;QAAA,eACF,CACH,EAGA0C,cAAc,CAACkB,WAAW,iBACzB3H,OAAA,CAAAE,SAAA;UAAAuF,QAAA,gBACEzF,OAAA,CAACxD,UAAU;YAAC4O,OAAO,EAAC,WAAW;YAAC/H,UAAU,EAAE,GAAI;YAACqH,EAAE,EAAE;cAAEE,EAAE,EAAE,CAAC;cAAEkC,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE,CAAC;cAAEvM,KAAK,EAAE,gBAAgB;cAAE8C,QAAQ,EAAE,SAAS;cAAE0J,aAAa,EAAE;YAAQ,CAAE;YAAAvH,QAAA,EAAC;UAEpJ;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZ0F,gBAAgB,CAACwD,GAAG,CAAEC,IAAI,iBACzBlN,OAAA,CAACyD,OAAO;YAEN0J,SAAS,EAAED,IAAI,CAAC5D,IAAK;YACrB5F,MAAM,EAAEkC,QAAQ,CAACwH,QAAQ,CAACC,UAAU,CAACH,IAAI,CAAC3D,IAAI,CAAE;YAChD0B,OAAO,EAAEA,CAAA,KAAMtF,QAAQ,CAACuH,IAAI,CAAC3D,IAAI,CAAE;YAAA9D,QAAA,EAElCyH,IAAI,CAACxM;UAAI,GALLwM,IAAI,CAACxM,IAAI;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMP,CACV,CAAC;QAAA,eACF,CACH,EAGA0C,cAAc,CAACiB,SAAS,iBACvB1H,OAAA,CAAAE,SAAA;UAAAuF,QAAA,gBACEzF,OAAA,CAACxD,UAAU;YAAC4O,OAAO,EAAC,WAAW;YAAC/H,UAAU,EAAE,GAAI;YAACqH,EAAE,EAAE;cAAEE,EAAE,EAAE,CAAC;cAAEkC,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE,CAAC;cAAEvM,KAAK,EAAE,gBAAgB;cAAE8C,QAAQ,EAAE,SAAS;cAAE0J,aAAa,EAAE;YAAQ,CAAE;YAAAvH,QAAA,EAAC;UAEpJ;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZ2F,cAAc,CAACuD,GAAG,CAAEC,IAAI,iBACvBlN,OAAA,CAACyD,OAAO;YAEN0J,SAAS,EAAED,IAAI,CAAC5D,IAAK;YACrB5F,MAAM,EAAEkC,QAAQ,CAACwH,QAAQ,KAAKF,IAAI,CAAC3D,IAAK;YACxC0B,OAAO,EAAEA,CAAA,KAAMtF,QAAQ,CAACuH,IAAI,CAAC3D,IAAI,CAAE;YAAA9D,QAAA,EAElCyH,IAAI,CAACxM;UAAI,GALLwM,IAAI,CAACxM,IAAI;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMP,CACV,CAAC;QAAA,eACF,CACH,EAGA0C,cAAc,CAACG,YAAY,iBAC1B5G,OAAA,CAAAE,SAAA;UAAAuF,QAAA,gBACEzF,OAAA,CAACxD,UAAU;YAAC4O,OAAO,EAAC,WAAW;YAAC/H,UAAU,EAAE,GAAI;YAACqH,EAAE,EAAE;cAAEE,EAAE,EAAE,CAAC;cAAEkC,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE,CAAC;cAAEvM,KAAK,EAAE,gBAAgB;cAAE8C,QAAQ,EAAE,SAAS;cAAE0J,aAAa,EAAE;YAAQ,CAAE;YAAAvH,QAAA,EAAC;UAEpJ;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZyF,iBAAiB,CAACyD,GAAG,CAAEC,IAAI,iBAC1BlN,OAAA,CAACyD,OAAO;YAEN0J,SAAS,EAAED,IAAI,CAAC5D,IAAK;YACrB5F,MAAM,EAAEkC,QAAQ,CAACwH,QAAQ,KAAKF,IAAI,CAAC3D,IAAK;YACxC0B,OAAO,EAAEA,CAAA,KAAMtF,QAAQ,CAACuH,IAAI,CAAC3D,IAAI,CAAE;YAAA9D,QAAA,EAElCyH,IAAI,CAACxM;UAAI,GALLwM,IAAI,CAACxM,IAAI;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMP,CACV,CAAC;QAAA,eACF,CACH,EAGA0C,cAAc,CAACI,cAAc,iBAC5B7G,OAAA,CAAAE,SAAA;UAAAuF,QAAA,gBACEzF,OAAA,CAACxD,UAAU;YAAC4O,OAAO,EAAC,WAAW;YAAC/H,UAAU,EAAE,GAAI;YAACqH,EAAE,EAAE;cAAEE,EAAE,EAAE,CAAC;cAAEkC,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE,CAAC;cAAEvM,KAAK,EAAE,gBAAgB;cAAE8C,QAAQ,EAAE,SAAS;cAAE0J,aAAa,EAAE;YAAQ,CAAE;YAAAvH,QAAA,EAAC;UAEpJ;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZ4F,mBAAmB,CAACsD,GAAG,CAAEC,IAAI,iBAC5BlN,OAAA,CAACyD,OAAO;YAEN0J,SAAS,EAAED,IAAI,CAAC5D,IAAK;YACrB5F,MAAM,EAAEkC,QAAQ,CAACwH,QAAQ,KAAKF,IAAI,CAAC3D,IAAK;YACxC0B,OAAO,EAAEA,CAAA,KAAMtF,QAAQ,CAACuH,IAAI,CAAC3D,IAAI,CAAE;YAAA9D,QAAA,EAElCyH,IAAI,CAACxM;UAAI,GALLwM,IAAI,CAACxM,IAAI;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMP,CACV,CAAC;QAAA,eACF,CACH,EAGA0C,cAAc,CAACK,aAAa,iBAC3B9G,OAAA,CAAAE,SAAA;UAAAuF,QAAA,gBACEzF,OAAA,CAACxD,UAAU;YAAC4O,OAAO,EAAC,WAAW;YAAC/H,UAAU,EAAE,GAAI;YAACqH,EAAE,EAAE;cAAEE,EAAE,EAAE,CAAC;cAAEkC,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE,CAAC;cAAEvM,KAAK,EAAE,gBAAgB;cAAE8C,QAAQ,EAAE,SAAS;cAAE0J,aAAa,EAAE;YAAQ,CAAE;YAAAvH,QAAA,EAAC;UAEpJ;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZ6F,kBAAkB,CAACqD,GAAG,CAAEC,IAAI,iBAC3BlN,OAAA,CAACyD,OAAO;YAEN0J,SAAS,EAAED,IAAI,CAAC5D,IAAK;YACrB5F,MAAM,EAAEkC,QAAQ,CAACwH,QAAQ,KAAKF,IAAI,CAAC3D,IAAK;YACxC0B,OAAO,EAAEA,CAAA,KAAMtF,QAAQ,CAACuH,IAAI,CAAC3D,IAAI,CAAE;YAAA9D,QAAA,EAElCyH,IAAI,CAACxM;UAAI,GALLwM,IAAI,CAACxM,IAAI;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMP,CACV,CAAC;QAAA,eACF,CACH,EAGA0C,cAAc,CAACM,OAAO,iBACrB/G,OAAA,CAAAE,SAAA;UAAAuF,QAAA,gBACEzF,OAAA,CAACxD,UAAU;YAAC4O,OAAO,EAAC,WAAW;YAAC/H,UAAU,EAAE,GAAI;YAACqH,EAAE,EAAE;cAAEE,EAAE,EAAE,CAAC;cAAEkC,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE,CAAC;cAAEvM,KAAK,EAAE,gBAAgB;cAAE8C,QAAQ,EAAE,SAAS;cAAE0J,aAAa,EAAE;YAAQ,CAAE;YAAAvH,QAAA,EAAC;UAEpJ;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZ8F,YAAY,CAACoD,GAAG,CAAEC,IAAI,iBACrBlN,OAAA,CAACyD,OAAO;YAEN0J,SAAS,EAAED,IAAI,CAAC5D,IAAK;YACrB5F,MAAM,EAAEkC,QAAQ,CAACwH,QAAQ,KAAKF,IAAI,CAAC3D,IAAK;YACxC0B,OAAO,EAAEA,CAAA,KAAMtF,QAAQ,CAACuH,IAAI,CAAC3D,IAAI,CAAE;YAAA9D,QAAA,EAElCyH,IAAI,CAACxM;UAAI,GALLwM,IAAI,CAACxM,IAAI;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMP,CACV,CAAC;QAAA,eACF,CACH,EAGA0C,cAAc,CAACO,QAAQ,iBACtBhH,OAAA,CAAAE,SAAA;UAAAuF,QAAA,gBACEzF,OAAA,CAACxD,UAAU;YAAC4O,OAAO,EAAC,WAAW;YAAC/H,UAAU,EAAE,GAAI;YAACqH,EAAE,EAAE;cAAEE,EAAE,EAAE,CAAC;cAAEkC,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE,CAAC;cAAEvM,KAAK,EAAE,gBAAgB;cAAE8C,QAAQ,EAAE,SAAS;cAAE0J,aAAa,EAAE;YAAQ,CAAE;YAAAvH,QAAA,EAAC;UAEpJ;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZ+F,aAAa,CAACmD,GAAG,CAAEC,IAAI,iBACtBlN,OAAA,CAACyD,OAAO;YAEN0J,SAAS,EAAED,IAAI,CAAC5D,IAAK;YACrB5F,MAAM,EAAEkC,QAAQ,CAACwH,QAAQ,KAAKF,IAAI,CAAC3D,IAAK;YACxC0B,OAAO,EAAEA,CAAA,KAAMtF,QAAQ,CAACuH,IAAI,CAAC3D,IAAI,CAAE;YAAA9D,QAAA,EAElCyH,IAAI,CAACxM;UAAI,GALLwM,IAAI,CAACxM,IAAI;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMP,CACV,CAAC;QAAA,eACF,CACH,EAGA0C,cAAc,CAACQ,MAAM,iBACpBjH,OAAA,CAAAE,SAAA;UAAAuF,QAAA,gBACEzF,OAAA,CAACxD,UAAU;YAAC4O,OAAO,EAAC,WAAW;YAAC/H,UAAU,EAAE,GAAI;YAACqH,EAAE,EAAE;cAAEE,EAAE,EAAE,CAAC;cAAEkC,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE,CAAC;cAAEvM,KAAK,EAAE,gBAAgB;cAAE8C,QAAQ,EAAE,SAAS;cAAE0J,aAAa,EAAE;YAAQ,CAAE;YAAAvH,QAAA,EAAC;UAEpJ;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZgG,WAAW,CAACkD,GAAG,CAAEC,IAAI,iBACpBlN,OAAA,CAACyD,OAAO;YAEN0J,SAAS,EAAED,IAAI,CAAC5D,IAAK;YACrB5F,MAAM,EAAEkC,QAAQ,CAACwH,QAAQ,KAAKF,IAAI,CAAC3D,IAAK;YACxC0B,OAAO,EAAEA,CAAA,KAAMtF,QAAQ,CAACuH,IAAI,CAAC3D,IAAI,CAAE;YAAA9D,QAAA,EAElCyH,IAAI,CAACxM;UAAI,GALLwM,IAAI,CAACxM,IAAI;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMP,CACV,CAAC;QAAA,eACF,CACH,EAGA0C,cAAc,CAACS,SAAS,iBACvBlH,OAAA,CAAAE,SAAA;UAAAuF,QAAA,gBACEzF,OAAA,CAACxD,UAAU;YAAC4O,OAAO,EAAC,WAAW;YAAC/H,UAAU,EAAE,GAAI;YAACqH,EAAE,EAAE;cAAEE,EAAE,EAAE,CAAC;cAAEkC,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE,CAAC;cAAEvM,KAAK,EAAE,gBAAgB;cAAE8C,QAAQ,EAAE,SAAS;cAAE0J,aAAa,EAAE;YAAQ,CAAE;YAAAvH,QAAA,EAAC;UAEpJ;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZmG,cAAc,CAAC+C,GAAG,CAAEC,IAAI,IAAK;YAC5B;YACA,IAAKA,IAAI,CAACxM,IAAI,KAAK,cAAc,IAAI,CAAC+F,cAAc,CAACU,WAAW,IAC3D+F,IAAI,CAACxM,IAAI,KAAK,SAAS,IAAI,CAAC+F,cAAc,CAACW,OAAQ,IACnD8F,IAAI,CAACxM,IAAI,KAAK,OAAO,IAAI,CAAC+F,cAAc,CAACY,KAAM,EAAE;cACpD,OAAO,IAAI;YACb;YACA,oBACErH,OAAA,CAACyD,OAAO;cAEN0J,SAAS,EAAED,IAAI,CAAC5D,IAAK;cACrB5F,MAAM,EAAEkC,QAAQ,CAACwH,QAAQ,KAAKF,IAAI,CAAC3D,IAAK;cACxC0B,OAAO,EAAEA,CAAA,KAAMtF,QAAQ,CAACuH,IAAI,CAAC3D,IAAI,CAAE;cAAA9D,QAAA,EAElCyH,IAAI,CAACxM;YAAI,GALLwM,IAAI,CAACxM,IAAI;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAMP,CAAC;UAEd,CAAC,CAAC;QAAA,eACF,CACH,EAGA0C,cAAc,CAACa,UAAU,iBACxBtH,OAAA,CAAAE,SAAA;UAAAuF,QAAA,gBACEzF,OAAA,CAACxD,UAAU;YAAC4O,OAAO,EAAC,WAAW;YAAC/H,UAAU,EAAE,GAAI;YAACqH,EAAE,EAAE;cAAEE,EAAE,EAAE,CAAC;cAAEkC,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE,CAAC;cAAEvM,KAAK,EAAE,gBAAgB;cAAE8C,QAAQ,EAAE,SAAS;cAAE0J,aAAa,EAAE;YAAQ,CAAE;YAAAvH,QAAA,EAAC;UAEpJ;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZiG,aAAa,CAACiD,GAAG,CAAEC,IAAI,iBACtBlN,OAAA,CAACyD,OAAO;YAEN0J,SAAS,EAAED,IAAI,CAAC5D,IAAK;YACrB5F,MAAM,EAAEkC,QAAQ,CAACwH,QAAQ,KAAKF,IAAI,CAAC3D,IAAK;YACxC0B,OAAO,EAAEA,CAAA,KAAMtF,QAAQ,CAACuH,IAAI,CAAC3D,IAAI,CAAE;YAAA9D,QAAA,EAElCyH,IAAI,CAACxM;UAAI,GALLwM,IAAI,CAACxM,IAAI;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMP,CACV,CAAC;QAAA,eACF,CACH,EAGA0C,cAAc,CAACc,WAAW,iBACzBvH,OAAA,CAAAE,SAAA;UAAAuF,QAAA,gBACEzF,OAAA,CAACxD,UAAU;YAAC4O,OAAO,EAAC,WAAW;YAAC/H,UAAU,EAAE,GAAI;YAACqH,EAAE,EAAE;cAAEE,EAAE,EAAE,CAAC;cAAEkC,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE,CAAC;cAAEvM,KAAK,EAAE,gBAAgB;cAAE8C,QAAQ,EAAE,SAAS;cAAE0J,aAAa,EAAE;YAAQ,CAAE;YAAAvH,QAAA,EAAC;UAEpJ;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZkG,gBAAgB,CAACgD,GAAG,CAAEC,IAAI,IAAK;YAC9B;YACA,IAAIA,IAAI,CAACxM,IAAI,KAAK,sBAAsB,IAAI,CAAC+F,cAAc,CAACe,mBAAmB,EAAE;cAC/E,OAAO,IAAI;YACb;YACA,oBACExH,OAAA,CAACyD,OAAO;cAEN0J,SAAS,EAAED,IAAI,CAAC5D,IAAK;cACrB5F,MAAM,EAAEkC,QAAQ,CAACwH,QAAQ,KAAKF,IAAI,CAAC3D,IAAK;cACxC0B,OAAO,EAAEA,CAAA,KAAMtF,QAAQ,CAACuH,IAAI,CAAC3D,IAAI,CAAE;cAAA9D,QAAA,EAElCyH,IAAI,CAACxM;YAAI,GALLwM,IAAI,CAACxM,IAAI;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAMP,CAAC;UAEd,CAAC,CAAC;QAAA,eACF,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV/D,OAAA,CAACe,WAAW;MAAA0E,QAAA,gBACVzF,OAAA,CAAC7D,OAAO;QAAAyH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,KAAC,eACZ/D,OAAA,CAACiE,gBAAgB;QAACC,WAAW,EAAE6B,WAAW,CAACuH,QAAQ,CAAC,CAAE;QAAA7H,QAAA,EACnDA;MAAQ;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,EAEbuG,UAAU,EACVY,uBAAuB;EAAA;IAAAtH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACrB,CAAC;AAEV,CAAC;AAAC2B,EAAA,CAjoBIF,MAAM;EAAA,QACIvJ,QAAQ,EACLyD,WAAW,EACXC,WAAW,EACX1C,aAAa;AAAA;AAAAsQ,IAAA,GAJ1B/H,MAAM;AAmoBZ,eAAeA,MAAM;AAAC,IAAA1E,EAAA,EAAAW,GAAA,EAAAmB,GAAA,EAAAY,GAAA,EAAAQ,GAAA,EAAAK,GAAA,EAAAW,GAAA,EAAAG,GAAA,EAAAI,GAAA,EAAAgI,IAAA;AAAAC,YAAA,CAAA1M,EAAA;AAAA0M,YAAA,CAAA/L,GAAA;AAAA+L,YAAA,CAAA5K,GAAA;AAAA4K,YAAA,CAAAhK,GAAA;AAAAgK,YAAA,CAAAxJ,GAAA;AAAAwJ,YAAA,CAAAnJ,GAAA;AAAAmJ,YAAA,CAAAxI,GAAA;AAAAwI,YAAA,CAAArI,GAAA;AAAAqI,YAAA,CAAAjI,GAAA;AAAAiI,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}