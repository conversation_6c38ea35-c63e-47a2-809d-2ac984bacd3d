{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\project ai\\\\Store Management System\\\\asset management\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.js\";\nimport React from 'react';\nimport { Box, Typography, Button, Paper, Alert } from '@mui/material';\nimport { Refresh as RefreshIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass ErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.handleReload = () => {\n      window.location.reload();\n    };\n    this.handleReset = () => {\n      this.setState({\n        hasError: false,\n        error: null,\n        errorInfo: null\n      });\n    };\n    this.state = {\n      hasError: false,\n      error: null,\n      errorInfo: null\n    };\n  }\n  static getDerivedStateFromError(error) {\n    // Update state so the next render will show the fallback UI\n    return {\n      hasError: true\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    var _error$message, _error$message2, _error$stack, _error$stack2;\n    // Log error details\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n\n    // Filter out browser extension errors\n    const isBrowserExtensionError = ((_error$message = error.message) === null || _error$message === void 0 ? void 0 : _error$message.includes('message channel closed')) || ((_error$message2 = error.message) === null || _error$message2 === void 0 ? void 0 : _error$message2.includes('Extension context invalidated')) || ((_error$stack = error.stack) === null || _error$stack === void 0 ? void 0 : _error$stack.includes('chrome-extension://')) || ((_error$stack2 = error.stack) === null || _error$stack2 === void 0 ? void 0 : _error$stack2.includes('moz-extension://'));\n    if (isBrowserExtensionError) {\n      // Don't show error UI for browser extension errors\n      console.warn('Browser extension error detected, ignoring:', error.message);\n      this.setState({\n        hasError: false\n      });\n      return;\n    }\n    this.setState({\n      error: error,\n      errorInfo: errorInfo\n    });\n  }\n  render() {\n    if (this.state.hasError) {\n      // Fallback UI\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          minHeight: '50vh',\n          p: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 4,\n            maxWidth: 600,\n            textAlign: 'center',\n            borderRadius: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n            sx: {\n              fontSize: 64,\n              color: 'warning.main',\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            gutterBottom: true,\n            children: \"Something went wrong\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            color: \"text.secondary\",\n            sx: {\n              mb: 3\n            },\n            children: \"We encountered an unexpected error. Please try refreshing the page or contact support if the problem persists.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this), process.env.NODE_ENV === 'development' && this.state.error && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"error\",\n            sx: {\n              mb: 3,\n              textAlign: 'left'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              gutterBottom: true,\n              children: \"Error Details:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              component: \"pre\",\n              sx: {\n                fontSize: '0.75rem'\n              },\n              children: this.state.error.toString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              gap: 2,\n              justifyContent: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 28\n              }, this),\n              onClick: this.handleReload,\n              children: \"Reload Page\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              onClick: this.handleReset,\n              children: \"Try Again\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this);\n    }\n    return this.props.children;\n  }\n}\nexport default ErrorBoundary;", "map": {"version": 3, "names": ["React", "Box", "Typography", "<PERSON><PERSON>", "Paper", "<PERSON><PERSON>", "Refresh", "RefreshIcon", "Warning", "WarningIcon", "jsxDEV", "_jsxDEV", "Error<PERSON>ou<PERSON><PERSON>", "Component", "constructor", "props", "handleReload", "window", "location", "reload", "handleReset", "setState", "<PERSON><PERSON><PERSON><PERSON>", "error", "errorInfo", "state", "getDerivedStateFromError", "componentDidCatch", "_error$message", "_error$message2", "_error$stack", "_error$stack2", "console", "isBrowserExtensionError", "message", "includes", "stack", "warn", "render", "sx", "display", "justifyContent", "alignItems", "minHeight", "p", "children", "max<PERSON><PERSON><PERSON>", "textAlign", "borderRadius", "fontSize", "color", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "gutterBottom", "process", "env", "NODE_ENV", "severity", "component", "toString", "gap", "startIcon", "onClick"], "sources": ["C:/Users/<USER>/Desktop/project ai/Store Management System/asset management/frontend/src/components/ErrorBoundary.js"], "sourcesContent": ["import React from 'react';\nimport { Box, Typography, Button, Paper, Alert } from '@mui/material';\nimport { Refresh as RefreshIcon, Warning as WarningIcon } from '@mui/icons-material';\n\nclass ErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = { hasError: false, error: null, errorInfo: null };\n  }\n\n  static getDerivedStateFromError(error) {\n    // Update state so the next render will show the fallback UI\n    return { hasError: true };\n  }\n\n  componentDidCatch(error, errorInfo) {\n    // Log error details\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n    \n    // Filter out browser extension errors\n    const isBrowserExtensionError = \n      error.message?.includes('message channel closed') ||\n      error.message?.includes('Extension context invalidated') ||\n      error.stack?.includes('chrome-extension://') ||\n      error.stack?.includes('moz-extension://');\n\n    if (isBrowserExtensionError) {\n      // Don't show error UI for browser extension errors\n      console.warn('Browser extension error detected, ignoring:', error.message);\n      this.setState({ hasError: false });\n      return;\n    }\n\n    this.setState({\n      error: error,\n      errorInfo: errorInfo\n    });\n  }\n\n  handleReload = () => {\n    window.location.reload();\n  };\n\n  handleReset = () => {\n    this.setState({ hasError: false, error: null, errorInfo: null });\n  };\n\n  render() {\n    if (this.state.hasError) {\n      // Fallback UI\n      return (\n        <Box\n          sx={{\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            minHeight: '50vh',\n            p: 3,\n          }}\n        >\n          <Paper\n            sx={{\n              p: 4,\n              maxWidth: 600,\n              textAlign: 'center',\n              borderRadius: 2,\n            }}\n          >\n            <WarningIcon\n              sx={{\n                fontSize: 64,\n                color: 'warning.main',\n                mb: 2,\n              }}\n            />\n            <Typography variant=\"h5\" gutterBottom>\n              Something went wrong\n            </Typography>\n            <Typography variant=\"body1\" color=\"text.secondary\" sx={{ mb: 3 }}>\n              We encountered an unexpected error. Please try refreshing the page or contact support if the problem persists.\n            </Typography>\n            \n            {process.env.NODE_ENV === 'development' && this.state.error && (\n              <Alert severity=\"error\" sx={{ mb: 3, textAlign: 'left' }}>\n                <Typography variant=\"subtitle2\" gutterBottom>\n                  Error Details:\n                </Typography>\n                <Typography variant=\"body2\" component=\"pre\" sx={{ fontSize: '0.75rem' }}>\n                  {this.state.error.toString()}\n                </Typography>\n              </Alert>\n            )}\n\n            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>\n              <Button\n                variant=\"contained\"\n                startIcon={<RefreshIcon />}\n                onClick={this.handleReload}\n              >\n                Reload Page\n              </Button>\n              <Button\n                variant=\"outlined\"\n                onClick={this.handleReset}\n              >\n                Try Again\n              </Button>\n            </Box>\n          </Paper>\n        </Box>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nexport default ErrorBoundary;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,EAAEC,UAAU,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,QAAQ,eAAe;AACrE,SAASC,OAAO,IAAIC,WAAW,EAAEC,OAAO,IAAIC,WAAW,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErF,MAAMC,aAAa,SAASZ,KAAK,CAACa,SAAS,CAAC;EAC1CC,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IAAC,KAiCfC,YAAY,GAAG,MAAM;MACnBC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;IAC1B,CAAC;IAAA,KAEDC,WAAW,GAAG,MAAM;MAClB,IAAI,CAACC,QAAQ,CAAC;QAAEC,QAAQ,EAAE,KAAK;QAAEC,KAAK,EAAE,IAAI;QAAEC,SAAS,EAAE;MAAK,CAAC,CAAC;IAClE,CAAC;IAtCC,IAAI,CAACC,KAAK,GAAG;MAAEH,QAAQ,EAAE,KAAK;MAAEC,KAAK,EAAE,IAAI;MAAEC,SAAS,EAAE;IAAK,CAAC;EAChE;EAEA,OAAOE,wBAAwBA,CAACH,KAAK,EAAE;IACrC;IACA,OAAO;MAAED,QAAQ,EAAE;IAAK,CAAC;EAC3B;EAEAK,iBAAiBA,CAACJ,KAAK,EAAEC,SAAS,EAAE;IAAA,IAAAI,cAAA,EAAAC,eAAA,EAAAC,YAAA,EAAAC,aAAA;IAClC;IACAC,OAAO,CAACT,KAAK,CAAC,gCAAgC,EAAEA,KAAK,EAAEC,SAAS,CAAC;;IAEjE;IACA,MAAMS,uBAAuB,GAC3B,EAAAL,cAAA,GAAAL,KAAK,CAACW,OAAO,cAAAN,cAAA,uBAAbA,cAAA,CAAeO,QAAQ,CAAC,wBAAwB,CAAC,OAAAN,eAAA,GACjDN,KAAK,CAACW,OAAO,cAAAL,eAAA,uBAAbA,eAAA,CAAeM,QAAQ,CAAC,+BAA+B,CAAC,OAAAL,YAAA,GACxDP,KAAK,CAACa,KAAK,cAAAN,YAAA,uBAAXA,YAAA,CAAaK,QAAQ,CAAC,qBAAqB,CAAC,OAAAJ,aAAA,GAC5CR,KAAK,CAACa,KAAK,cAAAL,aAAA,uBAAXA,aAAA,CAAaI,QAAQ,CAAC,kBAAkB,CAAC;IAE3C,IAAIF,uBAAuB,EAAE;MAC3B;MACAD,OAAO,CAACK,IAAI,CAAC,6CAA6C,EAAEd,KAAK,CAACW,OAAO,CAAC;MAC1E,IAAI,CAACb,QAAQ,CAAC;QAAEC,QAAQ,EAAE;MAAM,CAAC,CAAC;MAClC;IACF;IAEA,IAAI,CAACD,QAAQ,CAAC;MACZE,KAAK,EAAEA,KAAK;MACZC,SAAS,EAAEA;IACb,CAAC,CAAC;EACJ;EAUAc,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACb,KAAK,CAACH,QAAQ,EAAE;MACvB;MACA,oBACEX,OAAA,CAACV,GAAG;QACFsC,EAAE,EAAE;UACFC,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,QAAQ;UACxBC,UAAU,EAAE,QAAQ;UACpBC,SAAS,EAAE,MAAM;UACjBC,CAAC,EAAE;QACL,CAAE;QAAAC,QAAA,eAEFlC,OAAA,CAACP,KAAK;UACJmC,EAAE,EAAE;YACFK,CAAC,EAAE,CAAC;YACJE,QAAQ,EAAE,GAAG;YACbC,SAAS,EAAE,QAAQ;YACnBC,YAAY,EAAE;UAChB,CAAE;UAAAH,QAAA,gBAEFlC,OAAA,CAACF,WAAW;YACV8B,EAAE,EAAE;cACFU,QAAQ,EAAE,EAAE;cACZC,KAAK,EAAE,cAAc;cACrBC,EAAE,EAAE;YACN;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACF5C,OAAA,CAACT,UAAU;YAACsD,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAZ,QAAA,EAAC;UAEtC;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb5C,OAAA,CAACT,UAAU;YAACsD,OAAO,EAAC,OAAO;YAACN,KAAK,EAAC,gBAAgB;YAACX,EAAE,EAAE;cAAEY,EAAE,EAAE;YAAE,CAAE;YAAAN,QAAA,EAAC;UAElE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EAEZG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAI,IAAI,CAACnC,KAAK,CAACF,KAAK,iBACzDZ,OAAA,CAACN,KAAK;YAACwD,QAAQ,EAAC,OAAO;YAACtB,EAAE,EAAE;cAAEY,EAAE,EAAE,CAAC;cAAEJ,SAAS,EAAE;YAAO,CAAE;YAAAF,QAAA,gBACvDlC,OAAA,CAACT,UAAU;cAACsD,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAZ,QAAA,EAAC;YAE7C;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb5C,OAAA,CAACT,UAAU;cAACsD,OAAO,EAAC,OAAO;cAACM,SAAS,EAAC,KAAK;cAACvB,EAAE,EAAE;gBAAEU,QAAQ,EAAE;cAAU,CAAE;cAAAJ,QAAA,EACrE,IAAI,CAACpB,KAAK,CAACF,KAAK,CAACwC,QAAQ,CAAC;YAAC;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CACR,eAED5C,OAAA,CAACV,GAAG;YAACsC,EAAE,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEwB,GAAG,EAAE,CAAC;cAAEvB,cAAc,EAAE;YAAS,CAAE;YAAAI,QAAA,gBAC7DlC,OAAA,CAACR,MAAM;cACLqD,OAAO,EAAC,WAAW;cACnBS,SAAS,eAAEtD,OAAA,CAACJ,WAAW;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BW,OAAO,EAAE,IAAI,CAAClD,YAAa;cAAA6B,QAAA,EAC5B;YAED;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT5C,OAAA,CAACR,MAAM;cACLqD,OAAO,EAAC,UAAU;cAClBU,OAAO,EAAE,IAAI,CAAC9C,WAAY;cAAAyB,QAAA,EAC3B;YAED;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAEV;IAEA,OAAO,IAAI,CAACxC,KAAK,CAAC8B,QAAQ;EAC5B;AACF;AAEA,eAAejC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}