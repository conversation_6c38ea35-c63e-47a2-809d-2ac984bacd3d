{"ast": null, "code": "import api from '../utils/axios';\nexport const authService = {\n  login: async credentials => {\n    try {\n      const response = await api.post('/auth/token/', credentials);\n      const {\n        token,\n        user\n      } = response.data;\n      if (token) {\n        localStorage.setItem('token', token);\n        localStorage.setItem('user', JSON.stringify(user || {}));\n      }\n      return response.data;\n    } catch (error) {\n      var _error$response;\n      console.error('Login error:', error);\n      throw ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data) || error.message;\n    }\n  },\n  register: async userData => {\n    try {\n      // Try multiple endpoints\n      const endpoints = ['/auth/register/', '/api/auth/register/', '/api/v1/auth/register/', '/register/', '/api/users/'];\n\n      // Set the correct content type for FormData\n      const config = {\n        headers: {\n          'Content-Type': userData instanceof FormData ? 'multipart/form-data' : 'application/json'\n        }\n      };\n      for (const endpoint of endpoints) {\n        try {\n          console.log(`Trying to register at ${endpoint}`);\n          const response = await api.post(endpoint, userData, config);\n          return response.data;\n        } catch (error) {\n          console.warn(`Error with ${endpoint} endpoint:`, error.message);\n          // Continue to the next endpoint\n        }\n      }\n\n      // If we get here, all endpoints failed\n      throw new Error('All registration endpoints failed');\n    } catch (error) {\n      var _error$response2;\n      throw ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data) || error.message;\n    }\n  },\n  logout: () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n  },\n  getCurrentUser: async () => {\n    try {\n      const response = await api.get('/auth/user/');\n      return response.data;\n    } catch (error) {\n      var _error$response3;\n      throw ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data) || error.message;\n    }\n  },\n  updateProfile: async userData => {\n    try {\n      // Try multiple endpoints\n      const endpoints = ['/auth/user/', '/api/auth/user/', '/api/v1/auth/user/', '/api/users/me/', '/api/v1/users/me/'];\n\n      // Set the correct content type for FormData\n      const config = {\n        headers: {\n          'Content-Type': userData instanceof FormData ? 'multipart/form-data' : 'application/json'\n        }\n      };\n      for (const endpoint of endpoints) {\n        try {\n          console.log(`Trying to update user profile at ${endpoint}`);\n          const response = await api.patch(endpoint, userData, config);\n          return response.data;\n        } catch (error) {\n          console.warn(`Error with ${endpoint} endpoint:`, error.message);\n          // Continue to the next endpoint\n        }\n      }\n\n      // If we get here, all endpoints failed\n      throw new Error('All profile update endpoints failed');\n    } catch (error) {\n      var _error$response4;\n      throw ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : _error$response4.data) || error.message;\n    }\n  }\n};", "map": {"version": 3, "names": ["api", "authService", "login", "credentials", "response", "post", "token", "user", "data", "localStorage", "setItem", "JSON", "stringify", "error", "_error$response", "console", "message", "register", "userData", "endpoints", "config", "headers", "FormData", "endpoint", "log", "warn", "Error", "_error$response2", "logout", "removeItem", "getCurrentUser", "get", "_error$response3", "updateProfile", "patch", "_error$response4"], "sources": ["C:/Users/<USER>/Desktop/project ai/Store Management System/asset management/frontend/src/services/auth.js"], "sourcesContent": ["import api from '../utils/axios';\n\nexport const authService = {\n  login: async (credentials) => {\n    try {\n      const response = await api.post('/auth/token/', credentials);\n      const { token, user } = response.data;\n\n      if (token) {\n        localStorage.setItem('token', token);\n        localStorage.setItem('user', JSON.stringify(user || {}));\n      }\n\n      return response.data;\n    } catch (error) {\n      console.error('Login error:', error);\n      throw error.response?.data || error.message;\n    }\n  },\n\n  register: async (userData) => {\n    try {\n      // Try multiple endpoints\n      const endpoints = [\n        '/auth/register/',\n        '/api/auth/register/',\n        '/api/v1/auth/register/',\n        '/register/',\n        '/api/users/'\n      ];\n\n      // Set the correct content type for FormData\n      const config = {\n        headers: {\n          'Content-Type': userData instanceof FormData ? 'multipart/form-data' : 'application/json'\n        }\n      };\n\n      for (const endpoint of endpoints) {\n        try {\n          console.log(`Trying to register at ${endpoint}`);\n          const response = await api.post(endpoint, userData, config);\n          return response.data;\n        } catch (error) {\n          console.warn(`Error with ${endpoint} endpoint:`, error.message);\n          // Continue to the next endpoint\n        }\n      }\n\n      // If we get here, all endpoints failed\n      throw new Error('All registration endpoints failed');\n    } catch (error) {\n      throw error.response?.data || error.message;\n    }\n  },\n\n  logout: () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n  },\n\n  getCurrentUser: async () => {\n    try {\n      const response = await api.get('/auth/user/');\n      return response.data;\n    } catch (error) {\n      throw error.response?.data || error.message;\n    }\n  },\n\n  updateProfile: async (userData) => {\n    try {\n      // Try multiple endpoints\n      const endpoints = [\n        '/auth/user/',\n        '/api/auth/user/',\n        '/api/v1/auth/user/',\n        '/api/users/me/',\n        '/api/v1/users/me/'\n      ];\n\n      // Set the correct content type for FormData\n      const config = {\n        headers: {\n          'Content-Type': userData instanceof FormData ? 'multipart/form-data' : 'application/json'\n        }\n      };\n\n      for (const endpoint of endpoints) {\n        try {\n          console.log(`Trying to update user profile at ${endpoint}`);\n          const response = await api.patch(endpoint, userData, config);\n          return response.data;\n        } catch (error) {\n          console.warn(`Error with ${endpoint} endpoint:`, error.message);\n          // Continue to the next endpoint\n        }\n      }\n\n      // If we get here, all endpoints failed\n      throw new Error('All profile update endpoints failed');\n    } catch (error) {\n      throw error.response?.data || error.message;\n    }\n  }\n};\n\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,gBAAgB;AAEhC,OAAO,MAAMC,WAAW,GAAG;EACzBC,KAAK,EAAE,MAAOC,WAAW,IAAK;IAC5B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMJ,GAAG,CAACK,IAAI,CAAC,cAAc,EAAEF,WAAW,CAAC;MAC5D,MAAM;QAAEG,KAAK;QAAEC;MAAK,CAAC,GAAGH,QAAQ,CAACI,IAAI;MAErC,IAAIF,KAAK,EAAE;QACTG,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEJ,KAAK,CAAC;QACpCG,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACL,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;MAC1D;MAEA,OAAOH,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOK,KAAK,EAAE;MAAA,IAAAC,eAAA;MACdC,OAAO,CAACF,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC,MAAM,EAAAC,eAAA,GAAAD,KAAK,CAACT,QAAQ,cAAAU,eAAA,uBAAdA,eAAA,CAAgBN,IAAI,KAAIK,KAAK,CAACG,OAAO;IAC7C;EACF,CAAC;EAEDC,QAAQ,EAAE,MAAOC,QAAQ,IAAK;IAC5B,IAAI;MACF;MACA,MAAMC,SAAS,GAAG,CAChB,iBAAiB,EACjB,qBAAqB,EACrB,wBAAwB,EACxB,YAAY,EACZ,aAAa,CACd;;MAED;MACA,MAAMC,MAAM,GAAG;QACbC,OAAO,EAAE;UACP,cAAc,EAAEH,QAAQ,YAAYI,QAAQ,GAAG,qBAAqB,GAAG;QACzE;MACF,CAAC;MAED,KAAK,MAAMC,QAAQ,IAAIJ,SAAS,EAAE;QAChC,IAAI;UACFJ,OAAO,CAACS,GAAG,CAAC,yBAAyBD,QAAQ,EAAE,CAAC;UAChD,MAAMnB,QAAQ,GAAG,MAAMJ,GAAG,CAACK,IAAI,CAACkB,QAAQ,EAAEL,QAAQ,EAAEE,MAAM,CAAC;UAC3D,OAAOhB,QAAQ,CAACI,IAAI;QACtB,CAAC,CAAC,OAAOK,KAAK,EAAE;UACdE,OAAO,CAACU,IAAI,CAAC,cAAcF,QAAQ,YAAY,EAAEV,KAAK,CAACG,OAAO,CAAC;UAC/D;QACF;MACF;;MAEA;MACA,MAAM,IAAIU,KAAK,CAAC,mCAAmC,CAAC;IACtD,CAAC,CAAC,OAAOb,KAAK,EAAE;MAAA,IAAAc,gBAAA;MACd,MAAM,EAAAA,gBAAA,GAAAd,KAAK,CAACT,QAAQ,cAAAuB,gBAAA,uBAAdA,gBAAA,CAAgBnB,IAAI,KAAIK,KAAK,CAACG,OAAO;IAC7C;EACF,CAAC;EAEDY,MAAM,EAAEA,CAAA,KAAM;IACZnB,YAAY,CAACoB,UAAU,CAAC,OAAO,CAAC;IAChCpB,YAAY,CAACoB,UAAU,CAAC,MAAM,CAAC;EACjC,CAAC;EAEDC,cAAc,EAAE,MAAAA,CAAA,KAAY;IAC1B,IAAI;MACF,MAAM1B,QAAQ,GAAG,MAAMJ,GAAG,CAAC+B,GAAG,CAAC,aAAa,CAAC;MAC7C,OAAO3B,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOK,KAAK,EAAE;MAAA,IAAAmB,gBAAA;MACd,MAAM,EAAAA,gBAAA,GAAAnB,KAAK,CAACT,QAAQ,cAAA4B,gBAAA,uBAAdA,gBAAA,CAAgBxB,IAAI,KAAIK,KAAK,CAACG,OAAO;IAC7C;EACF,CAAC;EAEDiB,aAAa,EAAE,MAAOf,QAAQ,IAAK;IACjC,IAAI;MACF;MACA,MAAMC,SAAS,GAAG,CAChB,aAAa,EACb,iBAAiB,EACjB,oBAAoB,EACpB,gBAAgB,EAChB,mBAAmB,CACpB;;MAED;MACA,MAAMC,MAAM,GAAG;QACbC,OAAO,EAAE;UACP,cAAc,EAAEH,QAAQ,YAAYI,QAAQ,GAAG,qBAAqB,GAAG;QACzE;MACF,CAAC;MAED,KAAK,MAAMC,QAAQ,IAAIJ,SAAS,EAAE;QAChC,IAAI;UACFJ,OAAO,CAACS,GAAG,CAAC,oCAAoCD,QAAQ,EAAE,CAAC;UAC3D,MAAMnB,QAAQ,GAAG,MAAMJ,GAAG,CAACkC,KAAK,CAACX,QAAQ,EAAEL,QAAQ,EAAEE,MAAM,CAAC;UAC5D,OAAOhB,QAAQ,CAACI,IAAI;QACtB,CAAC,CAAC,OAAOK,KAAK,EAAE;UACdE,OAAO,CAACU,IAAI,CAAC,cAAcF,QAAQ,YAAY,EAAEV,KAAK,CAACG,OAAO,CAAC;UAC/D;QACF;MACF;;MAEA;MACA,MAAM,IAAIU,KAAK,CAAC,qCAAqC,CAAC;IACxD,CAAC,CAAC,OAAOb,KAAK,EAAE;MAAA,IAAAsB,gBAAA;MACd,MAAM,EAAAA,gBAAA,GAAAtB,KAAK,CAACT,QAAQ,cAAA+B,gBAAA,uBAAdA,gBAAA,CAAgB3B,IAAI,KAAIK,KAAK,CAACG,OAAO;IAC7C;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}