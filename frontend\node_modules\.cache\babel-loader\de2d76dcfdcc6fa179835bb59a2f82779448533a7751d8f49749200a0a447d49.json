{"ast": null, "code": "/**\n * Global error handler for browser extension and other errors\n */\n\n// Filter out browser extension errors that we can't control\nconst isBrowserExtensionError = error => {\n  if (!error) return false;\n  const message = error.message || '';\n  const stack = error.stack || '';\n  return message.includes('message channel closed') || message.includes('Extension context invalidated') || message.includes('chrome-extension://') || message.includes('moz-extension://') || stack.includes('chrome-extension://') || stack.includes('moz-extension://') || message.includes('listener indicated an asynchronous response');\n};\n\n// Global error handler\nconst setupGlobalErrorHandler = () => {\n  // Handle unhandled promise rejections\n  window.addEventListener('unhandledrejection', event => {\n    if (isBrowserExtensionError(event.reason)) {\n      var _event$reason;\n      console.warn('Browser extension error ignored:', ((_event$reason = event.reason) === null || _event$reason === void 0 ? void 0 : _event$reason.message) || event.reason);\n      event.preventDefault(); // Prevent the error from being logged to console\n      return;\n    }\n\n    // Log other errors normally\n    console.error('Unhandled promise rejection:', event.reason);\n  });\n\n  // Handle general errors\n  window.addEventListener('error', event => {\n    if (isBrowserExtensionError(event.error)) {\n      var _event$error;\n      console.warn('Browser extension error ignored:', ((_event$error = event.error) === null || _event$error === void 0 ? void 0 : _event$error.message) || event.error);\n      event.preventDefault(); // Prevent the error from being logged to console\n      return;\n    }\n\n    // Log other errors normally\n    console.error('Global error:', event.error);\n  });\n\n  // Override console.error to filter extension errors\n  const originalConsoleError = console.error;\n  console.error = (...args) => {\n    const errorMessage = args.join(' ');\n    if (errorMessage.includes('message channel closed') || errorMessage.includes('Extension context invalidated') || errorMessage.includes('chrome-extension://') || errorMessage.includes('moz-extension://') || errorMessage.includes('listener indicated an asynchronous response')) {\n      // Silently ignore browser extension errors\n      return;\n    }\n\n    // Log other errors normally\n    originalConsoleError.apply(console, args);\n  };\n};\n\n// Initialize error handling\nexport const initializeErrorHandling = () => {\n  setupGlobalErrorHandler();\n  console.log('✅ Global error handling initialized');\n  console.log('🔇 Browser extension errors will be silently ignored');\n};\nexport default {\n  initializeErrorHandling,\n  isBrowserExtensionError\n};", "map": {"version": 3, "names": ["isBrowserExtensionError", "error", "message", "stack", "includes", "setupGlobalErrorHandler", "window", "addEventListener", "event", "reason", "_event$reason", "console", "warn", "preventDefault", "_event$error", "originalConsoleError", "args", "errorMessage", "join", "apply", "initializeErrorHandling", "log"], "sources": ["C:/Users/<USER>/Desktop/project ai/Store Management System/asset management/frontend/src/utils/errorHandler.js"], "sourcesContent": ["/**\n * Global error handler for browser extension and other errors\n */\n\n// Filter out browser extension errors that we can't control\nconst isBrowserExtensionError = (error) => {\n  if (!error) return false;\n  \n  const message = error.message || '';\n  const stack = error.stack || '';\n  \n  return (\n    message.includes('message channel closed') ||\n    message.includes('Extension context invalidated') ||\n    message.includes('chrome-extension://') ||\n    message.includes('moz-extension://') ||\n    stack.includes('chrome-extension://') ||\n    stack.includes('moz-extension://') ||\n    message.includes('listener indicated an asynchronous response')\n  );\n};\n\n// Global error handler\nconst setupGlobalErrorHandler = () => {\n  // Handle unhandled promise rejections\n  window.addEventListener('unhandledrejection', (event) => {\n    if (isBrowserExtensionError(event.reason)) {\n      console.warn('Browser extension error ignored:', event.reason?.message || event.reason);\n      event.preventDefault(); // Prevent the error from being logged to console\n      return;\n    }\n    \n    // Log other errors normally\n    console.error('Unhandled promise rejection:', event.reason);\n  });\n\n  // Handle general errors\n  window.addEventListener('error', (event) => {\n    if (isBrowserExtensionError(event.error)) {\n      console.warn('Browser extension error ignored:', event.error?.message || event.error);\n      event.preventDefault(); // Prevent the error from being logged to console\n      return;\n    }\n    \n    // Log other errors normally\n    console.error('Global error:', event.error);\n  });\n\n  // Override console.error to filter extension errors\n  const originalConsoleError = console.error;\n  console.error = (...args) => {\n    const errorMessage = args.join(' ');\n    \n    if (\n      errorMessage.includes('message channel closed') ||\n      errorMessage.includes('Extension context invalidated') ||\n      errorMessage.includes('chrome-extension://') ||\n      errorMessage.includes('moz-extension://') ||\n      errorMessage.includes('listener indicated an asynchronous response')\n    ) {\n      // Silently ignore browser extension errors\n      return;\n    }\n    \n    // Log other errors normally\n    originalConsoleError.apply(console, args);\n  };\n};\n\n// Initialize error handling\nexport const initializeErrorHandling = () => {\n  setupGlobalErrorHandler();\n  \n  console.log('✅ Global error handling initialized');\n  console.log('🔇 Browser extension errors will be silently ignored');\n};\n\nexport default {\n  initializeErrorHandling,\n  isBrowserExtensionError,\n};\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA,MAAMA,uBAAuB,GAAIC,KAAK,IAAK;EACzC,IAAI,CAACA,KAAK,EAAE,OAAO,KAAK;EAExB,MAAMC,OAAO,GAAGD,KAAK,CAACC,OAAO,IAAI,EAAE;EACnC,MAAMC,KAAK,GAAGF,KAAK,CAACE,KAAK,IAAI,EAAE;EAE/B,OACED,OAAO,CAACE,QAAQ,CAAC,wBAAwB,CAAC,IAC1CF,OAAO,CAACE,QAAQ,CAAC,+BAA+B,CAAC,IACjDF,OAAO,CAACE,QAAQ,CAAC,qBAAqB,CAAC,IACvCF,OAAO,CAACE,QAAQ,CAAC,kBAAkB,CAAC,IACpCD,KAAK,CAACC,QAAQ,CAAC,qBAAqB,CAAC,IACrCD,KAAK,CAACC,QAAQ,CAAC,kBAAkB,CAAC,IAClCF,OAAO,CAACE,QAAQ,CAAC,6CAA6C,CAAC;AAEnE,CAAC;;AAED;AACA,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;EACpC;EACAC,MAAM,CAACC,gBAAgB,CAAC,oBAAoB,EAAGC,KAAK,IAAK;IACvD,IAAIR,uBAAuB,CAACQ,KAAK,CAACC,MAAM,CAAC,EAAE;MAAA,IAAAC,aAAA;MACzCC,OAAO,CAACC,IAAI,CAAC,kCAAkC,EAAE,EAAAF,aAAA,GAAAF,KAAK,CAACC,MAAM,cAAAC,aAAA,uBAAZA,aAAA,CAAcR,OAAO,KAAIM,KAAK,CAACC,MAAM,CAAC;MACvFD,KAAK,CAACK,cAAc,CAAC,CAAC,CAAC,CAAC;MACxB;IACF;;IAEA;IACAF,OAAO,CAACV,KAAK,CAAC,8BAA8B,EAAEO,KAAK,CAACC,MAAM,CAAC;EAC7D,CAAC,CAAC;;EAEF;EACAH,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;IAC1C,IAAIR,uBAAuB,CAACQ,KAAK,CAACP,KAAK,CAAC,EAAE;MAAA,IAAAa,YAAA;MACxCH,OAAO,CAACC,IAAI,CAAC,kCAAkC,EAAE,EAAAE,YAAA,GAAAN,KAAK,CAACP,KAAK,cAAAa,YAAA,uBAAXA,YAAA,CAAaZ,OAAO,KAAIM,KAAK,CAACP,KAAK,CAAC;MACrFO,KAAK,CAACK,cAAc,CAAC,CAAC,CAAC,CAAC;MACxB;IACF;;IAEA;IACAF,OAAO,CAACV,KAAK,CAAC,eAAe,EAAEO,KAAK,CAACP,KAAK,CAAC;EAC7C,CAAC,CAAC;;EAEF;EACA,MAAMc,oBAAoB,GAAGJ,OAAO,CAACV,KAAK;EAC1CU,OAAO,CAACV,KAAK,GAAG,CAAC,GAAGe,IAAI,KAAK;IAC3B,MAAMC,YAAY,GAAGD,IAAI,CAACE,IAAI,CAAC,GAAG,CAAC;IAEnC,IACED,YAAY,CAACb,QAAQ,CAAC,wBAAwB,CAAC,IAC/Ca,YAAY,CAACb,QAAQ,CAAC,+BAA+B,CAAC,IACtDa,YAAY,CAACb,QAAQ,CAAC,qBAAqB,CAAC,IAC5Ca,YAAY,CAACb,QAAQ,CAAC,kBAAkB,CAAC,IACzCa,YAAY,CAACb,QAAQ,CAAC,6CAA6C,CAAC,EACpE;MACA;MACA;IACF;;IAEA;IACAW,oBAAoB,CAACI,KAAK,CAACR,OAAO,EAAEK,IAAI,CAAC;EAC3C,CAAC;AACH,CAAC;;AAED;AACA,OAAO,MAAMI,uBAAuB,GAAGA,CAAA,KAAM;EAC3Cf,uBAAuB,CAAC,CAAC;EAEzBM,OAAO,CAACU,GAAG,CAAC,qCAAqC,CAAC;EAClDV,OAAO,CAACU,GAAG,CAAC,sDAAsD,CAAC;AACrE,CAAC;AAED,eAAe;EACbD,uBAAuB;EACvBpB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}