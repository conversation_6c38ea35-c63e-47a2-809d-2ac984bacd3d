{"ast": null, "code": "import api from '../utils/axios';\nexport const authService = {\n  login: async credentials => {\n    try {\n      const response = await api.post('/auth/token/', credentials);\n      const {\n        token,\n        user\n      } = response.data;\n      for (const endpoint of endpoints) {\n        try {\n          console.log(`Trying to login at ${endpoint}`);\n          const response = await api.post(endpoint, credentials);\n          console.log('Login response data:', response.data);\n\n          // Handle different token formats\n          if (response.data.token) {\n            localStorage.setItem('token', response.data.token);\n            localStorage.setItem('user', JSON.stringify(response.data.user || {}));\n          } else if (response.data.access) {\n            localStorage.setItem('token', response.data.access);\n            localStorage.setItem('user', JSON.stringify(response.data.user || {}));\n          } else if (response.data.access_token) {\n            localStorage.setItem('token', response.data.access_token);\n            localStorage.setItem('user', JSON.stringify(response.data.user || {}));\n          }\n\n          // If we don't have user data but have a token, create a minimal user object\n          if (!response.data.user && (response.data.token || response.data.access || response.data.access_token)) {\n            localStorage.setItem('user', JSON.stringify({\n              username: credentials.username,\n              id: 'unknown'\n            }));\n          }\n          return response.data;\n        } catch (error) {\n          console.warn(`Error with ${endpoint} endpoint:`, error.message);\n          // Continue to the next endpoint\n        }\n      }\n\n      // If we get here, all endpoints failed\n      console.error('All login endpoints failed');\n      throw new Error('Login failed. Please check your credentials or contact the administrator.');\n    } catch (error) {\n      var _error$response;\n      console.error('Login error:', error);\n\n      // Handle different error formats\n      if ((_error$response = error.response) !== null && _error$response !== void 0 && _error$response.data) {\n        if (typeof error.response.data === 'string') {\n          throw error.response.data;\n        } else if (error.response.data.detail) {\n          throw error.response.data.detail;\n        } else if (error.response.data.error) {\n          throw error.response.data.error;\n        } else if (error.response.data.message) {\n          throw error.response.data.message;\n        } else if (typeof error.response.data === 'object') {\n          // If it's an object with multiple fields, create a readable message\n          const errorMessages = Object.entries(error.response.data).map(([key, value]) => `${key}: ${Array.isArray(value) ? value.join(', ') : value}`).join('; ');\n          throw errorMessages;\n        }\n      }\n      throw error.message || 'Login failed. Please try again.';\n    }\n  },\n  register: async userData => {\n    try {\n      // Try multiple endpoints\n      const endpoints = ['/auth/register/', '/api/auth/register/', '/api/v1/auth/register/', '/register/', '/api/users/'];\n\n      // Set the correct content type for FormData\n      const config = {\n        headers: {\n          'Content-Type': userData instanceof FormData ? 'multipart/form-data' : 'application/json'\n        }\n      };\n      for (const endpoint of endpoints) {\n        try {\n          console.log(`Trying to register at ${endpoint}`);\n          const response = await api.post(endpoint, userData, config);\n          return response.data;\n        } catch (error) {\n          console.warn(`Error with ${endpoint} endpoint:`, error.message);\n          // Continue to the next endpoint\n        }\n      }\n\n      // If we get here, all endpoints failed\n      throw new Error('All registration endpoints failed');\n    } catch (error) {\n      var _error$response2;\n      throw ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data) || error.message;\n    }\n  },\n  logout: () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n  },\n  getCurrentUser: async () => {\n    try {\n      const response = await api.get('/auth/user/');\n      return response.data;\n    } catch (error) {\n      var _error$response3;\n      throw ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data) || error.message;\n    }\n  },\n  updateProfile: async userData => {\n    try {\n      // Try multiple endpoints\n      const endpoints = ['/auth/user/', '/api/auth/user/', '/api/v1/auth/user/', '/api/users/me/', '/api/v1/users/me/'];\n\n      // Set the correct content type for FormData\n      const config = {\n        headers: {\n          'Content-Type': userData instanceof FormData ? 'multipart/form-data' : 'application/json'\n        }\n      };\n      for (const endpoint of endpoints) {\n        try {\n          console.log(`Trying to update user profile at ${endpoint}`);\n          const response = await api.patch(endpoint, userData, config);\n          return response.data;\n        } catch (error) {\n          console.warn(`Error with ${endpoint} endpoint:`, error.message);\n          // Continue to the next endpoint\n        }\n      }\n\n      // If we get here, all endpoints failed\n      throw new Error('All profile update endpoints failed');\n    } catch (error) {\n      var _error$response4;\n      throw ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : _error$response4.data) || error.message;\n    }\n  }\n};", "map": {"version": 3, "names": ["api", "authService", "login", "credentials", "response", "post", "token", "user", "data", "endpoint", "endpoints", "console", "log", "localStorage", "setItem", "JSON", "stringify", "access", "access_token", "username", "id", "error", "warn", "message", "Error", "_error$response", "detail", "errorMessages", "Object", "entries", "map", "key", "value", "Array", "isArray", "join", "register", "userData", "config", "headers", "FormData", "_error$response2", "logout", "removeItem", "getCurrentUser", "get", "_error$response3", "updateProfile", "patch", "_error$response4"], "sources": ["C:/Users/<USER>/Desktop/project ai/Store Management System/asset management/frontend/src/services/auth.js"], "sourcesContent": ["import api from '../utils/axios';\n\nexport const authService = {\n  login: async (credentials) => {\n    try {\n      const response = await api.post('/auth/token/', credentials);\n      const { token, user } = response.data;\n\n      for (const endpoint of endpoints) {\n        try {\n          console.log(`Trying to login at ${endpoint}`);\n          const response = await api.post(endpoint, credentials);\n\n          console.log('Login response data:', response.data);\n\n          // Handle different token formats\n          if (response.data.token) {\n            localStorage.setItem('token', response.data.token);\n            localStorage.setItem('user', JSON.stringify(response.data.user || {}));\n          } else if (response.data.access) {\n            localStorage.setItem('token', response.data.access);\n            localStorage.setItem('user', JSON.stringify(response.data.user || {}));\n          } else if (response.data.access_token) {\n            localStorage.setItem('token', response.data.access_token);\n            localStorage.setItem('user', JSON.stringify(response.data.user || {}));\n          }\n\n          // If we don't have user data but have a token, create a minimal user object\n          if (!response.data.user && (response.data.token || response.data.access || response.data.access_token)) {\n            localStorage.setItem('user', JSON.stringify({\n              username: credentials.username,\n              id: 'unknown'\n            }));\n          }\n\n          return response.data;\n        } catch (error) {\n          console.warn(`Error with ${endpoint} endpoint:`, error.message);\n          // Continue to the next endpoint\n        }\n      }\n\n      // If we get here, all endpoints failed\n      console.error('All login endpoints failed');\n      throw new Error('Login failed. Please check your credentials or contact the administrator.');\n    } catch (error) {\n      console.error('Login error:', error);\n\n      // Handle different error formats\n      if (error.response?.data) {\n        if (typeof error.response.data === 'string') {\n          throw error.response.data;\n        } else if (error.response.data.detail) {\n          throw error.response.data.detail;\n        } else if (error.response.data.error) {\n          throw error.response.data.error;\n        } else if (error.response.data.message) {\n          throw error.response.data.message;\n        } else if (typeof error.response.data === 'object') {\n          // If it's an object with multiple fields, create a readable message\n          const errorMessages = Object.entries(error.response.data)\n            .map(([key, value]) => `${key}: ${Array.isArray(value) ? value.join(', ') : value}`)\n            .join('; ');\n          throw errorMessages;\n        }\n      }\n\n      throw error.message || 'Login failed. Please try again.';\n    }\n  },\n\n  register: async (userData) => {\n    try {\n      // Try multiple endpoints\n      const endpoints = [\n        '/auth/register/',\n        '/api/auth/register/',\n        '/api/v1/auth/register/',\n        '/register/',\n        '/api/users/'\n      ];\n\n      // Set the correct content type for FormData\n      const config = {\n        headers: {\n          'Content-Type': userData instanceof FormData ? 'multipart/form-data' : 'application/json'\n        }\n      };\n\n      for (const endpoint of endpoints) {\n        try {\n          console.log(`Trying to register at ${endpoint}`);\n          const response = await api.post(endpoint, userData, config);\n          return response.data;\n        } catch (error) {\n          console.warn(`Error with ${endpoint} endpoint:`, error.message);\n          // Continue to the next endpoint\n        }\n      }\n\n      // If we get here, all endpoints failed\n      throw new Error('All registration endpoints failed');\n    } catch (error) {\n      throw error.response?.data || error.message;\n    }\n  },\n\n  logout: () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n  },\n\n  getCurrentUser: async () => {\n    try {\n      const response = await api.get('/auth/user/');\n      return response.data;\n    } catch (error) {\n      throw error.response?.data || error.message;\n    }\n  },\n\n  updateProfile: async (userData) => {\n    try {\n      // Try multiple endpoints\n      const endpoints = [\n        '/auth/user/',\n        '/api/auth/user/',\n        '/api/v1/auth/user/',\n        '/api/users/me/',\n        '/api/v1/users/me/'\n      ];\n\n      // Set the correct content type for FormData\n      const config = {\n        headers: {\n          'Content-Type': userData instanceof FormData ? 'multipart/form-data' : 'application/json'\n        }\n      };\n\n      for (const endpoint of endpoints) {\n        try {\n          console.log(`Trying to update user profile at ${endpoint}`);\n          const response = await api.patch(endpoint, userData, config);\n          return response.data;\n        } catch (error) {\n          console.warn(`Error with ${endpoint} endpoint:`, error.message);\n          // Continue to the next endpoint\n        }\n      }\n\n      // If we get here, all endpoints failed\n      throw new Error('All profile update endpoints failed');\n    } catch (error) {\n      throw error.response?.data || error.message;\n    }\n  }\n};\n\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,gBAAgB;AAEhC,OAAO,MAAMC,WAAW,GAAG;EACzBC,KAAK,EAAE,MAAOC,WAAW,IAAK;IAC5B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMJ,GAAG,CAACK,IAAI,CAAC,cAAc,EAAEF,WAAW,CAAC;MAC5D,MAAM;QAAEG,KAAK;QAAEC;MAAK,CAAC,GAAGH,QAAQ,CAACI,IAAI;MAErC,KAAK,MAAMC,QAAQ,IAAIC,SAAS,EAAE;QAChC,IAAI;UACFC,OAAO,CAACC,GAAG,CAAC,sBAAsBH,QAAQ,EAAE,CAAC;UAC7C,MAAML,QAAQ,GAAG,MAAMJ,GAAG,CAACK,IAAI,CAACI,QAAQ,EAAEN,WAAW,CAAC;UAEtDQ,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAER,QAAQ,CAACI,IAAI,CAAC;;UAElD;UACA,IAAIJ,QAAQ,CAACI,IAAI,CAACF,KAAK,EAAE;YACvBO,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEV,QAAQ,CAACI,IAAI,CAACF,KAAK,CAAC;YAClDO,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACZ,QAAQ,CAACI,IAAI,CAACD,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;UACxE,CAAC,MAAM,IAAIH,QAAQ,CAACI,IAAI,CAACS,MAAM,EAAE;YAC/BJ,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEV,QAAQ,CAACI,IAAI,CAACS,MAAM,CAAC;YACnDJ,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACZ,QAAQ,CAACI,IAAI,CAACD,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;UACxE,CAAC,MAAM,IAAIH,QAAQ,CAACI,IAAI,CAACU,YAAY,EAAE;YACrCL,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEV,QAAQ,CAACI,IAAI,CAACU,YAAY,CAAC;YACzDL,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACZ,QAAQ,CAACI,IAAI,CAACD,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;UACxE;;UAEA;UACA,IAAI,CAACH,QAAQ,CAACI,IAAI,CAACD,IAAI,KAAKH,QAAQ,CAACI,IAAI,CAACF,KAAK,IAAIF,QAAQ,CAACI,IAAI,CAACS,MAAM,IAAIb,QAAQ,CAACI,IAAI,CAACU,YAAY,CAAC,EAAE;YACtGL,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAAC;cAC1CG,QAAQ,EAAEhB,WAAW,CAACgB,QAAQ;cAC9BC,EAAE,EAAE;YACN,CAAC,CAAC,CAAC;UACL;UAEA,OAAOhB,QAAQ,CAACI,IAAI;QACtB,CAAC,CAAC,OAAOa,KAAK,EAAE;UACdV,OAAO,CAACW,IAAI,CAAC,cAAcb,QAAQ,YAAY,EAAEY,KAAK,CAACE,OAAO,CAAC;UAC/D;QACF;MACF;;MAEA;MACAZ,OAAO,CAACU,KAAK,CAAC,4BAA4B,CAAC;MAC3C,MAAM,IAAIG,KAAK,CAAC,2EAA2E,CAAC;IAC9F,CAAC,CAAC,OAAOH,KAAK,EAAE;MAAA,IAAAI,eAAA;MACdd,OAAO,CAACU,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;;MAEpC;MACA,KAAAI,eAAA,GAAIJ,KAAK,CAACjB,QAAQ,cAAAqB,eAAA,eAAdA,eAAA,CAAgBjB,IAAI,EAAE;QACxB,IAAI,OAAOa,KAAK,CAACjB,QAAQ,CAACI,IAAI,KAAK,QAAQ,EAAE;UAC3C,MAAMa,KAAK,CAACjB,QAAQ,CAACI,IAAI;QAC3B,CAAC,MAAM,IAAIa,KAAK,CAACjB,QAAQ,CAACI,IAAI,CAACkB,MAAM,EAAE;UACrC,MAAML,KAAK,CAACjB,QAAQ,CAACI,IAAI,CAACkB,MAAM;QAClC,CAAC,MAAM,IAAIL,KAAK,CAACjB,QAAQ,CAACI,IAAI,CAACa,KAAK,EAAE;UACpC,MAAMA,KAAK,CAACjB,QAAQ,CAACI,IAAI,CAACa,KAAK;QACjC,CAAC,MAAM,IAAIA,KAAK,CAACjB,QAAQ,CAACI,IAAI,CAACe,OAAO,EAAE;UACtC,MAAMF,KAAK,CAACjB,QAAQ,CAACI,IAAI,CAACe,OAAO;QACnC,CAAC,MAAM,IAAI,OAAOF,KAAK,CAACjB,QAAQ,CAACI,IAAI,KAAK,QAAQ,EAAE;UAClD;UACA,MAAMmB,aAAa,GAAGC,MAAM,CAACC,OAAO,CAACR,KAAK,CAACjB,QAAQ,CAACI,IAAI,CAAC,CACtDsB,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK,GAAGD,GAAG,KAAKE,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,GAAGA,KAAK,CAACG,IAAI,CAAC,IAAI,CAAC,GAAGH,KAAK,EAAE,CAAC,CACnFG,IAAI,CAAC,IAAI,CAAC;UACb,MAAMR,aAAa;QACrB;MACF;MAEA,MAAMN,KAAK,CAACE,OAAO,IAAI,iCAAiC;IAC1D;EACF,CAAC;EAEDa,QAAQ,EAAE,MAAOC,QAAQ,IAAK;IAC5B,IAAI;MACF;MACA,MAAM3B,SAAS,GAAG,CAChB,iBAAiB,EACjB,qBAAqB,EACrB,wBAAwB,EACxB,YAAY,EACZ,aAAa,CACd;;MAED;MACA,MAAM4B,MAAM,GAAG;QACbC,OAAO,EAAE;UACP,cAAc,EAAEF,QAAQ,YAAYG,QAAQ,GAAG,qBAAqB,GAAG;QACzE;MACF,CAAC;MAED,KAAK,MAAM/B,QAAQ,IAAIC,SAAS,EAAE;QAChC,IAAI;UACFC,OAAO,CAACC,GAAG,CAAC,yBAAyBH,QAAQ,EAAE,CAAC;UAChD,MAAML,QAAQ,GAAG,MAAMJ,GAAG,CAACK,IAAI,CAACI,QAAQ,EAAE4B,QAAQ,EAAEC,MAAM,CAAC;UAC3D,OAAOlC,QAAQ,CAACI,IAAI;QACtB,CAAC,CAAC,OAAOa,KAAK,EAAE;UACdV,OAAO,CAACW,IAAI,CAAC,cAAcb,QAAQ,YAAY,EAAEY,KAAK,CAACE,OAAO,CAAC;UAC/D;QACF;MACF;;MAEA;MACA,MAAM,IAAIC,KAAK,CAAC,mCAAmC,CAAC;IACtD,CAAC,CAAC,OAAOH,KAAK,EAAE;MAAA,IAAAoB,gBAAA;MACd,MAAM,EAAAA,gBAAA,GAAApB,KAAK,CAACjB,QAAQ,cAAAqC,gBAAA,uBAAdA,gBAAA,CAAgBjC,IAAI,KAAIa,KAAK,CAACE,OAAO;IAC7C;EACF,CAAC;EAEDmB,MAAM,EAAEA,CAAA,KAAM;IACZ7B,YAAY,CAAC8B,UAAU,CAAC,OAAO,CAAC;IAChC9B,YAAY,CAAC8B,UAAU,CAAC,MAAM,CAAC;EACjC,CAAC;EAEDC,cAAc,EAAE,MAAAA,CAAA,KAAY;IAC1B,IAAI;MACF,MAAMxC,QAAQ,GAAG,MAAMJ,GAAG,CAAC6C,GAAG,CAAC,aAAa,CAAC;MAC7C,OAAOzC,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOa,KAAK,EAAE;MAAA,IAAAyB,gBAAA;MACd,MAAM,EAAAA,gBAAA,GAAAzB,KAAK,CAACjB,QAAQ,cAAA0C,gBAAA,uBAAdA,gBAAA,CAAgBtC,IAAI,KAAIa,KAAK,CAACE,OAAO;IAC7C;EACF,CAAC;EAEDwB,aAAa,EAAE,MAAOV,QAAQ,IAAK;IACjC,IAAI;MACF;MACA,MAAM3B,SAAS,GAAG,CAChB,aAAa,EACb,iBAAiB,EACjB,oBAAoB,EACpB,gBAAgB,EAChB,mBAAmB,CACpB;;MAED;MACA,MAAM4B,MAAM,GAAG;QACbC,OAAO,EAAE;UACP,cAAc,EAAEF,QAAQ,YAAYG,QAAQ,GAAG,qBAAqB,GAAG;QACzE;MACF,CAAC;MAED,KAAK,MAAM/B,QAAQ,IAAIC,SAAS,EAAE;QAChC,IAAI;UACFC,OAAO,CAACC,GAAG,CAAC,oCAAoCH,QAAQ,EAAE,CAAC;UAC3D,MAAML,QAAQ,GAAG,MAAMJ,GAAG,CAACgD,KAAK,CAACvC,QAAQ,EAAE4B,QAAQ,EAAEC,MAAM,CAAC;UAC5D,OAAOlC,QAAQ,CAACI,IAAI;QACtB,CAAC,CAAC,OAAOa,KAAK,EAAE;UACdV,OAAO,CAACW,IAAI,CAAC,cAAcb,QAAQ,YAAY,EAAEY,KAAK,CAACE,OAAO,CAAC;UAC/D;QACF;MACF;;MAEA;MACA,MAAM,IAAIC,KAAK,CAAC,qCAAqC,CAAC;IACxD,CAAC,CAAC,OAAOH,KAAK,EAAE;MAAA,IAAA4B,gBAAA;MACd,MAAM,EAAAA,gBAAA,GAAA5B,KAAK,CAACjB,QAAQ,cAAA6C,gBAAA,uBAAdA,gBAAA,CAAgBzC,IAAI,KAAIa,KAAK,CAACE,OAAO;IAC7C;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}