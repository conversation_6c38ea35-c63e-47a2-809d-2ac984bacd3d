import React from 'react';
import './styles/print.css';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline, Box } from '@mui/material';
import { SnackbarProvider } from 'notistack';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';

// Organization components
import OrganizationList from './features/organizations/OrganizationList';
import OrganizationTypeList from './features/organizations/OrganizationTypeList';
import OfficeList from './features/organizations/OfficeList';

// Classification components
import MainClassificationList from './features/classifications/MainClassificationList';
import SubClassificationList from './features/classifications/SubClassificationList';

// Specification components
import ItemTypeList from './features/specifications/ItemTypeList';
import ItemCategoryList from './features/specifications/ItemCategoryList';
import ItemBrandList from './features/specifications/ItemBrandList';
import ItemShapeList from './features/specifications/ItemShapeList';
import ItemSizeList from './features/specifications/ItemSizeList';
import ItemQualityList from './features/specifications/ItemQualityList';
import ItemManufacturerList from './features/specifications/ItemManufacturerList';
import UnitOfMeasureList from './features/specifications/UnitOfMeasureList';
import SpecificationsDashboard from './features/specifications/SpecificationsDashboard';
import StatusDashboard from './features/status/StatusDashboard';
import InventoryDashboard from './features/inventory/InventoryDashboard';
import ReceivingDashboard from './features/receiving/ReceivingDashboard';
import StorageDashboard from './features/storage/StorageDashboard';
import RequisitionDashboard from './features/requisitions/RequisitionDashboard';

// Storage components
import ShelfList from './features/storage/ShelfList';
import StoreTypeList from './features/storage/StoreTypeList';
import StoreList from './features/storage/StoreList';

// Supplier components
import SupplierList from './features/suppliers/SupplierList';

// Status components
import ItemStatusList from './features/status/ItemStatusList';
import PropertyStatusList from './features/status/PropertyStatusList';
import ApprovalStatusList from './features/status/ApprovalStatusList';

// Item components
import ItemMasterList from './features/items/ItemMasterList';
import ItemMasterDetail from './features/items/ItemMasterDetail';
import BatchList from './features/items/BatchList';
import BatchDetail from './features/items/BatchDetail';
import ItemList from './features/items/ItemList';
import ItemDetail from './features/items/ItemDetail';
import Model19Report from './features/items/Model19Report';

// Serial Voucher components
import SerialVoucherCategoryList from './features/serials/SerialVoucherCategoryList';
import SerialVoucherList from './features/serials/SerialVoucherList';
import VoucherRequestForm from './features/serials/VoucherRequestForm';
import VoucherDashboard from './features/serials/VoucherDashboard';

// Gate Pass components
import GatePassList from './features/gatepasses/GatePassList';

// Report components
import DiscrepancyTypeList from './features/reports/DiscrepancyTypeList';
import DamageReportList from './features/reports/DamageReportList';
import DamageReportDetail from './features/reports/DamageReportDetail';
import DamageReportForm from './features/reports/DamageReportForm';
import Model19Page from './features/reports/Model19Page';
import ReceivingInspection from './features/receiving/ReceivingInspection';
import DamageShortageReport from './features/receiving/DamageShortageReport';
import Model19Form from './features/receiving/Model19Form';
import Model19List from './features/receiving/Model19List';
import Model19Detail from './features/receiving/Model19Detail';
import DeliveryReceiptForm from './features/receiving/DeliveryReceiptForm';
import InspectionForm from './features/receiving/InspectionForm';
import InspectionDetail from './features/receiving/InspectionDetail';

// Requisition components
import RequisitionStatusList from './features/requisitions/RequisitionStatusList';
import RequisitionList from './features/requisitions/RequisitionList';
import RequisitionForm from './features/requisitions/RequisitionForm';
import RequisitionDetail from './features/requisitions/RequisitionDetail';
import Model22Preparation from './features/requisitions/Model22Preparation';
import Model22Report from './features/requisitions/Model22Report';
import BrowseAndRequestPage from './features/requisitions/BrowseAndRequestPage';
import ApiTest from './features/requisitions/ApiTest';

// Inspection components
import InspectionCommitteeList from './features/inspection/InspectionCommitteeList';
// import InspectionRoutes from './features/inspections';

// Entry Request components
import ItemEntryRequestList from './features/entryRequest/ItemEntryRequestList';
import ItemEntryRequestForm from './features/entryRequest/ItemEntryRequestForm';
import ItemEntryRequestDetail from './features/entryRequest/ItemEntryRequestDetail';

// Procurement components
import ItemEntryRequestFormNew from './features/procurement/ItemEntryRequestForm';
import ItemReceiveDashboard from './features/procurement/ItemReceiveDashboard';
import EntryRequestsList from './features/procurement/EntryRequestsList';

// Item Receive components
import ItemReceiveRoutes from './features/itemReceive';

// Dashboard component
import Dashboard from './features/dashboard/Dashboard';

// Auth and Layout
import Login from './features/auth/Login';
import Layout from './components/Layout';

const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#6366f1', // Indigo
      light: '#818cf8',
      dark: '#4f46e5',
      contrastText: '#ffffff',
    },
    secondary: {
      main: '#ec4899', // Pink
      light: '#f472b6',
      dark: '#db2777',
      contrastText: '#ffffff',
    },
    success: {
      main: '#10b981', // Emerald
      light: '#34d399',
      dark: '#059669',
    },
    error: {
      main: '#ef4444', // Red
      light: '#f87171',
      dark: '#dc2626',
    },
    warning: {
      main: '#f59e0b', // Amber
      light: '#fbbf24',
      dark: '#d97706',
    },
    info: {
      main: '#3b82f6', // Blue
      light: '#60a5fa',
      dark: '#2563eb',
    },
    purple: {
      main: '#AB47BC', // Purple
      light: '#CE93D8',
      dark: '#8E24AA',
      contrastText: '#ffffff',
    },
    background: {
      default: '#f9fafb',
      paper: '#ffffff',
    },
    contrastThreshold: 3,
    tonalOffset: 0.2,
    text: {
      primary: '#334155',
      secondary: '#64748b',
    },
  },
  typography: {
    fontFamily: '"Inter", "Plus Jakarta Sans", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontWeight: 800,
      letterSpacing: '-0.025em',
      fontSize: '2.5rem',
    },
    h2: {
      fontWeight: 700,
      letterSpacing: '-0.025em',
      fontSize: '2rem',
    },
    h3: {
      fontWeight: 700,
      letterSpacing: '-0.025em',
      fontSize: '1.75rem',
    },
    h4: {
      fontWeight: 700,
      fontSize: '1.5rem',
    },
    h5: {
      fontWeight: 600,
      fontSize: '1.25rem',
    },
    h6: {
      fontWeight: 600,
      fontSize: '1.125rem',
    },
    subtitle1: {
      fontWeight: 500,
      fontSize: '1rem',
    },
    subtitle2: {
      fontWeight: 500,
      fontSize: '0.875rem',
    },
    body1: {
      fontSize: '1rem',
      lineHeight: 1.5,
    },
    body2: {
      fontSize: '0.875rem',
      lineHeight: 1.5,
    },
    button: {
      textTransform: 'none',
      fontWeight: 600,
    },
  },
  shape: {
    borderRadius: 16,
  },
  shadows: [
    'none',
    '0px 1px 2px rgba(0, 0, 0, 0.06), 0px 1px 3px rgba(0, 0, 0, 0.1)',
    '0px 4px 6px -1px rgba(0, 0, 0, 0.1), 0px 2px 4px -2px rgba(0, 0, 0, 0.1)',
    '0px 10px 15px -3px rgba(0, 0, 0, 0.1), 0px 4px 6px -4px rgba(0, 0, 0, 0.1)',
    '0px 20px 25px -5px rgba(0, 0, 0, 0.1), 0px 8px 10px -6px rgba(0, 0, 0, 0.1)',
    ...Array(20).fill('none'),
  ],
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          padding: '10px 20px',
          boxShadow: 'none',
          fontWeight: 600,
          '&:hover': {
            boxShadow: '0 4px 10px rgba(0, 0, 0, 0.08)',
            transform: 'translateY(-2px)',
          },
          transition: 'all 0.2s ease-in-out',
        },
        contained: {
          '&:hover': {
            boxShadow: '0 6px 15px rgba(0, 0, 0, 0.1)',
          },
        },
        containedPrimary: {
          background: 'linear-gradient(135deg, #6366f1 0%, #4f46e5 100%)',
          '&:hover': {
            background: 'linear-gradient(135deg, #4f46e5 0%, #4338ca 100%)',
          },
        },
        containedSecondary: {
          background: 'linear-gradient(135deg, #ec4899 0%, #db2777 100%)',
          '&:hover': {
            background: 'linear-gradient(135deg, #db2777 0%, #be185d 100%)',
          },
        },
        outlined: {
          borderWidth: '1.5px',
          '&:hover': {
            borderWidth: '1.5px',
          },
        },
        sizeLarge: {
          padding: '12px 28px',
          fontSize: '1rem',
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: 16,
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)',
          backgroundImage: 'none',
        },
        elevation1: {
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.1)',
        },
        elevation2: {
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.05)',
        },
        elevation3: {
          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.05), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        },
        elevation4: {
          boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.05), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 16,
          overflow: 'hidden',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)',
          border: '1px solid rgba(0, 0, 0, 0.05)',
          '&:hover': {
            boxShadow: '0 8px 30px rgba(0, 0, 0, 0.08)',
            transform: 'translateY(-2px)',
          },
          transition: 'transform 0.3s, box-shadow 0.3s',
        },
      },
    },
    MuiCardContent: {
      styleOverrides: {
        root: {
          padding: 24,
          '&:last-child': {
            paddingBottom: 24,
          },
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: 12,
            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
              borderWidth: '2px',
              borderColor: '#6366f1',
            },
            '&:hover .MuiOutlinedInput-notchedOutline': {
              borderColor: '#6366f1',
            },
          },
          '& .MuiInputLabel-root.Mui-focused': {
            color: '#6366f1',
          },
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: 6,
          fontWeight: 500,
          '&.MuiChip-filled': {
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
          },
        },
        filledPrimary: {
          background: 'linear-gradient(135deg, #6366f1 0%, #4f46e5 100%)',
        },
        filledSecondary: {
          background: 'linear-gradient(135deg, #ec4899 0%, #db2777 100%)',
        },
      },
    },
    MuiListItem: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          '&.Mui-selected': {
            backgroundColor: 'rgba(99, 102, 241, 0.08)',
          },
        },
      },
    },
    MuiListItemButton: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          '&.Mui-selected': {
            backgroundColor: 'rgba(99, 102, 241, 0.08)',
            '&:hover': {
              backgroundColor: 'rgba(99, 102, 241, 0.12)',
            },
          },
          '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.04)',
          },
        },
      },
    },
    MuiAvatar: {
      styleOverrides: {
        root: {
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.1)',
          backgroundImage: 'none',
        },
        colorDefault: {
          backgroundColor: '#ffffff',
        },
      },
    },
    MuiDrawer: {
      styleOverrides: {
        paper: {
          borderRight: '1px solid rgba(0, 0, 0, 0.05)',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)',
        },
      },
    },
    MuiTableHead: {
      styleOverrides: {
        root: {
          backgroundColor: 'rgba(0, 0, 0, 0.02)',
          '& .MuiTableCell-root': {
            fontWeight: 600,
          },
        },
      },
    },
    MuiTableRow: {
      styleOverrides: {
        root: {
          '&:hover': {
            backgroundColor: '#f1f5f9',
          },
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        root: {
          borderBottom: '1px solid rgba(0, 0, 0, 0.05)',
        },
        head: {
          fontWeight: 600,
          backgroundColor: '#f8fafc',
        },
      },
    },
    MuiTooltip: {
      styleOverrides: {
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderRadius: 8,
          padding: '8px 12px',
          fontSize: '0.75rem',
        },
      },
    },
    // MuiTableCell, MuiTableRow, and MuiChip are already defined above
  },
});

const PrivateRoute = ({ children }) => {
  const token = localStorage.getItem('token');
  return token ? children : <Navigate to="/login" />;
};

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box sx={{ width: '100%', maxWidth: '100vw', overflow: 'hidden' }}>
        <SnackbarProvider
          maxSnack={5}
          autoHideDuration={5000}
          preventDuplicate
          dense
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'right',
          }}
        >
          <LocalizationProvider dateAdapter={AdapterDateFns}>
          <Router>
            <Routes>
              <Route path="/login" element={<Login />} />

              {/* Organization Routes */}
              <Route
                path="/organizations"
                element={
                  <PrivateRoute>
                    <Layout>
                      <OrganizationList />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/organization-types"
                element={
                  <PrivateRoute>
                    <Layout>
                      <OrganizationTypeList />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/offices"
                element={
                  <PrivateRoute>
                    <Layout>
                      <OfficeList />
                    </Layout>
                  </PrivateRoute>
                }
              />

              {/* Classification Routes */}
              <Route
                path="/main-classifications"
                element={
                  <PrivateRoute>
                    <Layout>
                      <MainClassificationList />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/sub-classifications"
                element={
                  <PrivateRoute>
                    <Layout>
                      <SubClassificationList />
                    </Layout>
                  </PrivateRoute>
                }
              />

              {/* Specification Routes */}
              <Route
                path="/specifications"
                element={
                  <PrivateRoute>
                    <Layout>
                      <SpecificationsDashboard />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/status-dashboard"
                element={
                  <PrivateRoute>
                    <Layout>
                      <StatusDashboard />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/inventory-dashboard"
                element={
                  <PrivateRoute>
                    <Layout>
                      <InventoryDashboard />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/receiving-dashboard"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ReceivingDashboard />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/storage-dashboard"
                element={
                  <PrivateRoute>
                    <Layout>
                      <StorageDashboard />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/requisition-dashboard"
                element={
                  <PrivateRoute>
                    <Layout>
                      <RequisitionDashboard />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/item-types"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ItemTypeList />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/item-categories"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ItemCategoryList />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/item-brands"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ItemBrandList />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/item-shapes"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ItemShapeList />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/item-sizes"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ItemSizeList />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/item-qualities"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ItemQualityList />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/item-manufacturers"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ItemManufacturerList />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/units-of-measure"
                element={
                  <PrivateRoute>
                    <Layout>
                      <UnitOfMeasureList />
                    </Layout>
                  </PrivateRoute>
                }
              />

              {/* Storage Routes */}
              <Route
                path="/store-types"
                element={
                  <PrivateRoute>
                    <Layout>
                      <StoreTypeList />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/stores"
                element={
                  <PrivateRoute>
                    <Layout>
                      <StoreList />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/shelves"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ShelfList />
                    </Layout>
                  </PrivateRoute>
                }
              />

              {/* Supplier Routes */}
              <Route
                path="/suppliers"
                element={
                  <PrivateRoute>
                    <Layout>
                      <SupplierList />
                    </Layout>
                  </PrivateRoute>
                }
              />

              {/* Status Routes */}
              <Route
                path="/item-statuses"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ItemStatusList />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/property-statuses"
                element={
                  <PrivateRoute>
                    <Layout>
                      <PropertyStatusList />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/approval-statuses"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ApprovalStatusList />
                    </Layout>
                  </PrivateRoute>
                }
              />

              {/* Item Routes */}
              <Route
                path="/item-masters"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ItemMasterList />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/item-masters/:id"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ItemMasterDetail />
                    </Layout>
                  </PrivateRoute>
                }
              />

              <Route
                path="/batches"
                element={
                  <PrivateRoute>
                    <Layout>
                      <BatchList />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/batches/:id"
                element={
                  <PrivateRoute>
                    <Layout>
                      <BatchDetail />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/batches/:id/model19"
                element={
                  <PrivateRoute>
                    <Layout>
                      <Model19Report />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/items"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ItemList />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/items/:id"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ItemDetail />
                    </Layout>
                  </PrivateRoute>
                }
              />

              {/* Gate Pass Routes */}
              <Route
                path="/gate-passes"
                element={
                  <PrivateRoute>
                    <Layout>
                      <GatePassList />
                    </Layout>
                  </PrivateRoute>
                }
              />

              {/* Report Routes */}
              <Route
                path="/discrepancy-types"
                element={
                  <PrivateRoute>
                    <Layout>
                      <DiscrepancyTypeList />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/damage-reports"
                element={
                  <PrivateRoute>
                    <Layout>
                      <DamageReportList />
                    </Layout>
                  </PrivateRoute>
                }
              />

              <Route
                path="/damage-reports/:id"
                element={
                  <PrivateRoute>
                    <Layout>
                      <DamageReportDetail />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/damage-reports/:id/edit"
                element={
                  <PrivateRoute>
                    <Layout>
                      <DamageReportForm />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/model19"
                element={
                  <PrivateRoute>
                    <Layout>
                      <Model19Page />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/receiving-inspection"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ReceivingInspection />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/damage-shortage-report"
                element={
                  <PrivateRoute>
                    <Layout>
                      <DamageShortageReport />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/damage-shortage-report/:id"
                element={
                  <PrivateRoute>
                    <Layout>
                      <DamageShortageReport />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/model19-form"
                element={
                  <PrivateRoute>
                    <Layout>
                      <Model19Form />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/item-masters/:itemMasterId/model19"
                element={
                  <PrivateRoute>
                    <Layout>
                      <Model19Form />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/batches/:batchId/model19"
                element={
                  <PrivateRoute>
                    <Layout>
                      <Model19Form />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/inspection/:inspectionId/model19"
                element={
                  <PrivateRoute>
                    <Layout>
                      <Model19Form />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/delivery-receipt-form"
                element={
                  <PrivateRoute>
                    <Layout>
                      <DeliveryReceiptForm />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/inspection-form/:deliveryReceiptId"
                element={
                  <PrivateRoute>
                    <Layout>
                      <InspectionForm />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/model19-receipts"
                element={
                  <PrivateRoute>
                    <Layout>
                      <Model19List />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/model19-receipts/:id"
                element={
                  <PrivateRoute>
                    <Layout>
                      <Model19Detail />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/model19-receipts/:id/print"
                element={
                  <PrivateRoute>
                    <Layout>
                      <Model19Detail />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/model19-receipts/:id/edit"
                element={
                  <PrivateRoute>
                    <Layout>
                      <Model19Form />
                    </Layout>
                  </PrivateRoute>
                }
              />

              {/* Additional Receiving Routes */}
              <Route
                path="/receiving-dashboard"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ReceivingDashboard />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/model19-form"
                element={
                  <PrivateRoute>
                    <Layout>
                      <Model19Form />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/model19-form/:inspectionId"
                element={
                  <PrivateRoute>
                    <Layout>
                      <Model19Form />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/delivery-receipts"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ReceivingDashboard />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/delivery-receipt/:id"
                element={
                  <PrivateRoute>
                    <Layout>
                      <DeliveryReceiptForm />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/delivery-receipt/new"
                element={
                  <PrivateRoute>
                    <Layout>
                      <DeliveryReceiptForm />
                    </Layout>
                  </PrivateRoute>
                }
              />
              {/* Temporarily commented out until we fix the dependencies
              <Route
                path="/inspections/*"
                element={
                  <PrivateRoute>
                    <Layout>
                      <InspectionRoutes />
                    </Layout>
                  </PrivateRoute>
                }
              />
              */}

              {/* Requisition Routes */}
              <Route
                path="/requisition-statuses"
                element={
                  <PrivateRoute>
                    <Layout>
                      <RequisitionStatusList />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/requisitions"
                element={
                  <PrivateRoute>
                    <Layout>
                      <RequisitionList />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/requisitions/new"
                element={
                  <PrivateRoute>
                    <Layout>
                      <RequisitionForm />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/requisitions/browse-and-request"
                element={
                  <PrivateRoute>
                    <Layout>
                      <BrowseAndRequestPage />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/api-test"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ApiTest />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/requisitions/:id"
                element={
                  <PrivateRoute>
                    <Layout>
                      <RequisitionDetail />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/requisitions/:id/edit"
                element={
                  <PrivateRoute>
                    <Layout>
                      <RequisitionForm />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/requisitions/:id/prepare-model22"
                element={
                  <PrivateRoute>
                    <Layout>
                      <Model22Preparation />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/requisitions/:id/model22-report"
                element={
                  <PrivateRoute>
                    <Layout>
                      <Model22Report />
                    </Layout>
                  </PrivateRoute>
                }
              />

              {/* Serial Voucher Routes */}
              <Route
                path="/serial-voucher-categories"
                element={
                  <PrivateRoute>
                    <Layout>
                      <SerialVoucherCategoryList />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/serial-vouchers"
                element={
                  <PrivateRoute>
                    <Layout>
                      <SerialVoucherList />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/voucher-dashboard"
                element={
                  <PrivateRoute>
                    <Layout>
                      <VoucherDashboard />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/voucher-request"
                element={
                  <PrivateRoute>
                    <Layout>
                      <VoucherRequestForm />
                    </Layout>
                  </PrivateRoute>
                }
              />

              {/* Inspection Routes */}
              <Route
                path="/inspection-committees"
                element={
                  <PrivateRoute>
                    <Layout>
                      <InspectionCommitteeList />
                    </Layout>
                  </PrivateRoute>
                }
              />

              {/* Entry Request Routes */}
              <Route
                path="/entry-requests"
                element={
                  <PrivateRoute>
                    <Layout>
                      <EntryRequestsList />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/entry-requests/new"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ItemEntryRequestForm />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/entry-requests/:id"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ItemEntryRequestDetail />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/entry-requests/:id/edit"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ItemEntryRequestForm />
                    </Layout>
                  </PrivateRoute>
                }
              />

              {/* Procurement Routes */}
              <Route
                path="/procurement/item-receive"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ItemReceiveDashboard />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/procurement/entry-request/new"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ItemEntryRequestFormNew />
                    </Layout>
                  </PrivateRoute>
                }
              />
              <Route
                path="/procurement/entry-request/edit/:id"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ItemEntryRequestFormNew />
                    </Layout>
                  </PrivateRoute>
                }
              />

              {/* Item Receive Routes */}
              <Route
                path="/item-receive/*"
                element={
                  <PrivateRoute>
                    <Layout>
                      <ItemReceiveRoutes />
                    </Layout>
                  </PrivateRoute>
                }
              />

              {/* Dashboard Route */}
              <Route
                path="/dashboard"
                element={
                  <PrivateRoute>
                    <Layout>
                      <Dashboard />
                    </Layout>
                  </PrivateRoute>
                }
              />

              <Route path="/" element={<Navigate to="/dashboard" replace />} />
              <Route path="*" element={<Navigate to="/dashboard" replace />} />
            </Routes>
          </Router>
        </LocalizationProvider>
      </SnackbarProvider>
      </Box>
    </ThemeProvider>
  );
}

export default App;



