import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Chip,
  useTheme,
  alpha,
  Stack,
} from '@mui/material';
import {
  Inventory as InventoryIcon,
  Business as BusinessIcon,
  TrendingUp as TrendingUpIcon,
  Security as SecurityIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Language as LanguageIcon,
} from '@mui/icons-material';
import { getMainOrganization } from '../services/organizations';

const DashboardBanner = () => {
  const theme = useTheme();
  const [organization, setOrganization] = useState(null);
  const [loading, setLoading] = useState(true);

  // Get user data from localStorage instead of Redux to avoid context issues
  const getUserData = () => {
    try {
      const userData = localStorage.getItem('user');
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Error parsing user data:', error);
      return null;
    }
  };

  const user = getUserData();

  // Fetch organization data
  useEffect(() => {
    const fetchOrganization = async () => {
      try {
        setLoading(true);
        const orgData = await getMainOrganization();
        setOrganization(orgData);
      } catch (error) {
        console.error('Error fetching organization:', error);
        // Fallback to University of Gondar data if API fails
        setOrganization({
          name: 'University of Gondar',
          motto: 'Stock Management System',
          logo_url: '/assets/images/uog-logo.png',
          website: '',
          phone: '',
          email: ''
        });
      } finally {
        setLoading(false);
      }
    };

    fetchOrganization();
  }, []);

  const bannerStyle = {
    background: `linear-gradient(135deg,
      ${theme.palette.primary.main} 0%,
      ${theme.palette.primary.dark} 50%,
      ${theme.palette.secondary.main} 100%)`,
    color: 'white',
    borderRadius: theme.spacing(2),
    overflow: 'hidden',
    position: 'relative',
    minHeight: '200px',
    '&::before': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
    },
  };

  const logoStyle = {
    width: 'auto',
    height: 80,
    maxWidth: 120,
    borderRadius: 0, // Remove circular shape
    border: 'none', // Remove border
    backgroundColor: 'transparent', // Make background transparent
    backdropFilter: 'none',
    objectFit: 'contain', // Maintain aspect ratio
  };

  const statsCardStyle = {
    backgroundColor: alpha('#ffffff', 0.15),
    backdropFilter: 'blur(10px)',
    border: `1px solid ${alpha('#ffffff', 0.2)}`,
    borderRadius: theme.spacing(1.5),
    padding: theme.spacing(2),
    textAlign: 'center',
    transition: 'all 0.3s ease',
    '&:hover': {
      backgroundColor: alpha('#ffffff', 0.25),
      transform: 'translateY(-2px)',
    },
  };

  return (
    <Paper sx={bannerStyle} elevation={0}>
      <Box sx={{ position: 'relative', zIndex: 1, p: 4 }}>
        <Grid container spacing={3} alignItems="center">
          {/* Logo and Title Section */}
          <Grid item xs={12} md={8}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Box
                component="img"
                src={organization?.logo_url || '/assets/images/uog-logo.png'}
                alt={organization?.name || 'Organization'}
                sx={logoStyle}
                onError={(e) => {
                  // Fallback to icon if image fails to load
                  e.target.style.display = 'none';
                  e.target.nextSibling.style.display = 'block';
                }}
              />
              <BusinessIcon
                sx={{
                  fontSize: 60,
                  color: alpha('#ffffff', 0.8),
                  display: 'none' // Hidden by default, shown if image fails
                }}
              />
              <Box sx={{ ml: 3 }}>
                <Typography
                  variant="h4"
                  component="h1"
                  sx={{
                    fontWeight: 700,
                    mb: 0.5,
                    textShadow: '0 2px 4px rgba(0,0,0,0.3)',
                    fontSize: { xs: '1.5rem', md: '2.125rem' },
                  }}
                >
                  {organization?.name || 'University of Gondar'}
                </Typography>
                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: 500,
                    opacity: 0.9,
                    fontSize: { xs: '1rem', md: '1.25rem' },
                  }}
                >
                  {organization?.motto || 'Stock Management System'}
                </Typography>
                <Box sx={{ mt: 1, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  <Chip
                    label="Professional"
                    size="small"
                    sx={{
                      backgroundColor: alpha('#ffffff', 0.2),
                      color: 'white',
                      fontWeight: 500,
                    }}
                  />
                  <Chip
                    label="Secure"
                    size="small"
                    sx={{
                      backgroundColor: alpha('#ffffff', 0.2),
                      color: 'white',
                      fontWeight: 500,
                    }}
                  />
                  <Chip
                    label="Efficient"
                    size="small"
                    sx={{
                      backgroundColor: alpha('#ffffff', 0.2),
                      color: 'white',
                      fontWeight: 500,
                    }}
                  />
                </Box>
              </Box>
            </Box>

            {/* Welcome Message */}
            <Box sx={{ mt: 2 }}>
              <Typography variant="h6" sx={{ fontWeight: 500, mb: 0.5 }}>
                Welcome back, {user?.first_name || user?.username || 'User'}!
              </Typography>
              <Typography variant="body1" sx={{ opacity: 0.8, mb: 2 }}>
                Manage your inventory with confidence and precision
              </Typography>

              {/* Organization Contact Information */}
              {organization && (organization.website || organization.phone || organization.email) && (
                <Stack direction="row" spacing={3} sx={{ flexWrap: 'wrap', gap: 1 }}>
                  {organization.website && (
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <LanguageIcon sx={{ fontSize: 18, opacity: 0.8 }} />
                      <Typography
                        component="a"
                        href={organization.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        sx={{
                          color: 'white',
                          textDecoration: 'underline',
                          fontSize: '0.875rem',
                          opacity: 0.9,
                          '&:hover': { opacity: 1 }
                        }}
                      >
                        {organization.website}
                      </Typography>
                    </Box>
                  )}
                  {organization.phone && (
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <PhoneIcon sx={{ fontSize: 18, opacity: 0.8 }} />
                      <Typography sx={{ fontSize: '0.875rem', opacity: 0.9 }}>
                        {organization.phone}
                      </Typography>
                    </Box>
                  )}
                  {organization.email && (
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <EmailIcon sx={{ fontSize: 18, opacity: 0.8 }} />
                      <Typography
                        component="a"
                        href={`mailto:${organization.email}`}
                        sx={{
                          color: 'white',
                          textDecoration: 'underline',
                          fontSize: '0.875rem',
                          opacity: 0.9,
                          '&:hover': { opacity: 1 }
                        }}
                      >
                        {organization.email}
                      </Typography>
                    </Box>
                  )}
                </Stack>
              )}
            </Box>
          </Grid>

          {/* Quick Stats Section */}
          <Grid item xs={12} md={4}>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Box sx={statsCardStyle}>
                  <InventoryIcon sx={{ fontSize: 32, mb: 1, opacity: 0.9 }} />
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5 }}>
                    Active
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.8 }}>
                    Inventory
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Box sx={statsCardStyle}>
                  <SecurityIcon sx={{ fontSize: 32, mb: 1, opacity: 0.9 }} />
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5 }}>
                    Secure
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.8 }}>
                    System
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Box sx={statsCardStyle}>
                  <TrendingUpIcon sx={{ fontSize: 32, mb: 1, opacity: 0.9 }} />
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5 }}>
                    Growth
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.8 }}>
                    Tracking
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Box sx={statsCardStyle}>
                  <BusinessIcon sx={{ fontSize: 32, mb: 1, opacity: 0.9 }} />
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5 }}>
                    Enterprise
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.8 }}>
                    Grade
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Box>
    </Paper>
  );
};

export default DashboardBanner;
