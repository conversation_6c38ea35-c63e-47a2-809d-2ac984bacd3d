import React from 'react';
import { Box, Button, Typography, Paper } from '@mui/material';
import { Error as ErrorIcon } from '@mui/icons-material';

const ErrorScreen = ({ error, onRetry }) => {
  const errorMessage = error?.data?.detail || error?.message || 'An unexpected error occurred';
  
  return (
    <Paper
      elevation={3}
      sx={{
        p: 4,
        m: 2,
        maxWidth: 600,
        mx: 'auto',
        textAlign: 'center',
      }}
    >
      <ErrorIcon color="error" sx={{ fontSize: 60, mb: 2 }} />
      <Typography variant="h5" gutterBottom>
        Error
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        {errorMessage}
      </Typography>
      {onRetry && (
        <Button variant="contained" onClick={onRetry}>
          Retry
        </Button>
      )}
    </Paper>
  );
};

export default ErrorScreen;
