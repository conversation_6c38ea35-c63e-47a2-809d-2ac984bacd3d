import React, { useState, useEffect } from 'react';
import {
  Box,
  useTheme,
  CssBaseline,
  Toolbar,
  Container,
  alpha,
  AppBar,
  IconButton,
  Typography,
  Avatar,
  Menu,
  MenuItem,
  ListItemIcon,
  Divider,
  Badge,
  Tooltip,
  Button,
  useMediaQuery
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  Menu as MenuIcon,
  Notifications as NotificationsIcon,
  Person as PersonIcon,
  Logout as LogoutIcon,
  Settings as SettingsIcon,
  Dashboard as DashboardIcon,
  Business as BusinessIcon,
  Category as CategoryIcon,
  Assignment as AssignmentIcon,
  Search as SearchIcon,
  ChevronRight as ChevronRightIcon,
  Inventory as InventoryIcon,
  LocalShipping as SupplierIcon,
  Warehouse as StorageIcon,
  ViewList as ItemTypeIcon,
  Style as ItemCategoryIcon,
  Group as GroupIcon,
  Add as AddIcon,
  Receipt as ReceiptIcon,
  List as ListIcon
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { authService } from '../services/auth';
import { getMenuVisibility } from '../utils/permissions';
import api from '../utils/axios';

// Styled components for the fancy layout
const StyledAppBar = styled(AppBar)(({ theme }) => ({
  backgroundColor: 'rgba(255, 255, 255, 0.8)',
  backdropFilter: 'blur(10px)',
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)',
  color: theme.palette.text.primary,
  borderBottom: `1px solid ${alpha(theme.palette.divider, 0.08)}`,
}));

const MainContent = styled(Box)(({ theme }) => ({
  flexGrow: 1,
  minHeight: 'calc(100vh - 64px)',
  overflow: 'hidden',
  backgroundImage: `
    radial-gradient(at 90% 10%, ${alpha(theme.palette.primary.main, 0.05)} 0, transparent 50%),
    radial-gradient(at 10% 90%, ${alpha(theme.palette.secondary.main, 0.05)} 0, transparent 50%)
  `,
  padding: theme.spacing(3),
  paddingTop: theme.spacing(2),
}));

const SideNav = styled(Box)(({ theme, open }) => ({
  position: 'fixed',
  top: 64,
  left: 0,
  height: 'calc(100vh - 64px)',
  width: open ? 240 : 0,
  backgroundColor: theme.palette.background.paper,
  boxShadow: '4px 0 20px rgba(0, 0, 0, 0.05)',
  transition: theme.transitions.create('width', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.enteringScreen,
  }),
  overflow: 'hidden',
  zIndex: 1200,
  borderRight: `1px solid ${alpha(theme.palette.divider, 0.08)}`,
  backgroundImage: `linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05)),
                    radial-gradient(at top left, ${alpha(theme.palette.primary.main, 0.12)}, transparent 60%)`,
}));

// Create a styled component that doesn't pass the active prop to the DOM
const StyledNavButton = styled(Button, {
  shouldForwardProp: (prop) => prop !== 'isActive',
})(({ theme, isActive }) => ({
  justifyContent: 'flex-start',
  padding: theme.spacing(1.5, 3),
  borderRadius: 0,
  width: '100%',
  textAlign: 'left',
  textTransform: 'none',
  fontWeight: 500,
  fontSize: '0.9rem',
  color: isActive ? theme.palette.primary.main : theme.palette.text.primary,
  backgroundColor: isActive ? alpha(theme.palette.primary.main, 0.08) : 'transparent',
  borderLeft: isActive ? `4px solid ${theme.palette.primary.main}` : '4px solid transparent',
  '&:hover': {
    backgroundColor: alpha(theme.palette.primary.main, 0.05),
  },
}));

// Create a NavItem component that filters out the active prop
const NavItem = ({ active, ...props }) => {
  return <StyledNavButton isActive={active} {...props} />;
};

const ContentContainer = styled(Container)(({ theme, sidenavopen }) => ({
  transition: theme.transitions.create(['margin', 'width'], {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.enteringScreen,
  }),
  marginLeft: sidenavopen === 'true' ? '240px' : 0,
  width: sidenavopen === 'true' ? 'calc(100% - 240px)' : '100%',
  maxWidth: '100%',
  padding: theme.spacing(3),
}));

const SearchBar = styled(Box)(({ theme }) => ({
  position: 'relative',
  borderRadius: theme.shape.borderRadius * 3,
  backgroundColor: alpha(theme.palette.common.white, 0.15),
  '&:hover': {
    backgroundColor: alpha(theme.palette.common.white, 0.25),
  },
  marginRight: theme.spacing(2),
  marginLeft: 0,
  width: '100%',
  [theme.breakpoints.up('sm')]: {
    marginLeft: theme.spacing(3),
    width: 'auto',
  },
  display: 'flex',
  alignItems: 'center',
  border: `1px solid ${alpha(theme.palette.divider, 0.15)}`,
}));

const SearchIconWrapper = styled('div')(({ theme }) => ({
  padding: theme.spacing(0, 2),
  height: '100%',
  position: 'absolute',
  pointerEvents: 'none',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  color: alpha(theme.palette.text.primary, 0.5),
}));

const StyledInputBase = styled('input')(({ theme }) => ({
  color: theme.palette.text.primary,
  padding: theme.spacing(1, 1, 1, 0),
  paddingLeft: `calc(1em + ${theme.spacing(4)})`,
  transition: theme.transitions.create('width'),
  width: '100%',
  backgroundColor: 'transparent',
  border: 'none',
  outline: 'none',
  fontSize: '0.9rem',
  [theme.breakpoints.up('md')]: {
    width: '20ch',
  },
}));

const Layout = ({ children }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const [sideNavOpen, setSideNavOpen] = useState(!isMobile);
  const [anchorEl, setAnchorEl] = useState(null);
  const [notificationsAnchorEl, setNotificationsAnchorEl] = useState(null);
  const [currentUser, setCurrentUser] = useState(null);
  const [mainOrganization, setMainOrganization] = useState(null);
  const [menuVisibility, setMenuVisibility] = useState({
    dashboard: true,
    organization: false,
    classification: false,
    specification: false,
    storage: false,
    supplier: false,
    status: false,
    inventory: false,
    itemMasters: false,
    batches: false,
    items: false,
    gatePasses: false,
    requisition: false,
    requisitionStatuses: false,
    reports: false,
    receiving: true, // Enable by default for better visibility
    itemReceive: true, // Enable by default for better visibility
  });

  const handleProfileMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleNotificationsMenuOpen = (event) => {
    setNotificationsAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setNotificationsAnchorEl(null);
  };

  const handleLogout = () => {
    localStorage.removeItem('token');
    navigate('/login');
    handleMenuClose();
  };

  const toggleSideNav = () => {
    setSideNavOpen(!sideNavOpen);
  };

  // Fetch main organization
  const fetchMainOrganization = async () => {
    try {
      // First try to get the main organization
      try {
        const mainResponse = await api.get('/organizations/main/');
        if (mainResponse.data) {
          setMainOrganization(mainResponse.data);
          return; // Exit if we found the main organization
        }
      } catch (mainError) {
        console.log('No main organization set, falling back to first organization');
      }

      // Fallback to getting the first organization if no main is set
      const response = await api.get('/organizations/');
      if (Array.isArray(response.data) && response.data.length > 0) {
        setMainOrganization(response.data[0]);
      } else if (response.data && response.data.results && Array.isArray(response.data.results) && response.data.results.length > 0) {
        setMainOrganization(response.data.results[0]);
      } else {
        console.error('No organizations found');
      }
    } catch (error) {
      console.error('Error fetching organization:', error);
    }
  };

  // Fetch current user and update menu visibility
  useEffect(() => {
    const fetchCurrentUser = async () => {
      try {
        const user = await authService.getCurrentUser();
        setCurrentUser(user);
        setMenuVisibility(getMenuVisibility(user));
      } catch (error) {
        console.error('Error fetching current user:', error);
      }
    };

    fetchCurrentUser();
    fetchMainOrganization();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // Group menu items by category
  const dashboardItems = [
    { text: 'Dashboard', icon: <DashboardIcon />, path: '/dashboard' },
  ];

  const organizationItems = [
    { text: 'Organizations', icon: <BusinessIcon />, path: '/organizations' },
    { text: 'Organization Types', icon: <BusinessIcon />, path: '/organization-types' },
    { text: 'Offices', icon: <BusinessIcon />, path: '/offices' },
  ];

  const itemReceiveItems = [
    { text: 'Item Receive Dashboard', icon: <DashboardIcon />, path: '/procurement/item-receive' },
  ];

  const receivingItems = [
    { text: 'Receiving Dashboard', icon: <DashboardIcon />, path: '/receiving-dashboard' },
    { text: 'Delivery Receipts', icon: <ReceiptIcon />, path: '/delivery-receipts' },
    { text: 'Create Delivery Receipt', icon: <AddIcon />, path: '/delivery-receipt-form' },
    { text: 'Inspections', icon: <SearchIcon />, path: '/inspections' },
    { text: 'Model 19 Receipts', icon: <AssignmentIcon />, path: '/model19-receipts' },
    { text: 'Create Model 19', icon: <AddIcon />, path: '/model19-form' },
    { text: 'Item Received Vouchers', icon: <ReceiptIcon />, path: '/serial-vouchers' },
  ];

  const classificationItems = [
    { text: 'Main Classifications', icon: <CategoryIcon />, path: '/main-classifications' },
    { text: 'Sub Classifications', icon: <CategoryIcon />, path: '/sub-classifications' },
  ];

  const specificationItems = [
    { text: 'Specifications Dashboard', icon: <DashboardIcon />, path: '/specifications' },
  ];

  const storageItems = [
    { text: 'Storage Dashboard', icon: <DashboardIcon />, path: '/storage-dashboard' },
  ];

  const supplierItems = [
    { text: 'Suppliers', icon: <SupplierIcon />, path: '/suppliers' },
  ];

  const statusItems = [
    { text: 'Status Dashboard', icon: <DashboardIcon />, path: '/status-dashboard' },
  ];

  const gatePassItems = [
    { text: 'Gate Passes', icon: <AssignmentIcon />, path: '/gate-passes' },
  ];

  // Reports menu removed as requested

  const requisitionItems = [
    { text: 'Requisition Dashboard', icon: <DashboardIcon />, path: '/requisition-dashboard' },
  ];

  const inventoryItems = [
    { text: 'Inventory Dashboard', icon: <DashboardIcon />, path: '/inventory-dashboard' },
  ];

  const isMenuOpen = Boolean(anchorEl);
  const isNotificationsOpen = Boolean(notificationsAnchorEl);

  const renderMenu = (
    <Menu
      anchorEl={anchorEl}
      id="profile-menu"
      keepMounted
      open={isMenuOpen}
      onClose={handleMenuClose}
      sx={{
        '& .MuiPaper-root': {
          borderRadius: 2,
          minWidth: 180,
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
          mt: 1.5,
        },
      }}
      transformOrigin={{ horizontal: 'right', vertical: 'top' }}
      anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
    >
      <MenuItem onClick={handleMenuClose}>
        <ListItemIcon>
          <PersonIcon fontSize="small" />
        </ListItemIcon>
        Profile
      </MenuItem>
      <MenuItem onClick={handleMenuClose}>
        <ListItemIcon>
          <SettingsIcon fontSize="small" />
        </ListItemIcon>
        Settings
      </MenuItem>
      <Divider />
      <MenuItem onClick={handleLogout}>
        <ListItemIcon>
          <LogoutIcon fontSize="small" color="error" />
        </ListItemIcon>
        <Typography color="error">Logout</Typography>
      </MenuItem>
    </Menu>
  );

  const renderNotificationsMenu = (
    <Menu
      anchorEl={notificationsAnchorEl}
      id="notifications-menu"
      keepMounted
      open={isNotificationsOpen}
      onClose={handleMenuClose}
      sx={{
        '& .MuiPaper-root': {
          borderRadius: 2,
          minWidth: 280,
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
          mt: 1.5,
        },
      }}
      transformOrigin={{ horizontal: 'right', vertical: 'top' }}
      anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
    >
      <Box sx={{ p: 2 }}>
        <Typography variant="h6" fontWeight={600}>
          Notifications
        </Typography>
      </Box>
      <Divider />
      <MenuItem onClick={handleMenuClose} sx={{ py: 1.5 }}>
        <Box sx={{ display: 'flex', flexDirection: 'column' }}>
          <Typography variant="subtitle2" fontWeight={600}>
            New gate pass request
          </Typography>
          <Typography variant="body2" color="text.secondary">
            A new gate pass has been requested
          </Typography>
        </Box>
      </MenuItem>
      <MenuItem onClick={handleMenuClose} sx={{ py: 1.5 }}>
        <Box sx={{ display: 'flex', flexDirection: 'column' }}>
          <Typography variant="subtitle2" fontWeight={600}>
            Organization updated
          </Typography>
          <Typography variant="body2" color="text.secondary">
            An organization has been updated
          </Typography>
        </Box>
      </MenuItem>
      <Divider />
      <Box sx={{ p: 1, display: 'flex', justifyContent: 'center' }}>
        <Button
          size="small"
          endIcon={<ChevronRightIcon />}
          sx={{ textTransform: 'none', fontWeight: 500 }}
        >
          View all notifications
        </Button>
      </Box>
    </Menu>
  );

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh', overflow: 'hidden' }}>
      <CssBaseline />

      {/* Top App Bar */}
      <StyledAppBar position="fixed">
        <Toolbar>
          <IconButton
            edge="start"
            color="inherit"
            aria-label="menu"
            onClick={toggleSideNav}
            sx={{ mr: 2 }}
          >
            <MenuIcon />
          </IconButton>

          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {mainOrganization && mainOrganization.logo_url ? (
              <Box
                component="img"
                src={mainOrganization.logo_url}
                alt={mainOrganization.name}
                sx={{
                  height: 32,
                  maxWidth: 120,
                  objectFit: 'contain',
                  mr: 1
                }}
              />
            ) : (
              <Avatar
                sx={{
                  width: 32,
                  height: 32,
                  mr: 1,
                  bgcolor: theme.palette.primary.main,
                }}
              >
                {mainOrganization ? mainOrganization.name.charAt(0) : 'A'}
              </Avatar>
            )}
            <Typography
              variant="h6"
              noWrap
              component="div"
              sx={{
                display: { xs: 'none', sm: 'block' },
                fontWeight: 700,
                background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
            >
              {mainOrganization ? mainOrganization.name : 'AssetManager'}
            </Typography>
          </Box>

          <SearchBar>
            <SearchIconWrapper>
              <SearchIcon />
            </SearchIconWrapper>
            <StyledInputBase
              placeholder="Search…"
              inputProps={{ 'aria-label': 'search' }}
            />
          </SearchBar>

          <Box sx={{ flexGrow: 1 }} />

          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Tooltip title="Notifications">
              <IconButton
                size="large"
                aria-label="show notifications"
                color="inherit"
                onClick={handleNotificationsMenuOpen}
                sx={{
                  mr: 1,
                  '&:hover': {
                    backgroundColor: alpha(theme.palette.primary.main, 0.08),
                  },
                }}
              >
                <Badge badgeContent={3} color="error">
                  <NotificationsIcon />
                </Badge>
              </IconButton>
            </Tooltip>

            <Tooltip title="Account">
              <IconButton
                size="large"
                edge="end"
                aria-label="account of current user"
                aria-haspopup="true"
                onClick={handleProfileMenuOpen}
                color="inherit"
                sx={{
                  '&:hover': {
                    backgroundColor: alpha(theme.palette.primary.main, 0.08),
                  },
                }}
              >
                <Avatar
                  sx={{
                    width: 32,
                    height: 32,
                    bgcolor: alpha(theme.palette.primary.main, 0.1),
                    color: theme.palette.primary.main,
                  }}
                >
                  <PersonIcon fontSize="small" />
                </Avatar>
              </IconButton>
            </Tooltip>
          </Box>
        </Toolbar>
      </StyledAppBar>

      {/* Side Navigation */}
      <SideNav open={sideNavOpen}>
        <Box sx={{ p: 2, pt: 3, overflowY: 'auto', height: '100%' }}>
          {/* Dashboard Section */}
          {menuVisibility.dashboard && (
            <>
              <Typography variant="subtitle2" fontWeight={700} sx={{ mb: 1, pl: 1, color: 'text.secondary', fontSize: '0.75rem', letterSpacing: '0.1em' }}>
                DASHBOARD
              </Typography>
              {dashboardItems.map((item) => (
                <NavItem
                  key={item.text}
                  startIcon={item.icon}
                  active={location.pathname === item.path}
                  onClick={() => navigate(item.path)}
                >
                  {item.text}
                </NavItem>
              ))}
            </>
          )}

          {/* Item Receive Section */}
          {menuVisibility.itemReceive && (
            <>
              <Typography variant="subtitle2" fontWeight={700} sx={{ mt: 3, mb: 1, pl: 1, color: 'text.secondary', fontSize: '0.75rem', letterSpacing: '0.1em' }}>
                ITEM RECEIVE
              </Typography>
              {itemReceiveItems.map((item) => (
                <NavItem
                  key={item.text}
                  startIcon={item.icon}
                  active={location.pathname.startsWith(item.path)}
                  onClick={() => navigate(item.path)}
                >
                  {item.text}
                </NavItem>
              ))}
            </>
          )}

          {/* Receiving & Inspection Section */}
          {menuVisibility.receiving && (
            <>
              <Typography variant="subtitle2" fontWeight={700} sx={{ mt: 3, mb: 1, pl: 1, color: 'text.secondary', fontSize: '0.75rem', letterSpacing: '0.1em' }}>
                RECEIVING & INSPECTION
              </Typography>
              {receivingItems.map((item) => (
                <NavItem
                  key={item.text}
                  startIcon={item.icon}
                  active={location.pathname === item.path}
                  onClick={() => navigate(item.path)}
                >
                  {item.text}
                </NavItem>
              ))}
            </>
          )}

          {/* Organization Section */}
          {menuVisibility.organization && (
            <>
              <Typography variant="subtitle2" fontWeight={700} sx={{ mt: 3, mb: 1, pl: 1, color: 'text.secondary', fontSize: '0.75rem', letterSpacing: '0.1em' }}>
                ORGANIZATION
              </Typography>
              {organizationItems.map((item) => (
                <NavItem
                  key={item.text}
                  startIcon={item.icon}
                  active={location.pathname === item.path}
                  onClick={() => navigate(item.path)}
                >
                  {item.text}
                </NavItem>
              ))}
            </>
          )}

          {/* Classification Section */}
          {menuVisibility.classification && (
            <>
              <Typography variant="subtitle2" fontWeight={700} sx={{ mt: 3, mb: 1, pl: 1, color: 'text.secondary', fontSize: '0.75rem', letterSpacing: '0.1em' }}>
                CLASSIFICATION
              </Typography>
              {classificationItems.map((item) => (
                <NavItem
                  key={item.text}
                  startIcon={item.icon}
                  active={location.pathname === item.path}
                  onClick={() => navigate(item.path)}
                >
                  {item.text}
                </NavItem>
              ))}
            </>
          )}

          {/* Specification Section */}
          {menuVisibility.specification && (
            <>
              <Typography variant="subtitle2" fontWeight={700} sx={{ mt: 3, mb: 1, pl: 1, color: 'text.secondary', fontSize: '0.75rem', letterSpacing: '0.1em' }}>
                SPECIFICATIONS
              </Typography>
              {specificationItems.map((item) => (
                <NavItem
                  key={item.text}
                  startIcon={item.icon}
                  active={location.pathname === item.path}
                  onClick={() => navigate(item.path)}
                >
                  {item.text}
                </NavItem>
              ))}
            </>
          )}

          {/* Storage Section */}
          {menuVisibility.storage && (
            <>
              <Typography variant="subtitle2" fontWeight={700} sx={{ mt: 3, mb: 1, pl: 1, color: 'text.secondary', fontSize: '0.75rem', letterSpacing: '0.1em' }}>
                STORAGE
              </Typography>
              {storageItems.map((item) => (
                <NavItem
                  key={item.text}
                  startIcon={item.icon}
                  active={location.pathname === item.path}
                  onClick={() => navigate(item.path)}
                >
                  {item.text}
                </NavItem>
              ))}
            </>
          )}

          {/* Supplier Section */}
          {menuVisibility.supplier && (
            <>
              <Typography variant="subtitle2" fontWeight={700} sx={{ mt: 3, mb: 1, pl: 1, color: 'text.secondary', fontSize: '0.75rem', letterSpacing: '0.1em' }}>
                SUPPLIERS
              </Typography>
              {supplierItems.map((item) => (
                <NavItem
                  key={item.text}
                  startIcon={item.icon}
                  active={location.pathname === item.path}
                  onClick={() => navigate(item.path)}
                >
                  {item.text}
                </NavItem>
              ))}
            </>
          )}

          {/* Status Section */}
          {menuVisibility.status && (
            <>
              <Typography variant="subtitle2" fontWeight={700} sx={{ mt: 3, mb: 1, pl: 1, color: 'text.secondary', fontSize: '0.75rem', letterSpacing: '0.1em' }}>
                STATUS
              </Typography>
              {statusItems.map((item) => (
                <NavItem
                  key={item.text}
                  startIcon={item.icon}
                  active={location.pathname === item.path}
                  onClick={() => navigate(item.path)}
                >
                  {item.text}
                </NavItem>
              ))}
            </>
          )}

          {/* Inventory Section */}
          {menuVisibility.inventory && (
            <>
              <Typography variant="subtitle2" fontWeight={700} sx={{ mt: 3, mb: 1, pl: 1, color: 'text.secondary', fontSize: '0.75rem', letterSpacing: '0.1em' }}>
                INVENTORY
              </Typography>
              {inventoryItems.map((item) => {
                // Check specific permissions for inventory items
                if ((item.text === 'Item Masters' && !menuVisibility.itemMasters) ||
                    (item.text === 'Batches' && !menuVisibility.batches) ||
                    (item.text === 'Items' && !menuVisibility.items)) {
                  return null;
                }
                return (
                  <NavItem
                    key={item.text}
                    startIcon={item.icon}
                    active={location.pathname === item.path}
                    onClick={() => navigate(item.path)}
                  >
                    {item.text}
                  </NavItem>
                );
              })}
            </>
          )}

          {/* Gate Pass Section */}
          {menuVisibility.gatePasses && (
            <>
              <Typography variant="subtitle2" fontWeight={700} sx={{ mt: 3, mb: 1, pl: 1, color: 'text.secondary', fontSize: '0.75rem', letterSpacing: '0.1em' }}>
                GATE PASSES
              </Typography>
              {gatePassItems.map((item) => (
                <NavItem
                  key={item.text}
                  startIcon={item.icon}
                  active={location.pathname === item.path}
                  onClick={() => navigate(item.path)}
                >
                  {item.text}
                </NavItem>
              ))}
            </>
          )}

          {/* Requisition Section */}
          {menuVisibility.requisition && (
            <>
              <Typography variant="subtitle2" fontWeight={700} sx={{ mt: 3, mb: 1, pl: 1, color: 'text.secondary', fontSize: '0.75rem', letterSpacing: '0.1em' }}>
                REQUISITIONS
              </Typography>
              {requisitionItems.map((item) => {
                // Check specific permissions for requisition items
                if (item.text === 'Requisition Statuses' && !menuVisibility.requisitionStatuses) {
                  return null;
                }
                return (
                  <NavItem
                    key={item.text}
                    startIcon={item.icon}
                    active={location.pathname === item.path}
                    onClick={() => navigate(item.path)}
                  >
                    {item.text}
                  </NavItem>
                );
              })}
            </>
          )}

          {/* Reports Section removed as requested */}
        </Box>
      </SideNav>

      {/* Main Content */}
      <MainContent>
        <Toolbar /> {/* Spacer for AppBar */}
        <ContentContainer sidenavopen={sideNavOpen.toString()}>
          {children}
        </ContentContainer>
      </MainContent>

      {renderMenu}
      {renderNotificationsMenu}
    </Box>
  );
};

export default Layout;

