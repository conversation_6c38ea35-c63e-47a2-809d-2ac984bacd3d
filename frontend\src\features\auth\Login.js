import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import {
  Box,
  Paper,
  TextField,
  Button,
  Typography,
  Grid,
  Avatar,
  Chip,
  CircularProgress,
  useTheme,
  alpha,
  InputAdornment,
  IconButton,
} from '@mui/material';
import {
  Login as LoginIcon,
  Person as PersonIcon,
  Lock as LockIcon,
  Visibility,
  VisibilityOff,
  Security as SecurityIcon,
  School as SchoolIcon,
  Inventory as InventoryIcon,
  TrendingUp as TrendingUpIcon,
} from '@mui/icons-material';
import { authService } from '../../services/auth';
import { useSnackbar } from 'notistack';

const validationSchema = Yup.object({
  username: Yup.string().required('Username is required'),
  password: Yup.string().required('Password is required'),
});

const Login = () => {
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const theme = useTheme();

  const formik = useFormik({
    initialValues: {
      username: '',
      password: '',
    },
    validationSchema,
    onSubmit: async (values) => {
      setLoading(true);
      try {
        await authService.login(values);
        enqueueSnackbar('Login successful', { variant: 'success' });
        navigate('/dashboard');
      } catch (error) {
        console.error('Login error:', error);
        enqueueSnackbar(typeof error === 'string' ? error : 'Login failed. Please check your credentials.', { variant: 'error' });
      } finally {
        setLoading(false);
      }
    },
  });

  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <Box sx={{
      minHeight: '100vh',
      background: `linear-gradient(135deg,
        ${theme.palette.primary.main} 0%,
        ${theme.palette.primary.dark} 50%,
        ${theme.palette.secondary.main} 100%)`,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      p: 2,
    }}>
      <Paper sx={{
        width: '100%',
        maxWidth: 1200,
        minHeight: 600,
        borderRadius: 4,
        overflow: 'hidden',
        boxShadow: '0 20px 60px rgba(0,0,0,0.3)',
        background: 'white',
      }}>
        <Grid container sx={{ height: '100%', minHeight: 600 }}>
          {/* Left Column - Branding */}
          <Grid item xs={12} md={6} sx={{
            background: `linear-gradient(135deg,
              ${theme.palette.primary.main} 0%,
              ${theme.palette.primary.dark} 50%,
              ${theme.palette.secondary.main} 100%)`,
            color: 'white',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            p: 4,
            position: 'relative',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            },
          }}>
            <Box sx={{ position: 'relative', zIndex: 1, textAlign: 'center' }}>
              {/* University Logo */}
              <Avatar
                src="/assets/images/uog-logo.png"
                alt="University of Gondar"
                sx={{
                  width: 120,
                  height: 120,
                  mb: 3,
                  mx: 'auto',
                  border: `4px solid ${alpha('#ffffff', 0.3)}`,
                  backgroundColor: alpha('#ffffff', 0.1),
                  backdropFilter: 'blur(10px)',
                }}
              >
                <SchoolIcon sx={{ fontSize: 60 }} />
              </Avatar>

              {/* University Title */}
              <Typography variant="h3" sx={{
                fontWeight: 700,
                mb: 1,
                textShadow: '0 2px 4px rgba(0,0,0,0.3)',
                fontSize: { xs: '1.8rem', md: '3rem' },
              }}>
                University of Gondar
              </Typography>

              <Typography variant="h5" sx={{
                fontWeight: 500,
                mb: 3,
                opacity: 0.9,
                fontSize: { xs: '1.1rem', md: '1.5rem' },
              }}>
                Stock Management System
              </Typography>

              {/* Feature Highlights */}
              <Box sx={{ mt: 4, display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 2 }}>
                  <SecurityIcon sx={{ fontSize: 24 }} />
                  <Typography variant="body1">Secure & Professional</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 2 }}>
                  <InventoryIcon sx={{ fontSize: 24 }} />
                  <Typography variant="body1">Complete Inventory Control</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 2 }}>
                  <TrendingUpIcon sx={{ fontSize: 24 }} />
                  <Typography variant="body1">Real-time Analytics</Typography>
                </Box>
              </Box>

              {/* Status Chips */}
              <Box sx={{ mt: 4, display: 'flex', flexWrap: 'wrap', gap: 1, justifyContent: 'center' }}>
                <Chip
                  label="Enterprise Grade"
                  sx={{
                    backgroundColor: alpha('#ffffff', 0.2),
                    color: 'white',
                    fontWeight: 500,
                  }}
                />
                <Chip
                  label="University System"
                  sx={{
                    backgroundColor: alpha('#ffffff', 0.2),
                    color: 'white',
                    fontWeight: 500,
                  }}
                />
                <Chip
                  label="Trusted Platform"
                  sx={{
                    backgroundColor: alpha('#ffffff', 0.2),
                    color: 'white',
                    fontWeight: 500,
                  }}
                />
              </Box>
            </Box>
          </Grid>

          {/* Right Column - Login Form */}
          <Grid item xs={12} md={6} sx={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            p: { xs: 3, md: 6 },
          }}>
            <Box sx={{ maxWidth: 400, mx: 'auto', width: '100%' }}>
              {/* Login Header */}
              <Box sx={{ textAlign: 'center', mb: 4 }}>
                <LoginIcon color="primary" sx={{
                  fontSize: 48,
                  mb: 2,
                  filter: 'drop-shadow(0 4px 6px rgba(0,0,0,0.1))'
                }} />
                <Typography component="h1" variant="h4" sx={{
                  fontWeight: 700,
                  letterSpacing: '-0.025em',
                  color: theme.palette.text.primary,
                  mb: 1,
                }}>
                  Welcome Back
                </Typography>
                <Typography variant="subtitle1" color="text.secondary">
                  Sign in to access your account
                </Typography>
              </Box>
          <form onSubmit={formik.handleSubmit}>
                <TextField
                  fullWidth
                  margin="normal"
                  name="username"
                  label="Username"
                  variant="outlined"
                  value={formik.values.username}
                  onChange={formik.handleChange}
                  error={formik.touched.username && Boolean(formik.errors.username)}
                  helperText={formik.touched.username && formik.errors.username}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <PersonIcon color="action" />
                      </InputAdornment>
                    ),
                    sx: {
                      borderRadius: 2,
                      '& .MuiOutlinedInput-root': {
                        '&:hover fieldset': {
                          borderColor: theme.palette.primary.main,
                        },
                      },
                    }
                  }}
                  sx={{ mb: 2 }}
                />

                <TextField
                  fullWidth
                  margin="normal"
                  name="password"
                  label="Password"
                  type={showPassword ? 'text' : 'password'}
                  variant="outlined"
                  value={formik.values.password}
                  onChange={formik.handleChange}
                  error={formik.touched.password && Boolean(formik.errors.password)}
                  helperText={formik.touched.password && formik.errors.password}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <LockIcon color="action" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={handleTogglePasswordVisibility}
                          edge="end"
                          aria-label="toggle password visibility"
                        >
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                    sx: {
                      borderRadius: 2,
                      '& .MuiOutlinedInput-root': {
                        '&:hover fieldset': {
                          borderColor: theme.palette.primary.main,
                        },
                      },
                    }
                  }}
                  sx={{ mb: 3 }}
                />
                <Button
                  fullWidth
                  type="submit"
                  variant="contained"
                  size="large"
                  disabled={loading}
                  sx={{
                    mt: 3,
                    mb: 2,
                    py: 1.5,
                    borderRadius: 2,
                    fontWeight: 700,
                    fontSize: '1.1rem',
                    textTransform: 'none',
                    boxShadow: `0 8px 16px ${alpha(theme.palette.primary.main, 0.25)}`,
                    background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
                    '&:hover': {
                      boxShadow: `0 12px 20px ${alpha(theme.palette.primary.main, 0.35)}`,
                      transform: 'translateY(-2px)',
                      background: `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, ${theme.palette.primary.main} 100%)`,
                    },
                    transition: 'all 0.3s ease-in-out'
                  }}
                >
                  {loading ? <CircularProgress size={24} color="inherit" /> : 'Sign In to System'}
                </Button>

                <Typography variant="body2" color="text.secondary" align="center" sx={{ mt: 3 }}>
                  University of Gondar Stock Management System
                </Typography>
                <Typography variant="caption" color="text.secondary" align="center" sx={{ display: 'block', mt: 1 }}>
                  Secure • Professional • Reliable
                </Typography>
              </form>
            </Box>
          </Grid>
        </Grid>
      </Paper>
    </Box>
  );
};

export default Login;