import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Avatar,
  Divider,
  Chip,
  Stack,
  Button,
  CircularProgress,
  useTheme,
  alpha,
  LinearProgress,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import DashboardBanner from '../../components/DashboardBanner';

import {
  Business as BusinessIcon,
  Category as CategoryIcon,
  LocationOn as LocationIcon,
  Assignment as AssignmentIcon,
  Inventory as InventoryIcon,
  People as PeopleIcon,
  Store as StoreIcon,
  Dashboard as DashboardIcon,
  MoreVert as MoreVertIcon,
  Refresh as RefreshIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  Settings as SettingsIcon,
  <PERSON><PERSON><PERSON> as BarChartIcon,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Timeline as TimelineIcon,
  CalendarToday as CalendarTodayIcon,
  Language as LanguageIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { getRequisitions } from '../../services/requisitions';
import { authService } from '../../services/auth';
import api from '../../utils/axios';
import { useSnackbar } from 'notistack';

const Dashboard = () => {
  const [organization, setOrganization] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [menuAnchorEl, setMenuAnchorEl] = useState(null);
  const [currentUser, setCurrentUser] = useState(null);
  const [userRequisitions, setUserRequisitions] = useState([]);
  const [stats, setStats] = useState({
    organizations: 0,
    offices: 0,
    items: 0,
    gatePasses: 0,
    activeGatePasses: 0,
    overdueGatePasses: 0,
    pendingRequisitions: 0,
    approvedRequisitions: 0,
    rejectedRequisitions: 0,
  });
  const { enqueueSnackbar } = useSnackbar();
  const navigate = useNavigate();
  const theme = useTheme();



  const handleMenuOpen = (event) => {
    setMenuAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  const refreshData = async () => {
    setRefreshing(true);
    try {
      await fetchOrganization();
      await fetchStats();
      await fetchUserRequisitions();
      enqueueSnackbar('Dashboard data refreshed', { variant: 'success' });
    } catch (error) {
      console.error('Error refreshing data:', error);
      enqueueSnackbar('Failed to refresh data', { variant: 'error' });
    } finally {
      setRefreshing(false);
    }
  };

  const fetchUserRequisitions = async () => {
    try {
      // Get current user if not already loaded
      if (!currentUser) {
        const user = await authService.getCurrentUser();
        setCurrentUser(user);
      }

      // Fetch user's requisitions
      const response = await getRequisitions();
      if (response && response.results) {
        setUserRequisitions(response.results);

        // Update requisition stats
        const pendingCount = response.results.filter(req =>
          req.status_name?.toLowerCase().includes('pending')).length;

        const approvedCount = response.results.filter(req =>
          req.status_name?.toLowerCase() === 'approved' ||
          req.status_name?.toLowerCase() === 'handed over').length;

        const rejectedCount = response.results.filter(req =>
          req.status_name?.toLowerCase() === 'rejected').length;

        setStats(prev => ({
          ...prev,
          pendingRequisitions: pendingCount,
          approvedRequisitions: approvedCount,
          rejectedRequisitions: rejectedCount
        }));
      }
    } catch (error) {
      console.error('Error fetching user requisitions:', error);
    }
  };

  const fetchOrganization = async () => {
    try {
      // First try to get the main organization
      try {
        const mainResponse = await api.get('/organizations/main/');
        if (mainResponse.data) {
          setOrganization(mainResponse.data);
          return; // Exit if we found the main organization
        }
      } catch (mainError) {
        console.log('No main organization set, falling back to first organization');
      }

      // Fallback to getting the first organization if no main is set
      const response = await api.get('/organizations/');
      if (Array.isArray(response.data) && response.data.length > 0) {
        setOrganization(response.data[0]);
      } else if (response.data && response.data.results && Array.isArray(response.data.results) && response.data.results.length > 0) {
        setOrganization(response.data.results[0]);
      } else {
        console.error('No organizations found');
      }
    } catch (error) {
      console.error('Error fetching organization:', error);
      throw error;
    }
  };

  const fetchStats = async () => {
    try {
      // Fetch counts for various entities
      const [orgsResponse, officesResponse, gatePasses, activeGatePasses, overdueGatePasses] = await Promise.all([
        api.get('/organizations/'),
        api.get('/offices/'),
        api.get('/gate-passes/'),
        api.get('/gate-passes/active/'),
        api.get('/gate-passes/overdue/'),
      ]);

      setStats({
        organizations: Array.isArray(orgsResponse.data) ? orgsResponse.data.length : 0,
        offices: Array.isArray(officesResponse.data) ? officesResponse.data.length : 0,
        gatePasses: Array.isArray(gatePasses.data) ? gatePasses.data.length : 0,
        activeGatePasses: Array.isArray(activeGatePasses.data) ? activeGatePasses.data.length : 0,
        overdueGatePasses: Array.isArray(overdueGatePasses.data) ? overdueGatePasses.data.length : 0,
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
      throw error;
    }
  };

  useEffect(() => {
    const loadData = async () => {
      try {
        await fetchOrganization();
        await fetchStats();
        await fetchUserRequisitions();
      } catch (error) {
        enqueueSnackbar('Failed to fetch dashboard information', { variant: 'error' });
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [enqueueSnackbar]);

  const StatCard = ({ title, value, icon, color, onClick, trend, trendValue, subtitle }) => (
    <Card
      sx={{
        height: '100%',
        cursor: onClick ? 'pointer' : 'default',
        transition: 'all 0.3s ease',
        borderRadius: 3,
        overflow: 'hidden',
        position: 'relative',
        border: '1px solid rgba(0, 0, 0, 0.05)',
        background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.6) 100%)',
        backdropFilter: 'blur(10px)',
        '&:hover': onClick ? {
          transform: 'translateY(-5px)',
          boxShadow: `0 15px 30px ${alpha(theme.palette.common.black, 0.1)}`,
        } : {},
        '&::after': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '4px',
          background: `linear-gradient(90deg, ${color} 0%, ${alpha(color, 0.4)} 100%)`,
        }
      }}
      onClick={onClick}
      elevation={0}
    >
        <CardContent sx={{ p: { xs: 2, sm: 3 }, overflow: 'hidden' }}>
          <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
            <Avatar
              sx={{
                bgcolor: alpha(color, 0.1),
                color: color,
                width: 56,
                height: 56,
                borderRadius: 2,
                boxShadow: `0 4px 12px ${alpha(color, 0.2)}`,
                '& .MuiSvgIcon-root': {
                  fontSize: '1.75rem',
                },
              }}
            >
              {icon}
            </Avatar>
            {trend && (
              <Chip
                icon={trend === 'up' ? <TrendingUpIcon /> : <TrendingDownIcon />}
                label={`${trendValue}%`}
                size="small"
                color={trend === 'up' ? 'success' : 'error'}
                variant="filled"
                sx={{
                  fontWeight: 'bold',
                  borderRadius: '20px',
                  '& .MuiChip-icon': {
                    fontSize: '1rem',
                  }
                }}
              />
            )}
          </Box>

          <Typography variant="h3" component="div" sx={{
            fontWeight: 800,
            mb: 0.5,
            background: `linear-gradient(90deg, ${color} 0%, ${alpha(color, 0.7)} 100%)`,
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            letterSpacing: '-0.025em',
            fontSize: { xs: '1.75rem', sm: '2.25rem', md: '2.5rem' },
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
          }}>
            {value}
          </Typography>

          <Typography variant="subtitle1" sx={{
            fontWeight: 600,
            color: theme.palette.text.primary,
            mb: 0.5,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
          }}>
            {title}
          </Typography>

          {subtitle && (
            <Typography variant="body2" sx={{
              color: theme.palette.text.secondary,
              display: 'block',
              fontWeight: 500,
              opacity: 0.8,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}>
              {subtitle}
            </Typography>
          )}
        </CardContent>
      </Card>
  );

  if (loading) {
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress size={60} thickness={4} sx={{ mb: 3 }} />
        <Typography variant="h6" color="text.secondary">
          Loading dashboard...
        </Typography>
      </Box>
    );
  }

  // Dashboard menu
  const dashboardMenu = (
    <Menu
      anchorEl={menuAnchorEl}
      open={Boolean(menuAnchorEl)}
      onClose={handleMenuClose}
      PaperProps={{
        elevation: 3,
        sx: {
          overflow: 'visible',
          filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.1))',
          mt: 1.5,
          '&:before': {
            content: '""',
            display: 'block',
            position: 'absolute',
            top: 0,
            right: 14,
            width: 10,
            height: 10,
            bgcolor: 'background.paper',
            transform: 'translateY(-50%) rotate(45deg)',
            zIndex: 0,
          },
        },
      }}
    >
      <MenuItem onClick={refreshData}>
        <ListItemIcon>
          <RefreshIcon fontSize="small" />
        </ListItemIcon>
        <ListItemText>Refresh Data</ListItemText>
      </MenuItem>
      <MenuItem onClick={() => navigate('/organizations')}>
        <ListItemIcon>
          <BusinessIcon fontSize="small" />
        </ListItemIcon>
        <ListItemText>Manage Organizations</ListItemText>
      </MenuItem>
      <MenuItem onClick={() => navigate('/settings')}>
        <ListItemIcon>
          <SettingsIcon fontSize="small" />
        </ListItemIcon>
        <ListItemText>Dashboard Settings</ListItemText>
      </MenuItem>
    </Menu>
  );

  return (
    <Box sx={{ width: '100%', maxWidth: '100%', overflow: 'hidden' }}>
      {refreshing && (
        <LinearProgress
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            zIndex: 9999,
            height: 3,
          }}
        />
      )}

      {/* Professional University Banner */}
      <Box sx={{ mb: 4 }}>
        <DashboardBanner />
      </Box>

      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography
          variant="h5"
          component="h2"
          sx={{
            fontWeight: 700,
            position: 'relative',
            display: 'inline-block',
            '&::after': {
              content: '""',
              position: 'absolute',
              bottom: -8,
              left: 0,
              width: 60,
              height: 4,
              borderRadius: 2,
              background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${alpha(theme.palette.primary.main, 0.4)} 100%)`,
            }
          }}
        >
          System Overview
        </Typography>
        <Tooltip title="Refresh Data">
          <IconButton onClick={refreshData} color="primary" disabled={refreshing}>
            <RefreshIcon />
          </IconButton>
        </Tooltip>
      </Box>


        <Grid container spacing={{ xs: 2, sm: 3 }} sx={{ mb: 4, width: '100%', mx: 0 }}>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Organizations"
              value={stats.organizations}
              icon={<BusinessIcon />}
              color={theme.palette.primary.main}
              onClick={() => navigate('/organizations')}
              trend="up"
              trendValue="12"
              subtitle="Total registered organizations"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Offices"
              value={stats.offices}
              icon={<LocationIcon />}
              color={theme.palette.secondary.main}
              onClick={() => navigate('/offices')}
              trend="up"
              trendValue="5"
              subtitle="Across all organizations"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Classifications"
              value="24"
              icon={<CategoryIcon />}
              color={theme.palette.warning.main}
              onClick={() => navigate('/main-classifications')}
              subtitle="Main & sub classifications"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Inventory Items"
              value="156"
              icon={<InventoryIcon />}
              color={theme.palette.info.main}
              trend="up"
              trendValue="8"
              subtitle="Total items in inventory"
            />
          </Grid>
        </Grid>

      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography
          variant="h5"
          component="h2"
          sx={{
            fontWeight: 700,
            position: 'relative',
            display: 'inline-block',
            '&::after': {
              content: '""',
              position: 'absolute',
              bottom: -8,
              left: 0,
              width: 60,
              height: 4,
              borderRadius: 2,
              background: `linear-gradient(90deg, ${theme.palette.secondary.main} 0%, ${alpha(theme.palette.secondary.main, 0.4)} 100%)`,
            }
          }}
        >
          Gate Passes
        </Typography>
      </Box>

        <Grid container spacing={{ xs: 2, sm: 3 }} sx={{ width: '100%', mx: 0 }}>
          <Grid item xs={12} sm={6} md={4}>
            <StatCard
              title="Total Gate Passes"
              value={stats.gatePasses}
              icon={<AssignmentIcon />}
              color={theme.palette.info.dark}
              onClick={() => navigate('/gate-passes')}
              subtitle="All time gate passes"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <StatCard
              title="Active Gate Passes"
              value={stats.activeGatePasses}
              icon={<CheckCircleIcon />}
              color={theme.palette.success.main}
              onClick={() => navigate('/gate-passes')}
              trend="up"
              trendValue="15"
              subtitle="Currently active passes"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <StatCard
              title="Overdue Gate Passes"
              value={stats.overdueGatePasses}
              icon={<WarningIcon />}
              color={theme.palette.error.main}
              onClick={() => navigate('/gate-passes')}
              trend="down"
              trendValue="5"
              subtitle="Require immediate attention"
            />
          </Grid>
        </Grid>

      {dashboardMenu}
    </Box>
  );
};

export default Dashboard;
