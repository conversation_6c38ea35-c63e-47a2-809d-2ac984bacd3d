import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Grid,
  TextField,
  Typography,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  CircularProgress,
  Paper,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useSnackbar } from 'notistack';
import { useNavigate, useParams } from 'react-router-dom';
import { createEntryRequest, getEntryRequest, updateEntryRequest } from '../../services/entryRequest';
import { getSuppliers } from '../../services/supplier';
import { getApprovalStatuses } from '../../services/status';

const validationSchema = Yup.object({
  title: Yup.string().required('Title is required'),
  po_number: Yup.string().required('Purchase Order Number is required'),
  supplier: Yup.number().required('Supplier is required'),
  status: Yup.number().required('Status is required'),
  delivery_note: Yup.string(),
  additional_notes: Yup.string(),
  expected_delivery_date: Yup.date().nullable(),
  actual_delivery_date: Yup.date().nullable(),
});

const ItemEntryRequestForm = () => {
  const { id } = useParams();
  const isEditMode = Boolean(id);
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(isEditMode);
  const [suppliers, setSuppliers] = useState([]);
  const [statuses, setStatuses] = useState([]);
  const { enqueueSnackbar } = useSnackbar();
  const navigate = useNavigate();

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [suppliersResponse, statusesResponse] = await Promise.all([
          getSuppliers(),
          getApprovalStatuses(),
        ]);

        setSuppliers(suppliersResponse.results || suppliersResponse);
        setStatuses(statusesResponse.results || statusesResponse);
      } catch (error) {
        console.error('Error fetching form data:', error);
        enqueueSnackbar('Failed to load form data', { variant: 'error' });
      }
    };

    fetchData();
  }, [enqueueSnackbar]);

  useEffect(() => {
    const fetchEntryRequest = async () => {
      if (!isEditMode) return;

      setInitialLoading(true);
      try {
        const data = await getEntryRequest(id);
        formik.setValues({
          title: data.title || '',
          description: data.description || '',
          po_number: data.po_number || '',
          supplier: data.supplier || '',
          status: data.status || '',
          delivery_note: data.delivery_note || '',
          additional_notes: data.additional_notes || '',
          expected_delivery_date: data.expected_delivery_date ? new Date(data.expected_delivery_date) : null,
          actual_delivery_date: data.actual_delivery_date ? new Date(data.actual_delivery_date) : null,
        });
      } catch (error) {
        console.error('Error fetching entry request:', error);
        enqueueSnackbar('Failed to load entry request data', { variant: 'error' });
      } finally {
        setInitialLoading(false);
      }
    };

    fetchEntryRequest();
  }, [id, isEditMode, enqueueSnackbar]);

  const formik = useFormik({
    initialValues: {
      title: '',
      description: '',
      po_number: '',
      supplier: '',
      status: '',
      delivery_note: '',
      additional_notes: '',
      expected_delivery_date: null,
      actual_delivery_date: null,
    },
    validationSchema,
    enableReinitialize: true,
    onSubmit: async (values) => {
      setLoading(true);
      try {
        if (isEditMode) {
          await updateEntryRequest(id, values);
          enqueueSnackbar('Entry request updated successfully', { variant: 'success' });
        } else {
          await createEntryRequest(values);
          enqueueSnackbar('Entry request created successfully', { variant: 'success' });
        }
        navigate('/entry-requests');
      } catch (error) {
        console.error('Error saving entry request:', error);
        enqueueSnackbar('Failed to save entry request', { variant: 'error' });
      } finally {
        setLoading(false);
      }
    },
  });

  if (initialLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h5" component="h1" gutterBottom>
        {isEditMode ? 'Edit Item Entry Request' : 'New Item Entry Request'}
      </Typography>

      <Paper sx={{ p: 3, mb: 3 }}>
        <form onSubmit={formik.handleSubmit}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                id="title"
                name="title"
                label="Title"
                value={formik.values.title}
                onChange={formik.handleChange}
                error={formik.touched.title && Boolean(formik.errors.title)}
                helperText={formik.touched.title && formik.errors.title}
                disabled={loading}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                id="po_number"
                name="po_number"
                label="Purchase Order Number"
                value={formik.values.po_number}
                onChange={formik.handleChange}
                error={formik.touched.po_number && Boolean(formik.errors.po_number)}
                helperText={formik.touched.po_number && formik.errors.po_number}
                disabled={loading}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth error={formik.touched.supplier && Boolean(formik.errors.supplier)}>
                <InputLabel id="supplier-label">Supplier</InputLabel>
                <Select
                  labelId="supplier-label"
                  id="supplier"
                  name="supplier"
                  value={formik.values.supplier}
                  onChange={formik.handleChange}
                  label="Supplier"
                  disabled={loading}
                >
                  {suppliers.map((supplier) => (
                    <MenuItem key={supplier.id} value={supplier.id}>
                      {supplier.company_name || supplier.name}
                    </MenuItem>
                  ))}
                </Select>
                {formik.touched.supplier && formik.errors.supplier && (
                  <Typography variant="caption" color="error">
                    {formik.errors.supplier}
                  </Typography>
                )}
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth error={formik.touched.status && Boolean(formik.errors.status)}>
                <InputLabel id="status-label">Status</InputLabel>
                <Select
                  labelId="status-label"
                  id="status"
                  name="status"
                  value={formik.values.status}
                  onChange={formik.handleChange}
                  label="Status"
                  disabled={loading}
                >
                  {statuses.map((status) => (
                    <MenuItem key={status.id} value={status.id}>
                      {status.name}
                    </MenuItem>
                  ))}
                </Select>
                {formik.touched.status && formik.errors.status && (
                  <Typography variant="caption" color="error">
                    {formik.errors.status}
                  </Typography>
                )}
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                id="description"
                name="description"
                label="Description"
                multiline
                rows={3}
                value={formik.values.description}
                onChange={formik.handleChange}
                error={formik.touched.description && Boolean(formik.errors.description)}
                helperText={formik.touched.description && formik.errors.description}
                disabled={loading}
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Notes
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                id="delivery_note"
                name="delivery_note"
                label="Delivery Note"
                multiline
                rows={4}
                value={formik.values.delivery_note}
                onChange={formik.handleChange}
                error={formik.touched.delivery_note && Boolean(formik.errors.delivery_note)}
                helperText={formik.touched.delivery_note && formik.errors.delivery_note}
                disabled={loading}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                id="additional_notes"
                name="additional_notes"
                label="Additional Notes"
                multiline
                rows={4}
                value={formik.values.additional_notes}
                onChange={formik.handleChange}
                error={formik.touched.additional_notes && Boolean(formik.errors.additional_notes)}
                helperText={formik.touched.additional_notes && formik.errors.additional_notes}
                disabled={loading}
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Dates
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>
            <Grid item xs={12} md={6}>
              <DatePicker
                label="Expected Delivery Date"
                value={formik.values.expected_delivery_date}
                onChange={(date) => formik.setFieldValue('expected_delivery_date', date)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    fullWidth
                    error={formik.touched.expected_delivery_date && Boolean(formik.errors.expected_delivery_date)}
                    helperText={formik.touched.expected_delivery_date && formik.errors.expected_delivery_date}
                  />
                )}
                disabled={loading}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <DatePicker
                label="Actual Delivery Date"
                value={formik.values.actual_delivery_date}
                onChange={(date) => formik.setFieldValue('actual_delivery_date', date)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    fullWidth
                    error={formik.touched.actual_delivery_date && Boolean(formik.errors.actual_delivery_date)}
                    helperText={formik.touched.actual_delivery_date && formik.errors.actual_delivery_date}
                  />
                )}
                disabled={loading}
              />
            </Grid>
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                <Button
                  variant="outlined"
                  color="secondary"
                  onClick={() => navigate('/entry-requests')}
                  sx={{ mr: 2 }}
                  disabled={loading}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  disabled={loading}
                >
                  {loading ? <CircularProgress size={24} /> : isEditMode ? 'Update Request' : 'Submit Request'}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </form>
      </Paper>
    </Box>
  );
};

export default ItemEntryRequestForm;
