import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Grid,
  Autocomplete,
  Chip,
  Box,
  Typography,
  CircularProgress,
} from '@mui/material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useSnackbar } from 'notistack';
import { createInspectionCommittee, updateInspectionCommittee, getInspectionCommittee } from '../../services/inspection';
import { getMainClassifications } from '../../services/classification';
import { getUsersByGroup } from '../../services/users';

const validationSchema = Yup.object({
  title: Yup.string().required('Title is required'),
  description: Yup.string(),
  main_classification: Yup.number().nullable(),
  main_classifications: Yup.array().min(0, 'Select at least one classification'),
  users: Yup.array().min(1, 'At least one member is required'),
});

const InspectionCommitteeDialog = ({ open, onClose, onSave, committee }) => {
  const [mainClassifications, setMainClassifications] = useState([]);
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [committeeData, setCommitteeData] = useState(null);
  const { enqueueSnackbar } = useSnackbar();

  const formik = useFormik({
    initialValues: {
      title: committeeData?.title || committee?.title || '',
      description: committeeData?.description || committee?.description || '',
      main_classification: committeeData?.main_classification || committee?.main_classification || '',
      main_classifications: committeeData?.main_classifications || committee?.main_classifications || [],
      users: committeeData?.users || committee?.users || [],
      is_active: committeeData?.is_active !== undefined ? committeeData.is_active : (committee?.is_active !== undefined ? committee.is_active : true),
    },
    validationSchema,
    enableReinitialize: true,
    onSubmit: async (values) => {
      setLoading(true);
      try {
        // If we're using the new main_classifications field, set the legacy field to null
        if (values.main_classifications && values.main_classifications.length > 0) {
          values.main_classification = '';
        }

        if (committee) {
          await updateInspectionCommittee(committee.id, values);
          enqueueSnackbar('Inspection committee updated successfully', { variant: 'success' });
        } else {
          await createInspectionCommittee(values);
          enqueueSnackbar('Inspection committee created successfully', { variant: 'success' });
        }
        onSave();
      } catch (error) {
        console.error('Error saving committee:', error);
        enqueueSnackbar('Failed to save inspection committee', { variant: 'error' });
      } finally {
        setLoading(false);
      }
    },
  });

  // Fetch basic data (classifications and users)
  useEffect(() => {
    const fetchBasicData = async () => {
      if (!open) return;

      setLoading(true);
      try {
        const [classificationsResponse, usersResponse] = await Promise.all([
          getMainClassifications(),
          getUsersByGroup('Inspector'),
        ]);

        setMainClassifications(classificationsResponse.results || classificationsResponse);
        setUsers(usersResponse.results || usersResponse);
      } catch (error) {
        console.error('Error fetching basic data:', error);
        enqueueSnackbar('Failed to load required data', { variant: 'error' });
      } finally {
        setLoading(false);
      }
    };

    fetchBasicData();
  }, [open, enqueueSnackbar]);

  // Fetch detailed committee data when editing
  useEffect(() => {
    const fetchCommitteeData = async () => {
      if (!open || !committee || !committee.id) {
        setCommitteeData(null);
        return;
      }

      try {
        const detailedCommittee = await getInspectionCommittee(committee.id);
        setCommitteeData(detailedCommittee);
      } catch (error) {
        console.error('Error fetching committee details:', error);
        enqueueSnackbar('Failed to load committee details', { variant: 'warning' });
      }
    };

    fetchCommitteeData();
  }, [open, committee, enqueueSnackbar]);

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {committee ? 'Edit Inspection Committee' : 'Create Inspection Committee'}
      </DialogTitle>
      <form onSubmit={formik.handleSubmit}>
        <DialogContent>
          {loading && (
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>
              <CircularProgress />
            </Box>
          )}
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                id="title"
                name="title"
                label="Title"
                value={formik.values.title}
                onChange={formik.handleChange}
                error={formik.touched.title && Boolean(formik.errors.title)}
                helperText={formik.touched.title && formik.errors.title}
                disabled={loading}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                id="description"
                name="description"
                label="Description"
                multiline
                rows={4}
                value={formik.values.description}
                onChange={formik.handleChange}
                error={formik.touched.description && Boolean(formik.errors.description)}
                helperText={formik.touched.description && formik.errors.description}
                disabled={loading}
              />
            </Grid>
            <Grid item xs={12}>
              <Autocomplete
                multiple
                id="main_classifications"
                options={mainClassifications}
                getOptionLabel={(option) =>
                  `${option.code} - ${option.name}`
                }
                value={mainClassifications.filter(classification =>
                  (formik.values.main_classifications || []).includes(classification.id)
                )}
                onChange={(event, newValue) => {
                  formik.setFieldValue(
                    'main_classifications',
                    newValue.map(classification => classification.id)
                  );
                }}
                renderTags={(value, getTagProps) =>
                  value.map((option, index) => {
                    const tagProps = getTagProps({ index });
                    // Remove key from props and use it directly on Chip
                    const { key, ...chipProps } = tagProps;
                    return (
                      <Chip
                        key={option.id}
                        label={`${option.code} - ${option.name}`}
                        {...chipProps}
                      />
                    );
                  })
                }
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Classifications"
                    error={formik.touched.main_classifications && Boolean(formik.errors.main_classifications)}
                    helperText={formik.touched.main_classifications && formik.errors.main_classifications}
                  />
                )}
                disabled={loading}
              />
              <Typography variant="caption" color="text.secondary">
                Select the classifications this committee is responsible for
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <Autocomplete
                multiple
                id="users"
                options={users}
                getOptionLabel={(option) =>
                  `${option.first_name} ${option.last_name}`.trim() || option.username
                }
                value={users.filter(user => (formik.values.users || []).includes(user.id))}
                onChange={(event, newValue) => {
                  formik.setFieldValue(
                    'users',
                    newValue.map(user => user.id)
                  );
                }}
                renderTags={(value, getTagProps) =>
                  value.map((option, index) => {
                    const tagProps = getTagProps({ index });
                    // Remove key from props and use it directly on Chip
                    const { key, ...chipProps } = tagProps;
                    return (
                      <Chip
                        key={option.id}
                        label={`${option.first_name} ${option.last_name}`.trim() || option.username}
                        {...chipProps}
                      />
                    );
                  })
                }
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Committee Members"
                    error={formik.touched.users && Boolean(formik.errors.users)}
                    helperText={formik.touched.users && formik.errors.users}
                  />
                )}
                disabled={loading}
              />
              <Typography variant="caption" color="text.secondary">
                Only users in the Inspector group are shown
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formik.values.is_active}
                    onChange={formik.handleChange}
                    name="is_active"
                    color="primary"
                    disabled={loading}
                  />
                }
                label="Active"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose} disabled={loading}>
            Cancel
          </Button>
          <Button type="submit" variant="contained" color="primary" disabled={loading}>
            {loading ? <CircularProgress size={24} /> : committee ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default InspectionCommitteeDialog;
