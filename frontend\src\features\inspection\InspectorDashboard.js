import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  Grid,
  Tabs,
  Tab,
} from '@mui/material';
import {
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  Assignment as InspectionIcon,
  Pending as PendingIcon,
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { getItemEntryRequestItems, updateInspectionStatus } from '../../services/entryRequest';

const InspectorDashboard = () => {
  const [assignedItems, setAssignedItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedItem, setSelectedItem] = useState(null);
  const [inspectionDialogOpen, setInspectionDialogOpen] = useState(false);
  const [inspectionStatus, setInspectionStatus] = useState('');
  const [inspectionNotes, setInspectionNotes] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const { enqueueSnackbar } = useSnackbar();

  // Get current user from localStorage
  const getCurrentUser = () => {
    try {
      const userData = localStorage.getItem('user');
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Error parsing user data:', error);
      return null;
    }
  };

  const user = getCurrentUser();

  useEffect(() => {
    fetchAssignedItems();
  }, []);

  const fetchAssignedItems = async () => {
    try {
      setLoading(true);
      
      // Fetch items assigned to committees that the current user is part of
      const response = await getItemEntryRequestItems({
        assigned_committee__users: user?.id,
        inspection_status: ['pending', 'in_progress']
      });
      
      setAssignedItems(response.results || response);
    } catch (error) {
      console.error('Error fetching assigned items:', error);
      enqueueSnackbar('Failed to load assigned items', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleInspectionAction = (item) => {
    setSelectedItem(item);
    setInspectionStatus('');
    setInspectionNotes('');
    setInspectionDialogOpen(true);
  };

  const handleSubmitInspection = async () => {
    if (!inspectionStatus) {
      enqueueSnackbar('Please select an inspection status', { variant: 'warning' });
      return;
    }

    try {
      setSubmitting(true);
      
      await updateInspectionStatus(selectedItem.id, {
        status: inspectionStatus,
        notes: inspectionNotes
      });

      enqueueSnackbar('Inspection status updated successfully', { variant: 'success' });
      setInspectionDialogOpen(false);
      fetchAssignedItems(); // Refresh the list
    } catch (error) {
      console.error('Error updating inspection status:', error);
      enqueueSnackbar('Failed to update inspection status', { variant: 'error' });
    } finally {
      setSubmitting(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return 'warning';
      case 'in_progress': return 'info';
      case 'passed': return 'success';
      case 'failed': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending': return <PendingIcon />;
      case 'in_progress': return <InspectionIcon />;
      case 'passed': return <ApproveIcon />;
      case 'failed': return <RejectIcon />;
      default: return <InspectionIcon />;
    }
  };

  const pendingItems = assignedItems.filter(item => item.inspection_status === 'pending');
  const inProgressItems = assignedItems.filter(item => item.inspection_status === 'in_progress');
  const completedItems = assignedItems.filter(item => ['passed', 'failed'].includes(item.inspection_status));

  const TabPanel = ({ children, value, index }) => (
    <div hidden={value !== index}>
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );

  const renderItemsTable = (items, showActions = true) => (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Item Description</TableCell>
            <TableCell>Entry Request</TableCell>
            <TableCell>Committee</TableCell>
            <TableCell>Status</TableCell>
            <TableCell>Quantity</TableCell>
            {showActions && <TableCell>Actions</TableCell>}
          </TableRow>
        </TableHead>
        <TableBody>
          {items.map((item) => (
            <TableRow key={item.id}>
              <TableCell>{item.item_description}</TableCell>
              <TableCell>{item.entry_request_title || `Request #${item.entry_request}`}</TableCell>
              <TableCell>{item.assigned_committee_name || 'Not Assigned'}</TableCell>
              <TableCell>
                <Chip
                  icon={getStatusIcon(item.inspection_status)}
                  label={item.inspection_status_display || item.inspection_status}
                  color={getStatusColor(item.inspection_status)}
                  size="small"
                />
              </TableCell>
              <TableCell>{item.quantity_requested}</TableCell>
              {showActions && (
                <TableCell>
                  {item.inspection_status === 'pending' && (
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => handleInspectionAction(item)}
                      startIcon={<InspectionIcon />}
                    >
                      Start Inspection
                    </Button>
                  )}
                  {item.inspection_status === 'in_progress' && (
                    <Button
                      variant="contained"
                      size="small"
                      onClick={() => handleInspectionAction(item)}
                      startIcon={<InspectionIcon />}
                    >
                      Complete Inspection
                    </Button>
                  )}
                </TableCell>
              )}
            </TableRow>
          ))}
          {items.length === 0 && (
            <TableRow>
              <TableCell colSpan={showActions ? 6 : 5} align="center">
                <Typography variant="body2" color="text.secondary">
                  No items found
                </Typography>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </TableContainer>
  );

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Inspector Dashboard
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Manage items assigned to your inspection committees
      </Typography>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="warning.main">
                {pendingItems.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Pending Inspections
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="info.main">
                {inProgressItems.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                In Progress
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="success.main">
                {completedItems.filter(item => item.inspection_status === 'passed').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Passed
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="error.main">
                {completedItems.filter(item => item.inspection_status === 'failed').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Failed
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs for different item statuses */}
      <Paper sx={{ width: '100%' }}>
        <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
          <Tab label={`Pending (${pendingItems.length})`} />
          <Tab label={`In Progress (${inProgressItems.length})`} />
          <Tab label={`Completed (${completedItems.length})`} />
        </Tabs>

        <TabPanel value={tabValue} index={0}>
          {renderItemsTable(pendingItems)}
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          {renderItemsTable(inProgressItems)}
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          {renderItemsTable(completedItems, false)}
        </TabPanel>
      </Paper>

      {/* Inspection Dialog */}
      <Dialog
        open={inspectionDialogOpen}
        onClose={() => !submitting && setInspectionDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Update Inspection Status
        </DialogTitle>
        <DialogContent>
          {selectedItem && (
            <>
              <Typography variant="body1" gutterBottom>
                Item: {selectedItem.item_description}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Current Status: {selectedItem.inspection_status_display || selectedItem.inspection_status}
              </Typography>

              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Inspection Status</InputLabel>
                <Select
                  value={inspectionStatus}
                  onChange={(e) => setInspectionStatus(e.target.value)}
                  label="Inspection Status"
                >
                  <MenuItem value="in_progress">In Progress</MenuItem>
                  <MenuItem value="passed">Passed</MenuItem>
                  <MenuItem value="failed">Failed</MenuItem>
                </Select>
              </FormControl>

              <TextField
                fullWidth
                multiline
                rows={4}
                label="Inspection Notes"
                value={inspectionNotes}
                onChange={(e) => setInspectionNotes(e.target.value)}
                placeholder="Enter inspection findings, remarks, or recommendations..."
              />
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setInspectionDialogOpen(false)}
            disabled={submitting}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmitInspection}
            variant="contained"
            disabled={submitting}
            startIcon={submitting ? <CircularProgress size={20} /> : null}
          >
            {submitting ? 'Updating...' : 'Update Status'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default InspectorDashboard;
