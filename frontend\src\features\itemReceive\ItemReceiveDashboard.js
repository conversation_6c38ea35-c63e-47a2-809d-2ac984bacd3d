import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardActionArea,
  CardContent,
  Grid,
  Typography,
  Paper,
  Divider,
} from '@mui/material';
import {
  Inventory as InventoryIcon,
  Assignment as AssignmentIcon,
  LocalShipping as ShippingIcon,
  Search as SearchIcon,
  Receipt as ReceiptIcon,
  Add as AddIcon,
  ListAlt as ListIcon,
  Dashboard as DashboardIcon,
  FactCheck as InspectionIcon,
  Store as StoreIcon,
  Description as DocumentIcon,
} from '@mui/icons-material';

const ItemReceiveDashboard = () => {
  const navigate = useNavigate();

  const itemReceiveCards = [
    {
      title: 'Pre-Registration',
      description: 'Step 1: Procurement officer pre-registers items before delivery',
      icon: <DocumentIcon fontSize="large" color="primary" />,
      path: '/item-receive/pre-registration',
      color: '#e3f2fd',
      badge: '1',
    },
    {
      title: 'Delivery Receipt',
      description: 'Step 2: Record initial delivery of items from suppliers',
      icon: <ShippingIcon fontSize="large" color="primary" />,
      path: '/delivery-receipt/new',
      color: '#e8f5e9',
      badge: '2',
    },
    {
      title: 'Inspections',
      description: 'Step 3: Inspect delivered items and record any discrepancies',
      icon: <SearchIcon fontSize="large" color="primary" />,
      path: '/inspections',
      color: '#fff3e0',
      badge: '3',
    },
    {
      title: 'Model 19 Form',
      description: 'Step 4: Create Model 19 receipts for accepted items',
      icon: <ReceiptIcon fontSize="large" color="primary" />,
      path: '/model19-form',
      color: '#f3e5f5',
      badge: '4',
    },
    {
      title: 'Pre-Registrations List',
      description: 'View and manage all pre-registered items',
      icon: <ListIcon fontSize="large" color="primary" />,
      path: '/item-receive/pre-registrations',
      color: '#e0f7fa',
    },
    {
      title: 'Delivery Receipts',
      description: 'View and manage all delivery receipts',
      icon: <AssignmentIcon fontSize="large" color="primary" />,
      path: '/delivery-receipts',
      color: '#fff8e1',
    },
    {
      title: 'Inspection Reports',
      description: 'View and manage all inspection reports',
      icon: <InspectionIcon fontSize="large" color="primary" />,
      path: '/inspections',
      color: '#f1f8e9',
    },
    {
      title: 'Model 19 Receipts',
      description: 'View and manage all Model 19 receipts',
      icon: <ReceiptIcon fontSize="large" color="primary" />,
      path: '/model19-receipts',
      color: '#e8eaf6',
    },
  ];

  return (
    <Box sx={{ p: 3 }}>
      <Paper sx={{ p: 3, mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          Item Receive Dashboard
        </Typography>
        <Typography variant="body1" color="textSecondary">
          Manage the complete item receiving process from pre-registration to final acceptance. 
          Track items through delivery, inspection, and formal receipt into inventory.
        </Typography>
      </Paper>

      <Grid container spacing={3}>
        {itemReceiveCards.map((card, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <Card
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                backgroundColor: card.color,
                transition: 'transform 0.3s, box-shadow 0.3s',
                '&:hover': {
                  transform: 'translateY(-5px)',
                  boxShadow: '0 8px 16px rgba(0,0,0,0.1)',
                },
                position: 'relative',
              }}
            >
              {card.badge && (
                <Box
                  sx={{
                    position: 'absolute',
                    top: 12,
                    right: 12,
                    width: 24,
                    height: 24,
                    borderRadius: '50%',
                    backgroundColor: 'primary.main',
                    color: 'white',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontWeight: 'bold',
                    fontSize: '0.75rem',
                    zIndex: 1,
                  }}
                >
                  {card.badge}
                </Box>
              )}
              <CardActionArea
                sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}
                onClick={() => navigate(card.path)}
              >
                <CardContent sx={{ width: '100%' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    {card.icon}
                    <Typography variant="h6" component="div" sx={{ ml: 1 }}>
                      {card.title}
                    </Typography>
                  </Box>
                  <Divider sx={{ mb: 2 }} />
                  <Typography variant="body2" color="text.secondary">
                    {card.description}
                  </Typography>
                </CardContent>
              </CardActionArea>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default ItemReceiveDashboard;
