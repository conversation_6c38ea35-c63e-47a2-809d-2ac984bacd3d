import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Grid,
  TextField,
  Typography,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Alert,
  Snackbar,
  FormHelperText,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Send as SendIcon,
  CloudUpload as UploadIcon,
  Description as DescriptionIcon,
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { format } from 'date-fns';
import { getSuppliers } from '../../services/supplier';
import { getMainClassifications } from '../../services/classification';
import { getUnitsOfMeasure } from '../../services/specifications';
import { createPreRegistration, updatePreRegistration } from '../../services/itemReceive';

const PreRegistrationForm = () => {
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  const [loading, setLoading] = useState(false);
  const [suppliers, setSuppliers] = useState([]);
  const [classifications, setClassifications] = useState([]);
  const [unitOfMeasures, setUnitOfMeasures] = useState([]);
  const [formData, setFormData] = useState({
    requestDate: format(new Date(), 'yyyy-MM-dd'),
    poNumber: '',
    supplier: '',
    description: '',
    technicalSpecs: '',
  });
  const [items, setItems] = useState([
    {
      id: 'temp-1',
      itemCode: `PRE-${Math.floor(1000 + Math.random() * 9000)}`,
      description: '',
      quantity: 1,
      unit: '',
      unitPrice: 0,
      classification: '',
      isNew: true,
    },
  ]);
  const [files, setFiles] = useState([]);
  const [errors, setErrors] = useState({});
  const [showInspectionWarning, setShowInspectionWarning] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const [suppliersData, classificationsData, unitOfMeasuresData] = await Promise.all([
          getSuppliers(),
          getMainClassifications(),
          getUnitsOfMeasure(),
        ]);

        setSuppliers(suppliersData.results || suppliersData);
        setClassifications(classificationsData.results || classificationsData);
        setUnitOfMeasures(unitOfMeasuresData.results || unitOfMeasuresData);
      } catch (error) {
        console.error('Error fetching form data:', error);
        enqueueSnackbar('Failed to load form data', { variant: 'error' });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [enqueueSnackbar]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });

    // Clear error when field is updated
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: '',
      });
    }
  };

  const handleItemChange = (index, field, value) => {
    const newItems = [...items];
    newItems[index][field] = value;

    // Check if inspection warning should be shown
    if (field === 'classification') {
      const classification = classifications.find(c => c.id === value);
      if (classification && (classification.name.toLowerCase().includes('medical') ||
          classification.name.toLowerCase().includes('lab'))) {
        setShowInspectionWarning(true);
      }
    }

    setItems(newItems);
  };

  const handleAddItem = () => {
    setItems([
      ...items,
      {
        id: `temp-${Date.now()}`,
        itemCode: `PRE-${Math.floor(1000 + Math.random() * 9000)}`,
        description: '',
        quantity: 1,
        unit: '',
        unitPrice: 0,
        classification: '',
        isNew: true,
      },
    ]);
  };

  const handleRemoveItem = (index) => {
    const newItems = [...items];
    newItems.splice(index, 1);
    setItems(newItems);
  };

  const handleFileChange = (e) => {
    const selectedFiles = Array.from(e.target.files);
    setFiles([...files, ...selectedFiles]);
  };

  const handleRemoveFile = (index) => {
    const newFiles = [...files];
    newFiles.splice(index, 1);
    setFiles(newFiles);
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.poNumber.trim()) {
      newErrors.poNumber = 'PO Number is required';
    }

    if (!formData.supplier) {
      newErrors.supplier = 'Supplier is required';
    }

    const itemErrors = items.map(item => {
      const errors = {};
      if (!item.description.trim()) {
        errors.description = 'Description is required';
      }
      if (!item.unit) {
        errors.unit = 'Unit is required';
      }
      if (!item.classification) {
        errors.classification = 'Classification is required';
      }
      return errors;
    });

    if (itemErrors.some(error => Object.keys(error).length > 0)) {
      newErrors.items = itemErrors;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e, isDraft = false) => {
    e.preventDefault();

    if (!isDraft && !validateForm()) {
      enqueueSnackbar('Please fix the errors in the form', { variant: 'error' });
      return;
    }

    setLoading(true);
    try {
      // Prepare form data for submission
      const formPayload = {
        ...formData,
        items: items.map(item => ({
          item_code: item.itemCode,
          description: item.description,
          quantity: item.quantity,
          unit: item.unit,
          unit_price: item.unitPrice,
          classification: item.classification,
        })),
        is_draft: isDraft,
      };

      // Create FormData for file uploads
      const formDataWithFiles = new FormData();
      formDataWithFiles.append('data', JSON.stringify(formPayload));
      files.forEach((file, index) => {
        formDataWithFiles.append(`file_${index}`, file);
      });

      // TODO: Replace with actual API call
      console.log('Submitting form data:', formPayload);
      console.log('Files:', files);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      enqueueSnackbar(
        isDraft ? 'Pre-registration saved as draft' : 'Pre-registration submitted successfully',
        { variant: 'success' }
      );

      // Navigate back to the dashboard
      navigate('/item-receive');
    } catch (error) {
      console.error('Error submitting pre-registration:', error);
      enqueueSnackbar('Failed to submit pre-registration', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ p: 3, maxWidth: 1200, mx: 'auto' }}>
      <Paper sx={{ p: 3, mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          Item Entry Request
        </Typography>
        <Typography variant="subtitle1" color="textSecondary" gutterBottom>
          Pre-Registration for Inventory Receiving
        </Typography>
        <Divider sx={{ mb: 3 }} />

        <form onSubmit={(e) => handleSubmit(e, false)}>
          <Grid container spacing={3}>
            {/* Basic Information */}
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Request Date"
                type="date"
                name="requestDate"
                value={formData.requestDate}
                InputLabelProps={{ shrink: true }}
                disabled
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Purchase Order (PO) Number"
                name="poNumber"
                value={formData.poNumber}
                onChange={handleInputChange}
                placeholder="PO-2024-XXXX"
                required
                error={!!errors.poNumber}
                helperText={errors.poNumber}
              />
            </Grid>

            <Grid item xs={12}>
              <FormControl fullWidth error={!!errors.supplier}>
                <InputLabel>Supplier *</InputLabel>
                <Select
                  name="supplier"
                  value={formData.supplier}
                  onChange={handleInputChange}
                  label="Supplier *"
                  required
                >
                  <MenuItem value="">-- Select Supplier --</MenuItem>
                  {suppliers.map((supplier) => (
                    <MenuItem key={supplier.id} value={supplier.id}>
                      {supplier.name}
                    </MenuItem>
                  ))}
                </Select>
                {errors.supplier && <FormHelperText>{errors.supplier}</FormHelperText>}
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                multiline
                rows={2}
              />
            </Grid>

            {/* Document Uploads */}
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Supporting Documents
              </Typography>
              <Paper
                variant="outlined"
                sx={{
                  p: 3,
                  border: '2px dashed #ccc',
                  textAlign: 'center',
                  cursor: 'pointer',
                  mb: 2,
                }}
                onClick={() => document.getElementById('file-upload').click()}
              >
                <input
                  type="file"
                  id="file-upload"
                  multiple
                  style={{ display: 'none' }}
                  onChange={handleFileChange}
                  accept=".pdf,.doc,.docx,.xls,.xlsx"
                />
                <UploadIcon sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                <Typography variant="body1" gutterBottom>
                  Drag & drop files here or click to browse
                </Typography>
                <Typography variant="caption" color="textSecondary">
                  Accepted: PO copy, bid documents, specifications (PDF/Word/Excel)
                </Typography>
              </Paper>

              {files.length > 0 && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Uploaded Files:
                  </Typography>
                  <List>
                    {files.map((file, index) => (
                      <ListItem
                        key={index}
                        secondaryAction={
                          <IconButton edge="end" onClick={() => handleRemoveFile(index)}>
                            <DeleteIcon />
                          </IconButton>
                        }
                      >
                        <ListItemIcon>
                          <DescriptionIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary={file.name}
                          secondary={`${(file.size / 1024).toFixed(2)} KB`}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              )}
            </Grid>

            {/* Items Table */}
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Items to Pre-Register *
              </Typography>
              <TableContainer component={Paper} variant="outlined" sx={{ mb: 2 }}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Item Code</TableCell>
                      <TableCell>Description</TableCell>
                      <TableCell>Quantity</TableCell>
                      <TableCell>Unit</TableCell>
                      <TableCell>Unit Price</TableCell>
                      <TableCell>Classification</TableCell>
                      <TableCell>Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {items.map((item, index) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <TextField
                            fullWidth
                            value={item.itemCode}
                            disabled
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <TextField
                            fullWidth
                            placeholder="Description"
                            value={item.description}
                            onChange={(e) => handleItemChange(index, 'description', e.target.value)}
                            required
                            size="small"
                            error={errors.items && errors.items[index]?.description}
                            helperText={errors.items && errors.items[index]?.description}
                          />
                        </TableCell>
                        <TableCell>
                          <TextField
                            type="number"
                            value={item.quantity}
                            onChange={(e) => handleItemChange(index, 'quantity', parseInt(e.target.value, 10) || 0)}
                            inputProps={{ min: 1 }}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <FormControl fullWidth size="small" error={errors.items && errors.items[index]?.unit}>
                            <Select
                              value={item.unit}
                              onChange={(e) => handleItemChange(index, 'unit', e.target.value)}
                              displayEmpty
                            >
                              <MenuItem value="">Select Unit</MenuItem>
                              {unitOfMeasures.map((unit) => (
                                <MenuItem key={unit.id} value={unit.id}>
                                  {unit.name}
                                </MenuItem>
                              ))}
                            </Select>
                            {errors.items && errors.items[index]?.unit && (
                              <FormHelperText>{errors.items && errors.items[index]?.unit}</FormHelperText>
                            )}
                          </FormControl>
                        </TableCell>
                        <TableCell>
                          <TextField
                            type="number"
                            value={item.unitPrice}
                            onChange={(e) => handleItemChange(index, 'unitPrice', parseFloat(e.target.value) || 0)}
                            inputProps={{ min: 0, step: 0.01 }}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <FormControl fullWidth size="small" error={errors.items && errors.items[index]?.classification}>
                            <Select
                              value={item.classification}
                              onChange={(e) => handleItemChange(index, 'classification', e.target.value)}
                              displayEmpty
                            >
                              <MenuItem value="">Select Classification</MenuItem>
                              {classifications.map((classification) => (
                                <MenuItem key={classification.id} value={classification.id}>
                                  {classification.name}
                                </MenuItem>
                              ))}
                            </Select>
                            {errors.items && errors.items[index]?.classification && (
                              <FormHelperText>{errors.items && errors.items[index]?.classification}</FormHelperText>
                            )}
                          </FormControl>
                        </TableCell>
                        <TableCell>
                          <IconButton
                            color="error"
                            onClick={() => handleRemoveItem(index)}
                            disabled={items.length === 1}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
              <Button
                variant="outlined"
                startIcon={<AddIcon />}
                onClick={handleAddItem}
                sx={{ mb: 3 }}
              >
                Add Item
              </Button>
            </Grid>

            {/* Technical Specifications */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Technical Specifications (If Applicable)"
                name="technicalSpecs"
                value={formData.technicalSpecs}
                onChange={handleInputChange}
                multiline
                rows={4}
                placeholder="Detailed specs, model numbers, compliance requirements..."
              />
            </Grid>

            {/* Inspection Warning */}
            {showInspectionWarning && (
              <Grid item xs={12}>
                <Alert severity="warning" sx={{ mb: 2 }}>
                  <Typography variant="subtitle2">Inspection Required</Typography>
                  <Typography variant="body2">
                    One or more items in this pre-registration will require technical inspection upon delivery.
                    The inspection committee will be notified automatically.
                  </Typography>
                </Alert>
              </Grid>
            )}

            {/* Submission Buttons */}
            <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 2 }}>
              <Button
                variant="outlined"
                startIcon={<SaveIcon />}
                onClick={(e) => handleSubmit(e, true)}
                disabled={loading}
              >
                Save Draft
              </Button>
              <Button
                type="submit"
                variant="contained"
                startIcon={<SendIcon />}
                disabled={loading}
              >
                Submit for Pre-Registration
              </Button>
            </Grid>
          </Grid>
        </form>
      </Paper>
    </Box>
  );
};

export default PreRegistrationForm;


