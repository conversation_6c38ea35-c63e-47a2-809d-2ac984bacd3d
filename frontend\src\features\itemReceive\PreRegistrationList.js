import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  But<PERSON>,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Typography,
  Tooltip,
  Chip,
  TextField,
  InputAdornment,
  CircularProgress,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { format } from 'date-fns';
import { getPreRegistrations, deletePreRegistration, approvePreRegistration, rejectPreRegistration } from '../../services/itemReceive';

// Mock data for pre-registrations
const mockPreRegistrations = [
  {
    id: 1,
    poNumber: 'PO-2024-0001',
    supplier: 'ABC Medical Supplies',
    requestDate: '2024-06-15',
    itemCount: 5,
    status: 'pending',
    createdBy: '<PERSON>',
  },
  {
    id: 2,
    poNumber: 'PO-2024-0002',
    supplier: 'XYZ Lab Equipment',
    requestDate: '2024-06-16',
    itemCount: 3,
    status: 'approved',
    createdBy: 'Jane Smith',
  },
  {
    id: 3,
    poNumber: 'PO-2024-0003',
    supplier: 'Office World',
    requestDate: '2024-06-17',
    itemCount: 8,
    status: 'rejected',
    createdBy: 'Mike Johnson',
  },
  {
    id: 4,
    poNumber: 'PO-2024-0004',
    supplier: 'Medical Devices Inc.',
    requestDate: '2024-06-18',
    itemCount: 2,
    status: 'draft',
    createdBy: 'John Doe',
  },
  {
    id: 5,
    poNumber: 'PO-2024-0005',
    supplier: 'Lab Supplies Co.',
    requestDate: '2024-06-19',
    itemCount: 10,
    status: 'pending',
    createdBy: 'Jane Smith',
  },
];

const PreRegistrationList = () => {
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  const [preRegistrations, setPreRegistrations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [totalCount, setTotalCount] = useState(0);

  useEffect(() => {
    const fetchPreRegistrations = async () => {
      setLoading(true);
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Filter mock data based on search term
        const filteredData = mockPreRegistrations.filter(
          item =>
            item.poNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
            item.supplier.toLowerCase().includes(searchTerm.toLowerCase())
        );

        setPreRegistrations(filteredData);
        setTotalCount(filteredData.length);
      } catch (error) {
        console.error('Error fetching pre-registrations:', error);
        enqueueSnackbar('Failed to load pre-registrations', { variant: 'error' });
      } finally {
        setLoading(false);
      }
    };

    fetchPreRegistrations();
  }, [enqueueSnackbar, searchTerm]);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
    setPage(0);
  };

  const handleAddClick = () => {
    navigate('/item-receive/pre-registration');
  };

  const handleEditClick = (id) => {
    navigate(`/item-receive/pre-registration/${id}/edit`);
  };

  const handleViewClick = (id) => {
    navigate(`/item-receive/pre-registration/${id}`);
  };

  const handleDeleteClick = (id) => {
    // Implement delete functionality
    enqueueSnackbar('Pre-registration deleted successfully', { variant: 'success' });
  };

  const handleApproveClick = (id) => {
    // Implement approve functionality
    enqueueSnackbar('Pre-registration approved successfully', { variant: 'success' });
  };

  const handleRejectClick = (id) => {
    // Implement reject functionality
    enqueueSnackbar('Pre-registration rejected', { variant: 'info' });
  };

  const getStatusChip = (status) => {
    const statusProps = {
      pending: { color: 'warning', label: 'Pending' },
      approved: { color: 'success', label: 'Approved' },
      rejected: { color: 'error', label: 'Rejected' },
      draft: { color: 'default', label: 'Draft' },
    };

    const { color, label } = statusProps[status] || { color: 'default', label: status };

    return <Chip label={label} color={color} size="small" />;
  };

  return (
    <Box sx={{ p: 3, maxWidth: 1200, mx: 'auto' }}>
      <Paper sx={{ p: 3, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4">Pre-Registrations</Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAddClick}
          >
            New Pre-Registration
          </Button>
        </Box>

        <Box sx={{ mb: 3 }}>
          <TextField
            fullWidth
            placeholder="Search by PO Number or Supplier..."
            value={searchTerm}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />
        </Box>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>PO Number</TableCell>
                    <TableCell>Supplier</TableCell>
                    <TableCell>Request Date</TableCell>
                    <TableCell>Items</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Created By</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {preRegistrations.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} align="center">
                        No pre-registrations found
                      </TableCell>
                    </TableRow>
                  ) : (
                    preRegistrations
                      .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                      .map((preReg) => (
                        <TableRow key={preReg.id}>
                          <TableCell>{preReg.poNumber}</TableCell>
                          <TableCell>{preReg.supplier}</TableCell>
                          <TableCell>{format(new Date(preReg.requestDate), 'MMM dd, yyyy')}</TableCell>
                          <TableCell>
                            <Chip label={preReg.itemCount} size="small" color="primary" />
                          </TableCell>
                          <TableCell>{getStatusChip(preReg.status)}</TableCell>
                          <TableCell>{preReg.createdBy}</TableCell>
                          <TableCell>
                            <Tooltip title="View">
                              <IconButton size="small" onClick={() => handleViewClick(preReg.id)}>
                                <ViewIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                            {preReg.status === 'draft' && (
                              <Tooltip title="Edit">
                                <IconButton size="small" onClick={() => handleEditClick(preReg.id)}>
                                  <EditIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            )}
                            {preReg.status === 'pending' && (
                              <>
                                <Tooltip title="Approve">
                                  <IconButton
                                    size="small"
                                    color="success"
                                    onClick={() => handleApproveClick(preReg.id)}
                                  >
                                    <ApproveIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                                <Tooltip title="Reject">
                                  <IconButton
                                    size="small"
                                    color="error"
                                    onClick={() => handleRejectClick(preReg.id)}
                                  >
                                    <RejectIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              </>
                            )}
                            {(preReg.status === 'draft' || preReg.status === 'rejected') && (
                              <Tooltip title="Delete">
                                <IconButton
                                  size="small"
                                  color="error"
                                  onClick={() => handleDeleteClick(preReg.id)}
                                >
                                  <DeleteIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            )}
                          </TableCell>
                        </TableRow>
                      ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>

            <TablePagination
              rowsPerPageOptions={[5, 10, 25]}
              component="div"
              count={totalCount}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </>
        )}
      </Paper>
    </Box>
  );
};

export default PreRegistrationList;
