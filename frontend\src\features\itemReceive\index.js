import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import ItemReceiveDashboard from './ItemReceiveDashboard';
import PreRegistrationForm from './PreRegistrationForm';
import PreRegistrationList from './PreRegistrationList';

const ItemReceiveRoutes = () => {
  return (
    <Routes>
      <Route path="/" element={<ItemReceiveDashboard />} />
      <Route path="/pre-registration" element={<PreRegistrationForm />} />
      <Route path="/pre-registration/:id" element={<PreRegistrationForm />} />
      <Route path="/pre-registration/:id/edit" element={<PreRegistrationForm />} />
      <Route path="/pre-registrations" element={<PreRegistrationList />} />
      <Route path="*" element={<Navigate to="/item-receive" replace />} />
    </Routes>
  );
};

export default ItemReceiveRoutes;
