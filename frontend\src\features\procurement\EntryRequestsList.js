import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Button,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tooltip,
  Alert,
  Grid,
  Divider,
  CircularProgress,
  Menu,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  Visibility as ViewIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  Check as ApproveIcon,
  Close as RejectIcon,
  Assignment as AssignIcon,
  Print as PrintIcon,
  Refresh as RefreshIcon,
  AttachFile as AttachFileIcon,
  List as ListIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useSnackbar } from 'notistack';
import api from '../../utils/axios';

const EntryRequestsList = () => {
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();

  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [actionMenuAnchor, setActionMenuAnchor] = useState(null);
  const [actionMenuRequest, setActionMenuRequest] = useState(null);
  const [approvalDialogOpen, setApprovalDialogOpen] = useState(false);
  const [approvalComments, setApprovalComments] = useState('');
  const [approvalAction, setApprovalAction] = useState(''); // 'approve' or 'reject'
  const [stores, setStores] = useState([]);
  const [assignDialogOpen, setAssignDialogOpen] = useState(false);
  const [selectedStore, setSelectedStore] = useState('');

  useEffect(() => {
    fetchRequests();
    fetchStores();
  }, []);

  const fetchRequests = async () => {
    try {
      setLoading(true);
      const response = await api.get('/entry-requests/');
      setRequests(response.data.results || response.data || []);
    } catch (error) {
      console.error('Error fetching entry requests:', error);
      enqueueSnackbar('Failed to fetch entry requests', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const fetchStores = async () => {
    try {
      const response = await api.get('/stores/');
      setStores(response.data.results || response.data || []);
    } catch (error) {
      console.error('Error fetching stores:', error);
    }
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'draft': return 'default';
      case 'pending': return 'warning';
      case 'approved': return 'success';
      case 'assigned': return 'info';
      case 'inspecting': return 'secondary';
      case 'completed': return 'success';
      case 'rejected': return 'error';
      default: return 'default';
    }
  };

  const getWorkflowStatusColor = (workflowStatus) => {
    switch (workflowStatus?.toLowerCase()) {
      case 'draft': return 'default';
      case 'pending': return 'warning';
      case 'approved': return 'success';
      case 'assigned': return 'info';
      case 'inspecting': return 'secondary';
      case 'completed': return 'success';
      case 'rejected': return 'error';
      default: return 'default';
    }
  };

  const handleViewRequest = (request) => {
    setSelectedRequest(request);
    setViewDialogOpen(true);
  };

  const handleEditRequest = (request) => {
    navigate(`/procurement/entry-request/edit/${request.id}`);
  };

  const handleDeleteRequest = async () => {
    try {
      await api.delete(`/entry-requests/${selectedRequest.id}/`);
      enqueueSnackbar('Entry request deleted successfully', { variant: 'success' });
      setDeleteDialogOpen(false);
      setSelectedRequest(null);
      fetchRequests();
    } catch (error) {
      console.error('Error deleting entry request:', error);
      enqueueSnackbar('Failed to delete entry request', { variant: 'error' });
    }
  };

  const handleActionMenuOpen = (event, request) => {
    setActionMenuAnchor(event.currentTarget);
    setActionMenuRequest(request);
  };

  const handleActionMenuClose = () => {
    setActionMenuAnchor(null);
    setActionMenuRequest(null);
  };

  const handleApprovalAction = (action) => {
    setApprovalAction(action);
    setSelectedRequest(actionMenuRequest);
    setApprovalDialogOpen(true);
    handleActionMenuClose();
  };

  const handleAssignToStore = () => {
    setSelectedRequest(actionMenuRequest);
    setAssignDialogOpen(true);
    handleActionMenuClose();
  };

  const submitApproval = async () => {
    try {
      const endpoint = approvalAction === 'approve' ? 'approve' : 'reject';
      await api.post(`/entry-requests/${selectedRequest.id}/${endpoint}/`, {
        comments: approvalComments
      });

      enqueueSnackbar(
        `Entry request ${approvalAction}d successfully`,
        { variant: 'success' }
      );

      setApprovalDialogOpen(false);
      setApprovalComments('');
      setSelectedRequest(null);
      fetchRequests();
    } catch (error) {
      console.error(`Error ${approvalAction}ing entry request:`, error);
      enqueueSnackbar(`Failed to ${approvalAction} entry request`, { variant: 'error' });
    }
  };

  const submitStoreAssignment = async () => {
    try {
      await api.post(`/entry-requests/${selectedRequest.id}/assign-store/`, {
        store_id: selectedStore
      });

      enqueueSnackbar('Entry request assigned to store successfully', { variant: 'success' });
      setAssignDialogOpen(false);
      setSelectedStore('');
      setSelectedRequest(null);
      fetchRequests();
    } catch (error) {
      console.error('Error assigning entry request to store:', error);
      enqueueSnackbar('Failed to assign entry request to store', { variant: 'error' });
    }
  };

  const canApprove = (request) => {
    return request.workflow_status === 'pending';
  };

  const canAssign = (request) => {
    return request.workflow_status === 'approved';
  };

  const canEdit = (request) => {
    return ['draft', 'pending'].includes(request.workflow_status);
  };

  const canDelete = (request) => {
    return request.workflow_status === 'draft';
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h5" fontWeight={600}>
              Entry Requests Management
            </Typography>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={fetchRequests}
              >
                Refresh
              </Button>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => navigate('/procurement/entry-request/new')}
              >
                New Entry Request
              </Button>
            </Box>
          </Box>

          {requests.length === 0 ? (
            <Alert severity="info">
              No entry requests found. Create your first entry request to get started.
            </Alert>
          ) : (
            <TableContainer component={Paper} variant="outlined">
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Request Code</TableCell>
                    <TableCell>Title</TableCell>
                    <TableCell>Supplier</TableCell>
                    <TableCell>PO Number</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Workflow Status</TableCell>
                    <TableCell>Requested By</TableCell>
                    <TableCell>Created Date</TableCell>
                    <TableCell align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {requests.map((request) => (
                    <TableRow key={request.id} hover>
                      <TableCell>
                        <Typography variant="body2" fontWeight={600} color="primary">
                          {request.request_code}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {request.title}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {request.supplier_name || 'N/A'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {request.po_number}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={request.status_name || 'No Status'}
                          color={getStatusColor(request.status_name)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={request.workflow_status || 'Unknown'}
                          color={getWorkflowStatusColor(request.workflow_status)}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {request.requested_by_name || 'N/A'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {new Date(request.created_at).toLocaleDateString()}
                        </Typography>
                      </TableCell>
                      <TableCell align="center">
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Tooltip title="View Details">
                            <IconButton
                              size="small"
                              onClick={() => handleViewRequest(request)}
                            >
                              <ViewIcon />
                            </IconButton>
                          </Tooltip>

                          {canEdit(request) && (
                            <Tooltip title="Edit">
                              <IconButton
                                size="small"
                                onClick={() => handleEditRequest(request)}
                              >
                                <EditIcon />
                              </IconButton>
                            </Tooltip>
                          )}

                          <Tooltip title="More Actions">
                            <IconButton
                              size="small"
                              onClick={(e) => handleActionMenuOpen(e, request)}
                            >
                              <MoreVertIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* Action Menu */}
      <Menu
        anchorEl={actionMenuAnchor}
        open={Boolean(actionMenuAnchor)}
        onClose={handleActionMenuClose}
      >
        {actionMenuRequest && canApprove(actionMenuRequest) && [
          <MenuItem key="approve" onClick={() => handleApprovalAction('approve')}>
            <ListItemIcon>
              <ApproveIcon color="success" />
            </ListItemIcon>
            <ListItemText>Approve Request</ListItemText>
          </MenuItem>,
          <MenuItem key="reject" onClick={() => handleApprovalAction('reject')}>
            <ListItemIcon>
              <RejectIcon color="error" />
            </ListItemIcon>
            <ListItemText>Reject Request</ListItemText>
          </MenuItem>
        ]}

        {actionMenuRequest && canAssign(actionMenuRequest) && (
          <MenuItem onClick={handleAssignToStore}>
            <ListItemIcon>
              <AssignIcon color="info" />
            </ListItemIcon>
            <ListItemText>Assign to Store</ListItemText>
          </MenuItem>
        )}

        {actionMenuRequest && canDelete(actionMenuRequest) && (
          <MenuItem onClick={() => {
            setSelectedRequest(actionMenuRequest);
            setDeleteDialogOpen(true);
            handleActionMenuClose();
          }}>
            <ListItemIcon>
              <DeleteIcon color="error" />
            </ListItemIcon>
            <ListItemText>Delete Request</ListItemText>
          </MenuItem>
        )}

        <MenuItem onClick={() => {
          // TODO: Implement print functionality
          handleActionMenuClose();
        }}>
          <ListItemIcon>
            <PrintIcon />
          </ListItemIcon>
          <ListItemText>Print Request</ListItemText>
        </MenuItem>
      </Menu>

      {/* Enhanced View Dialog */}
      <Dialog
        open={viewDialogOpen}
        onClose={() => setViewDialogOpen(false)}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: { height: '90vh' }
        }}
      >
        <DialogTitle sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          backgroundColor: 'primary.main',
          color: 'primary.contrastText'
        }}>
          <Box>
            <Typography variant="h6">
              Entry Request Details - {selectedRequest?.request_code}
            </Typography>
            <Typography variant="body2" sx={{ opacity: 0.8 }}>
              Complete request information and management
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            {selectedRequest && canEdit(selectedRequest) && (
              <Button
                variant="outlined"
                size="small"
                startIcon={<EditIcon />}
                onClick={() => {
                  setViewDialogOpen(false);
                  handleEditRequest(selectedRequest);
                }}
                sx={{ color: 'white', borderColor: 'white' }}
              >
                Edit
              </Button>
            )}
            {selectedRequest && canDelete(selectedRequest) && (
              <Button
                variant="outlined"
                size="small"
                startIcon={<DeleteIcon />}
                onClick={() => {
                  setViewDialogOpen(false);
                  setDeleteDialogOpen(true);
                }}
                sx={{ color: 'white', borderColor: 'white' }}
              >
                Delete
              </Button>
            )}
            <Button
              variant="outlined"
              size="small"
              startIcon={<PrintIcon />}
              sx={{ color: 'white', borderColor: 'white' }}
            >
              Print
            </Button>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ p: 0 }}>
          {selectedRequest && (
            <Box sx={{ height: '100%', overflow: 'auto' }}>
              {/* Basic Information Section */}
              <Card sx={{ m: 2, mb: 1 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom color="primary" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <ViewIcon />
                    Basic Information
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Request Code</Typography>
                      <Typography variant="body1" fontWeight={600} gutterBottom>{selectedRequest.request_code}</Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Status</Typography>
                      <Box sx={{ mt: 0.5 }}>
                        <Chip
                          label={selectedRequest.status_name || 'No Status'}
                          color={getStatusColor(selectedRequest.status_name)}
                          size="small"
                        />
                        <Chip
                          label={selectedRequest.workflow_status}
                          color={getWorkflowStatusColor(selectedRequest.workflow_status)}
                          size="small"
                          variant="outlined"
                          sx={{ ml: 1 }}
                        />
                      </Box>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Title</Typography>
                      <Typography variant="body1" gutterBottom>{selectedRequest.title}</Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">PO Number</Typography>
                      <Typography variant="body1" gutterBottom>{selectedRequest.po_number}</Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">PO Date</Typography>
                      <Typography variant="body1" gutterBottom>
                        {selectedRequest.po_date ? new Date(selectedRequest.po_date).toLocaleDateString() : 'N/A'}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Supplier</Typography>
                      <Typography variant="body1" gutterBottom>{selectedRequest.supplier_name}</Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Main Classification</Typography>
                      <Typography variant="body1" gutterBottom>{selectedRequest.main_classification_name || 'N/A'}</Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Target Store</Typography>
                      <Typography variant="body1" gutterBottom>{selectedRequest.target_store_name || 'N/A'}</Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Expected Delivery Date</Typography>
                      <Typography variant="body1" gutterBottom>
                        {selectedRequest.expected_delivery_date ? new Date(selectedRequest.expected_delivery_date).toLocaleDateString() : 'N/A'}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Is Urgent</Typography>
                      <Chip
                        label={selectedRequest.is_urgent ? 'Yes' : 'No'}
                        color={selectedRequest.is_urgent ? 'error' : 'default'}
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" color="text.secondary">Description</Typography>
                      <Typography variant="body1" gutterBottom>
                        {selectedRequest.description || 'No description provided'}
                      </Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" color="text.secondary">Additional Notes</Typography>
                      <Typography variant="body1" gutterBottom>
                        {selectedRequest.additional_notes || 'No additional notes'}
                      </Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" color="text.secondary">Delivery Note</Typography>
                      <Typography variant="body1" gutterBottom>
                        {selectedRequest.delivery_note || 'No delivery notes'}
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>

              {/* Items Section */}
              <Card sx={{ m: 2, mb: 1 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom color="primary" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <ListIcon />
                    Items List ({selectedRequest.items?.length || 0} items)
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                  {selectedRequest.items && selectedRequest.items.length > 0 ? (
                    <TableContainer component={Paper} variant="outlined">
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Item Code</TableCell>
                            <TableCell>Description</TableCell>
                            <TableCell>Specifications</TableCell>
                            <TableCell align="right">Quantity</TableCell>
                            <TableCell align="right">Unit Price</TableCell>
                            <TableCell align="right">Total</TableCell>
                            <TableCell>Classification</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {selectedRequest.items.map((item, index) => (
                            <TableRow key={item.id || index}>
                              <TableCell>
                                <Chip
                                  label={item.item_code || `PRE-${String(index + 1).padStart(3, '0')}`}
                                  size="small"
                                  color="primary"
                                  variant="outlined"
                                />
                              </TableCell>
                              <TableCell>{item.item_description}</TableCell>
                              <TableCell>{item.specifications || 'N/A'}</TableCell>
                              <TableCell align="right">{item.quantity}</TableCell>
                              <TableCell align="right">
                                {item.unit_price ? `$${parseFloat(item.unit_price).toFixed(2)}` : 'N/A'}
                              </TableCell>
                              <TableCell align="right">
                                {item.unit_price ? `$${(parseFloat(item.unit_price) * item.quantity).toFixed(2)}` : 'N/A'}
                              </TableCell>
                              <TableCell>{item.main_classification_name || 'N/A'}</TableCell>
                            </TableRow>
                          ))}
                          <TableRow>
                            <TableCell colSpan={5} align="right">
                              <Typography variant="subtitle2" fontWeight={600}>Total Items:</Typography>
                            </TableCell>
                            <TableCell align="right">
                              <Typography variant="subtitle2" fontWeight={600}>
                                {selectedRequest.items.reduce((sum, item) => sum + item.quantity, 0)}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Typography variant="subtitle2" fontWeight={600}>
                                Total Value: ${selectedRequest.items.reduce((sum, item) =>
                                  sum + (parseFloat(item.unit_price || 0) * item.quantity), 0
                                ).toFixed(2)}
                              </Typography>
                            </TableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                    </TableContainer>
                  ) : (
                    <Alert severity="info">No items added to this request yet.</Alert>
                  )}
                </CardContent>
              </Card>

              {/* Attachments Section */}
              <Card sx={{ m: 2, mb: 1 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom color="primary" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <AttachFileIcon />
                    Attachments ({selectedRequest.attachments?.length || 0} files)
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                  {selectedRequest.attachments && selectedRequest.attachments.length > 0 ? (
                    <Grid container spacing={2}>
                      {selectedRequest.attachments.map((attachment, index) => (
                        <Grid item xs={12} sm={6} md={4} key={attachment.id || index}>
                          <Paper
                            variant="outlined"
                            sx={{
                              p: 2,
                              display: 'flex',
                              alignItems: 'center',
                              gap: 1,
                              '&:hover': { backgroundColor: 'action.hover' }
                            }}
                          >
                            <AttachFileIcon color="primary" />
                            <Box sx={{ flexGrow: 1, minWidth: 0 }}>
                              <Typography variant="body2" noWrap>
                                {attachment.file_name || attachment.name || `Attachment ${index + 1}`}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {attachment.file_type || 'Unknown type'} • {attachment.file_size || 'Unknown size'}
                              </Typography>
                            </Box>
                            <IconButton
                              size="small"
                              onClick={() => {
                                // TODO: Implement file download
                                console.log('Download file:', attachment);
                              }}
                            >
                              <ViewIcon />
                            </IconButton>
                          </Paper>
                        </Grid>
                      ))}
                    </Grid>
                  ) : (
                    <Alert severity="info">No attachments uploaded for this request.</Alert>
                  )}
                </CardContent>
              </Card>

              {/* Workflow History Section */}
              <Card sx={{ m: 2, mb: 1 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom color="primary" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <AssignIcon />
                    Workflow History & Tracking
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Requested By</Typography>
                      <Typography variant="body1" gutterBottom>{selectedRequest.requested_by_name}</Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Created Date</Typography>
                      <Typography variant="body1" gutterBottom>
                        {new Date(selectedRequest.created_at).toLocaleString()}
                      </Typography>
                    </Grid>
                    {selectedRequest.approved_by_name && (
                      <>
                        <Grid item xs={12} md={6}>
                          <Typography variant="subtitle2" color="text.secondary">Approved By</Typography>
                          <Typography variant="body1" gutterBottom>{selectedRequest.approved_by_name}</Typography>
                        </Grid>
                        <Grid item xs={12} md={6}>
                          <Typography variant="subtitle2" color="text.secondary">Approval Date</Typography>
                          <Typography variant="body1" gutterBottom>
                            {selectedRequest.approval_date ? new Date(selectedRequest.approval_date).toLocaleString() : 'N/A'}
                          </Typography>
                        </Grid>
                      </>
                    )}
                    {selectedRequest.approval_comments && (
                      <Grid item xs={12}>
                        <Typography variant="subtitle2" color="text.secondary">Approval Comments</Typography>
                        <Paper variant="outlined" sx={{ p: 2, backgroundColor: 'action.hover' }}>
                          <Typography variant="body1">{selectedRequest.approval_comments}</Typography>
                        </Paper>
                      </Grid>
                    )}
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Last Updated</Typography>
                      <Typography variant="body1" gutterBottom>
                        {new Date(selectedRequest.updated_at).toLocaleString()}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Total Items Count</Typography>
                      <Typography variant="body1" gutterBottom>
                        {selectedRequest.total_items_count || selectedRequest.items?.length || 0}
                      </Typography>
                    </Grid>
                    {selectedRequest.received_items_count !== undefined && (
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" color="text.secondary">Received Items Count</Typography>
                        <Typography variant="body1" gutterBottom>
                          {selectedRequest.received_items_count}
                        </Typography>
                      </Grid>
                    )}
                  </Grid>
                </CardContent>
              </Card>

              {/* Action Buttons Section */}
              <Card sx={{ m: 2, mb: 2 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom color="primary" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <MoreVertIcon />
                    Available Actions
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                  <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                    {canEdit(selectedRequest) && (
                      <Button
                        variant="contained"
                        startIcon={<EditIcon />}
                        onClick={() => {
                          setViewDialogOpen(false);
                          handleEditRequest(selectedRequest);
                        }}
                      >
                        Edit Request
                      </Button>
                    )}

                    {canApprove(selectedRequest) && (
                      <>
                        <Button
                          variant="contained"
                          color="success"
                          startIcon={<ApproveIcon />}
                          onClick={() => {
                            setViewDialogOpen(false);
                            handleApprovalAction('approve');
                          }}
                        >
                          Approve Request
                        </Button>
                        <Button
                          variant="contained"
                          color="error"
                          startIcon={<RejectIcon />}
                          onClick={() => {
                            setViewDialogOpen(false);
                            handleApprovalAction('reject');
                          }}
                        >
                          Reject Request
                        </Button>
                      </>
                    )}

                    {canAssign(selectedRequest) && (
                      <Button
                        variant="contained"
                        color="info"
                        startIcon={<AssignIcon />}
                        onClick={() => {
                          setViewDialogOpen(false);
                          handleAssignToStore();
                        }}
                      >
                        Assign to Store
                      </Button>
                    )}

                    {canDelete(selectedRequest) && (
                      <Button
                        variant="outlined"
                        color="error"
                        startIcon={<DeleteIcon />}
                        onClick={() => {
                          setViewDialogOpen(false);
                          setDeleteDialogOpen(true);
                        }}
                      >
                        Delete Request
                      </Button>
                    )}

                    <Button
                      variant="outlined"
                      startIcon={<PrintIcon />}
                      onClick={() => {
                        // TODO: Implement print functionality
                        console.log('Print request:', selectedRequest);
                      }}
                    >
                      Print Request
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button onClick={() => setViewDialogOpen(false)} variant="outlined">
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {/* Approval Dialog */}
      <Dialog
        open={approvalDialogOpen}
        onClose={() => setApprovalDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          {approvalAction === 'approve' ? 'Approve' : 'Reject'} Entry Request
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" gutterBottom>
            Are you sure you want to {approvalAction} the entry request "{selectedRequest?.request_code}"?
          </Typography>
          <TextField
            fullWidth
            multiline
            rows={3}
            label="Comments"
            value={approvalComments}
            onChange={(e) => setApprovalComments(e.target.value)}
            placeholder={`Enter ${approvalAction} comments...`}
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setApprovalDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={submitApproval}
            variant="contained"
            color={approvalAction === 'approve' ? 'success' : 'error'}
          >
            {approvalAction === 'approve' ? 'Approve' : 'Reject'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Store Assignment Dialog */}
      <Dialog
        open={assignDialogOpen}
        onClose={() => setAssignDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Assign Entry Request to Store</DialogTitle>
        <DialogContent>
          <Typography variant="body1" gutterBottom>
            Assign entry request "{selectedRequest?.request_code}" to a store for processing.
          </Typography>
          <FormControl fullWidth sx={{ mt: 2 }}>
            <InputLabel>Select Store</InputLabel>
            <Select
              value={selectedStore}
              onChange={(e) => setSelectedStore(e.target.value)}
              label="Select Store"
            >
              {stores.map((store) => (
                <MenuItem key={store.id} value={store.id}>
                  {store.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAssignDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={submitStoreAssignment}
            variant="contained"
            disabled={!selectedStore}
          >
            Assign to Store
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        maxWidth="sm"
      >
        <DialogTitle>Delete Entry Request</DialogTitle>
        <DialogContent>
          <Typography variant="body1">
            Are you sure you want to delete the entry request "{selectedRequest?.request_code}"?
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleDeleteRequest}
            variant="contained"
            color="error"
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default EntryRequestsList;
