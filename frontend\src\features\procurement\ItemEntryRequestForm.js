import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Alert,
  Chip,
  Card,
  CardContent,
  CardHeader,
  Divider,
  FormHelperText,
  Checkbox,
  FormControlLabel
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  AttachFile as AttachFileIcon,
  Warning as WarningIcon,
  Save as SaveIcon,
  Send as SendIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { useSnackbar } from 'notistack';
import { useParams, useNavigate } from 'react-router-dom';
import api from '../../utils/axios';

const ItemEntryRequestForm = () => {
  const { enqueueSnackbar } = useSnackbar();
  const { id } = useParams();
  const navigate = useNavigate();
  const isEditMode = Boolean(id);

  const [loading, setLoading] = useState(false);
  const [suppliers, setSuppliers] = useState([]);
  const [classifications, setClassifications] = useState([]);
  const [existingRequest, setExistingRequest] = useState(null);

  // Form state
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    po_number: '',
    po_date: new Date(),
    supplier: '',
    main_classification: '',
    expected_delivery_date: null,
    delivery_note: '',
    additional_notes: '',
    is_urgent: false
  });

  // Items state
  const [items, setItems] = useState([
    {
      item_description: '',
      specifications: '',
      quantity: 1,
      unit_price: '',
      main_classification: ''
    }
  ]);

  // Attachments state
  const [attachments, setAttachments] = useState([]);
  const [inspectionWarning, setInspectionWarning] = useState(false);

  // Load existing request data for edit mode
  const loadExistingRequest = async () => {
    if (!isEditMode) return;

    try {
      setLoading(true);
      const response = await api.get(`/entry-requests/${id}/`);
      const request = response.data;
      setExistingRequest(request);

      // Populate form data
      setFormData({
        title: request.title || '',
        description: request.description || '',
        po_number: request.po_number || '',
        po_date: request.po_date ? new Date(request.po_date) : new Date(),
        supplier: request.supplier?.id || request.supplier || '',
        main_classification: request.main_classification?.id || request.main_classification || '',
        expected_delivery_date: request.expected_delivery_date ? new Date(request.expected_delivery_date) : null,
        delivery_note: request.delivery_note || '',
        additional_notes: request.additional_notes || '',
        is_urgent: request.is_urgent || false
      });

      // Load items if any
      if (request.items && request.items.length > 0) {
        setItems(request.items.map(item => ({
          id: item.id,
          item_description: item.item_description || '',
          specifications: item.specifications || '',
          quantity: item.quantity || 1,
          unit_price: item.unit_price || '',
          main_classification: item.main_classification?.id || item.main_classification || ''
        })));
      }

      // Load attachments if any
      if (request.attachments && request.attachments.length > 0) {
        setAttachments(request.attachments.map(attachment => ({
          id: attachment.id,
          name: attachment.file_name,
          size: attachment.file_size,
          type: attachment.file_type,
          file_path: attachment.file_path,
          existing: true // Mark as existing file
        })));
      }

    } catch (error) {
      console.error('Error loading existing request:', error);
      enqueueSnackbar('Failed to load request data', { variant: 'error' });
      navigate('/entry-requests');
    } finally {
      setLoading(false);
    }
  };

  // Load dropdown data
  useEffect(() => {
    const loadData = async () => {
      try {
        const [suppliersRes, classificationsRes] = await Promise.all([
          api.get('/suppliers/'),
          api.get('/main-classifications/')
        ]);

        setSuppliers(suppliersRes.data.results || suppliersRes.data || []);
        setClassifications(classificationsRes.data.results || classificationsRes.data || []);

        // Load existing request after dropdown data is loaded
        await loadExistingRequest();
      } catch (error) {
        console.error('Error loading data:', error);
        enqueueSnackbar('Failed to load form data', { variant: 'error' });
      }
    };

    loadData();
  }, [id, isEditMode, enqueueSnackbar, navigate]);

  // Handle form field changes
  const handleFormChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle item changes
  const handleItemChange = (index, field, value) => {
    const newItems = [...items];
    newItems[index] = {
      ...newItems[index],
      [field]: value
    };
    setItems(newItems);

    // Check for inspection warning
    if (field === 'main_classification') {
      checkInspectionNeeded(newItems);
    }
  };

  // Add new item row
  const addItem = () => {
    setItems([...items, {
      item_description: '',
      specifications: '',
      quantity: 1,
      unit_price: '',
      main_classification: ''
    }]);
  };

  // Remove item row
  const removeItem = (index) => {
    if (items.length > 1) {
      const newItems = items.filter((_, i) => i !== index);
      setItems(newItems);
      checkInspectionNeeded(newItems);
    }
  };

  // Check if inspection is needed
  const checkInspectionNeeded = (itemsList) => {
    const needsInspection = itemsList.some(item => {
      const classification = classifications.find(c => c.id === item.main_classification);
      return classification && ['Medical Equipment', 'Lab Equipment', 'Technical Equipment'].includes(classification.name);
    });
    setInspectionWarning(needsInspection);
  };

  // Handle file upload
  const handleFileUpload = (event) => {
    const files = Array.from(event.target.files);
    const newAttachments = files.map(file => ({
      file,
      name: file.name,
      size: file.size,
      type: file.type
    }));
    setAttachments(prev => [...prev, ...newAttachments]);
  };

  // Remove attachment
  const removeAttachment = (index) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };



  // Submit form
  const handleSubmit = async (isDraft = false) => {
    setLoading(true);
    try {
      // Validate required fields
      if (!formData.title || !formData.po_number || !formData.supplier) {
        enqueueSnackbar('Please fill in all required fields', { variant: 'error' });
        return;
      }

      if (items.some(item => !item.item_description || !item.quantity)) {
        enqueueSnackbar('Please complete all item details', { variant: 'error' });
        return;
      }

      // Try to get a default status for the entry request (optional)
      let defaultStatus = null;
      try {
        const statusResponse = await api.get('/approval-statuses/');
        const statuses = statusResponse.data.results || statusResponse.data || [];
        defaultStatus = statuses.find(s => s.name === 'Pending') || statuses[0];
      } catch (statusError) {
        console.warn('Could not fetch statuses:', statusError);
        // Continue without status - it's now optional
      }



      // Prepare the main entry request data
      const entryRequestData = {
        title: formData.title,
        description: formData.description || '',
        po_number: formData.po_number,
        po_date: formData.po_date?.toISOString().split('T')[0] || null,
        supplier: formData.supplier,
        main_classification: formData.main_classification || null,
        expected_delivery_date: formData.expected_delivery_date?.toISOString().split('T')[0] || null,
        delivery_note: formData.delivery_note || '',
        additional_notes: formData.additional_notes || '',
        is_urgent: formData.is_urgent || false
      };

      // Only add status if we have one
      if (defaultStatus?.id) {
        entryRequestData.status = defaultStatus.id;
      }

      // Only add workflow_status if not draft (let model default to 'pending')
      if (isDraft) {
        entryRequestData.workflow_status = 'draft';
      }
      // Don't send request_code - it's auto-generated by the model

      // Create or update the entry request
      let response;
      let entryRequestId;

      if (isEditMode) {
        response = await api.put(`/entry-requests/${id}/`, entryRequestData);
        entryRequestId = id;
      } else {
        response = await api.post('/entry-requests/', entryRequestData);
        entryRequestId = response.data.id;
      }

      // Then create the items
      for (const item of items) {
        const itemData = {
          entry_request: entryRequestId,
          item_description: item.item_description,
          specifications: item.specifications || '',
          quantity: parseInt(item.quantity) || 1,
          unit_price: item.unit_price ? parseFloat(item.unit_price) : null,
          main_classification: item.main_classification || null
        };

        await api.post('/entry-request-items/', itemData);
      }

      // Handle attachments if any (only upload new files, not existing ones)
      if (attachments.length > 0) {
        for (const attachment of attachments) {
          // Skip existing attachments in edit mode
          if (attachment.existing) {
            continue;
          }

          const attachmentFormData = new FormData();
          attachmentFormData.append('entry_request', entryRequestId);
          attachmentFormData.append('file', attachment.file);
          attachmentFormData.append('file_name', attachment.name);
          attachmentFormData.append('file_path', `entry_requests/${entryRequestId}/${attachment.name}`);
          attachmentFormData.append('file_type', attachment.type);
          attachmentFormData.append('file_size', attachment.size);
          attachmentFormData.append('attachment_type', 'OT'); // Default to 'Other'
          attachmentFormData.append('description', `Uploaded file: ${attachment.name}`);

          await api.post('/entry-request-attachments/', attachmentFormData, {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          });
        }
      }

      const actionText = isEditMode ? 'updated' : (isDraft ? 'saved as draft' : 'submitted');
      enqueueSnackbar(
        `Entry request ${actionText} successfully!`,
        { variant: 'success' }
      );

      // Always redirect to Item Receive Dashboard
      navigate('/procurement/item-receive');

    } catch (error) {
      console.error('Error submitting request:', error);
      console.error('Error response:', error.response?.data);

      let errorMessage = 'Failed to submit request';

      if (error.response?.data) {
        if (error.response.data.detail) {
          errorMessage = error.response.data.detail;
        } else if (error.response.data.message) {
          errorMessage = error.response.data.message;
        } else if (typeof error.response.data === 'object') {
          // Handle field-specific errors
          const fieldErrors = [];
          Object.keys(error.response.data).forEach(field => {
            const fieldError = error.response.data[field];
            if (Array.isArray(fieldError)) {
              fieldErrors.push(`${field}: ${fieldError.join(', ')}`);
            } else {
              fieldErrors.push(`${field}: ${fieldError}`);
            }
          });
          if (fieldErrors.length > 0) {
            errorMessage = fieldErrors.join('; ');
          }
        }
      }

      enqueueSnackbar(errorMessage, { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box sx={{ p: 3 }}>
        <Card>
          <CardHeader
            title={isEditMode ? `Edit Entry Request - ${existingRequest?.request_code}` : "Item Entry Request - Pre-Registration"}
            subheader={isEditMode ? "Update Procurement Officer Pre-Registration" : "Procurement Officer Pre-Registration for Inventory Receiving"}
            sx={{
              backgroundColor: 'primary.main',
              color: 'primary.contrastText',
              '& .MuiCardHeader-subheader': {
                color: 'primary.contrastText',
                opacity: 0.8
              }
            }}
          />

          <CardContent>
            {/* Basic Information */}
            <Typography variant="h6" gutterBottom>
              Basic Information
            </Typography>

            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Request Title"
                  value={formData.title}
                  onChange={(e) => handleFormChange('title', e.target.value)}
                  required
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Purchase Order Number"
                  value={formData.po_number}
                  onChange={(e) => handleFormChange('po_number', e.target.value)}
                  placeholder="PO-2024-XXXX"
                  required
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <DatePicker
                  label="PO Date"
                  value={formData.po_date}
                  onChange={(date) => handleFormChange('po_date', date)}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth required>
                  <InputLabel>Supplier</InputLabel>
                  <Select
                    value={formData.supplier}
                    onChange={(e) => handleFormChange('supplier', e.target.value)}
                    label="Supplier"
                  >
                    {suppliers.map((supplier) => (
                      <MenuItem key={supplier.id} value={supplier.id}>
                        {supplier.company_name || supplier.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  value={formData.description}
                  onChange={(e) => handleFormChange('description', e.target.value)}
                  multiline
                  rows={2}
                />
              </Grid>
            </Grid>

            <Divider sx={{ my: 3 }} />

            {/* Items Section */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                Items to Pre-Register
              </Typography>
              <Button
                startIcon={<AddIcon />}
                onClick={addItem}
                variant="outlined"
                size="small"
              >
                Add Item
              </Button>
            </Box>

            {inspectionWarning && (
              <Alert
                severity="warning"
                icon={<WarningIcon />}
                sx={{ mb: 2 }}
              >
                Some items may require technical inspection. The system will automatically flag these for inspection committee review.
              </Alert>
            )}

            <TableContainer component={Paper} variant="outlined">
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Item Code</TableCell>
                    <TableCell>Description *</TableCell>
                    <TableCell>Quantity *</TableCell>
                    <TableCell>Unit Price</TableCell>
                    <TableCell>Classification</TableCell>
                    <TableCell>Action</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {items.map((item, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <Chip
                          label={`PRE-${String(index + 1).padStart(3, '0')}`}
                          size="small"
                          color="primary"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <TextField
                          size="small"
                          value={item.item_description}
                          onChange={(e) => handleItemChange(index, 'item_description', e.target.value)}
                          placeholder="Item description"
                          required
                          fullWidth
                        />
                      </TableCell>
                      <TableCell>
                        <TextField
                          size="small"
                          type="number"
                          value={item.quantity}
                          onChange={(e) => handleItemChange(index, 'quantity', parseInt(e.target.value) || 1)}
                          inputProps={{ min: 1 }}
                          required
                          sx={{ width: 80 }}
                        />
                      </TableCell>
                      <TableCell>
                        <TextField
                          size="small"
                          type="number"
                          value={item.unit_price}
                          onChange={(e) => handleItemChange(index, 'unit_price', e.target.value)}
                          placeholder="0.00"
                          inputProps={{ step: 0.01 }}
                          sx={{ width: 100 }}
                        />
                      </TableCell>
                      <TableCell>
                        <FormControl size="small" sx={{ minWidth: 150 }}>
                          <Select
                            value={item.main_classification}
                            onChange={(e) => handleItemChange(index, 'main_classification', e.target.value)}
                            displayEmpty
                          >
                            <MenuItem value="">
                              <em>Select Classification</em>
                            </MenuItem>
                            {classifications.map((classification) => (
                              <MenuItem key={classification.id} value={classification.id}>
                                {classification.name}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      </TableCell>
                      <TableCell>
                        <IconButton
                          onClick={() => removeItem(index)}
                          disabled={items.length === 1}
                          size="small"
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

            <Divider sx={{ my: 3 }} />

            {/* Document Upload */}
            <Typography variant="h6" gutterBottom>
              Supporting Documents
            </Typography>

            <Box sx={{ mb: 2 }}>
              <input
                accept=".pdf,.doc,.docx,.xls,.xlsx"
                style={{ display: 'none' }}
                id="file-upload"
                multiple
                type="file"
                onChange={handleFileUpload}
              />
              <label htmlFor="file-upload">
                <Button
                  variant="outlined"
                  component="span"
                  startIcon={<AttachFileIcon />}
                >
                  Upload Documents
                </Button>
              </label>
              <FormHelperText>
                Accepted: PO copy, bid documents, specifications (PDF/Word/Excel)
              </FormHelperText>
            </Box>

            {attachments.length > 0 && (
              <Box sx={{ mb: 2 }}>
                {attachments.map((attachment, index) => (
                  <Chip
                    key={index}
                    label={attachment.name}
                    onDelete={() => removeAttachment(index)}
                    sx={{ mr: 1, mb: 1 }}
                  />
                ))}
              </Box>
            )}

            <Divider sx={{ my: 3 }} />

            {/* Additional Information */}
            <Typography variant="h6" gutterBottom>
              Additional Information
            </Typography>

            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <DatePicker
                  label="Expected Delivery Date"
                  value={formData.expected_delivery_date}
                  onChange={(date) => handleFormChange('expected_delivery_date', date)}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Technical Specifications (If Applicable)"
                  value={formData.additional_notes}
                  onChange={(e) => handleFormChange('additional_notes', e.target.value)}
                  multiline
                  rows={3}
                  placeholder="Detailed specs, model numbers, compliance requirements..."
                />
              </Grid>

              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={formData.is_urgent}
                      onChange={(e) => handleFormChange('is_urgent', e.target.checked)}
                      name="is_urgent"
                    />
                  }
                  label="Mark as Urgent Request"
                />
              </Grid>
            </Grid>

            {/* Submit Buttons */}
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 4 }}>
              <Button
                variant="outlined"
                onClick={() => navigate('/procurement/item-receive')}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                variant="contained"
                startIcon={<SendIcon />}
                onClick={() => handleSubmit(false)}
                disabled={loading}
              >
                {isEditMode ? 'Update Request' : 'Submit Pre-Registration'}
              </Button>
            </Box>
          </CardContent>
        </Card>
      </Box>
    </LocalizationProvider>
  );
};

export default ItemEntryRequestForm;
