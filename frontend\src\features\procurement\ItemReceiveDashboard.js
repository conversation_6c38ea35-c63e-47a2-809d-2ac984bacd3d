import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Tabs,
  Tab,
  Badge,
  Divider,
  Tooltip,
  Menu,
  ListItemIcon,
  ListItemText,
  CardHeader,
  Avatar,
  CircularProgress
} from '@mui/material';
import {
  Add as AddIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  Assignment as AssignIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Refresh as RefreshIcon,
  MoreVert as MoreVertIcon,
  AttachFile as AttachFileIcon,
  List as ListIcon,
  Delete as DeleteIcon,
  Print as PrintIcon,
  TrendingUp as TrendingUpIcon,
  PendingActions as PendingIcon,
  Done as DoneIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { useNavigate } from 'react-router-dom';
import api from '../../utils/axios';

const ItemReceiveDashboard = () => {
  const { enqueueSnackbar } = useSnackbar();
  const navigate = useNavigate();

  const [loading, setLoading] = useState(false);
  const [requests, setRequests] = useState([]);
  const [filteredRequests, setFilteredRequests] = useState([]);
  const [currentTab, setCurrentTab] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  // Dialog states
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [approvalDialogOpen, setApprovalDialogOpen] = useState(false);
  const [assignDialogOpen, setAssignDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [actionMenuAnchor, setActionMenuAnchor] = useState(null);
  const [actionMenuRequest, setActionMenuRequest] = useState(null);

  // Form states
  const [approvalComments, setApprovalComments] = useState('');
  const [approvalAction, setApprovalAction] = useState(''); // 'approve' or 'reject'
  const [stores, setStores] = useState([]);
  const [selectedStore, setSelectedStore] = useState('');

  // Statistics
  const [stats, setStats] = useState({
    pending: 0,
    approved: 0,
    assigned: 0,
    inspecting: 0,
    completed: 0,
    rejected: 0
  });

  // Load data
  useEffect(() => {
    loadRequests();
    loadStores();
  }, []);

  // Filter requests based on tab and search
  useEffect(() => {
    let filtered = requests;

    // Filter by tab - treat null/undefined workflow_status as pending
    if (currentTab === 1) filtered = filtered.filter(r => !r.workflow_status || r.workflow_status === 'pending');
    else if (currentTab === 2) filtered = filtered.filter(r => r.workflow_status === 'approved');
    else if (currentTab === 3) filtered = filtered.filter(r => r.workflow_status === 'assigned');
    else if (currentTab === 4) filtered = filtered.filter(r => r.workflow_status === 'inspecting');
    else if (currentTab === 5) filtered = filtered.filter(r => r.workflow_status === 'completed');

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(r =>
        r.request_code.toLowerCase().includes(searchTerm.toLowerCase()) ||
        r.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        r.po_number.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(r => r.workflow_status === statusFilter);
    }

    setFilteredRequests(filtered);
  }, [requests, currentTab, searchTerm, statusFilter]);

  const loadRequests = async () => {
    setLoading(true);
    try {
      const response = await api.get('/entry-requests/');
      const requestsData = response.data.results || response.data || [];
      setRequests(requestsData);

      // Calculate statistics - treat null/undefined workflow_status as pending
      const newStats = {
        pending: requestsData.filter(r => !r.workflow_status || r.workflow_status === 'pending').length,
        approved: requestsData.filter(r => r.workflow_status === 'approved').length,
        assigned: requestsData.filter(r => r.workflow_status === 'assigned').length,
        inspecting: requestsData.filter(r => r.workflow_status === 'inspecting').length,
        completed: requestsData.filter(r => r.workflow_status === 'completed').length,
        rejected: requestsData.filter(r => r.workflow_status === 'rejected').length,
      };
      setStats(newStats);

    } catch (error) {
      console.error('Error loading requests:', error);
      enqueueSnackbar('Failed to load requests', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // Load detailed request data for viewing
  const loadRequestDetails = async (requestId) => {
    try {
      const response = await api.get(`/entry-requests/${requestId}/`);
      return response.data;
    } catch (error) {
      console.error('Error loading request details:', error);
      enqueueSnackbar('Failed to load request details', { variant: 'error' });
      return null;
    }
  };

  const loadStores = async () => {
    try {
      const response = await api.get('/stores/');
      setStores(response.data.results || response.data || []);
    } catch (error) {
      console.error('Error loading stores:', error);
    }
  };

  // Action menu handlers
  const handleActionMenuOpen = (event, request) => {
    setActionMenuAnchor(event.currentTarget);
    setActionMenuRequest(request);
  };

  const handleActionMenuClose = () => {
    setActionMenuAnchor(null);
    setActionMenuRequest(null);
  };

  const handleViewRequest = async (request) => {
    const detailedRequest = await loadRequestDetails(request.id);
    if (detailedRequest) {
      setSelectedRequest(detailedRequest);
      setViewDialogOpen(true);
    }
  };

  const handleEditRequest = (request) => {
    navigate(`/procurement/entry-request/edit/${request.id}`);
  };

  const handleDeleteRequest = async () => {
    try {
      await api.delete(`/entry-requests/${selectedRequest.id}/`);
      enqueueSnackbar('Request deleted successfully', { variant: 'success' });
      setDeleteDialogOpen(false);
      setSelectedRequest(null);
      loadRequests();
    } catch (error) {
      console.error('Error deleting request:', error);
      enqueueSnackbar('Failed to delete request', { variant: 'error' });
    }
  };

  const handleApprovalAction = (action) => {
    setApprovalAction(action);
    setSelectedRequest(actionMenuRequest);
    setApprovalDialogOpen(true);
    handleActionMenuClose();
  };

  const handleAssignAction = () => {
    setSelectedRequest(actionMenuRequest);
    setAssignDialogOpen(true);
    handleActionMenuClose();
  };

  const submitApproval = async () => {
    try {
      const endpoint = approvalAction === 'approve' ? 'approve' : 'reject';
      await api.post(`/entry-requests/${selectedRequest.id}/${endpoint}/`, {
        comments: approvalComments
      });

      enqueueSnackbar(
        `Request ${approvalAction}d successfully`,
        { variant: 'success' }
      );

      setApprovalDialogOpen(false);
      setApprovalComments('');
      setSelectedRequest(null);
      loadRequests();
    } catch (error) {
      console.error(`Error ${approvalAction}ing request:`, error);
      enqueueSnackbar(`Failed to ${approvalAction} request`, { variant: 'error' });
    }
  };

  const submitStoreAssignment = async () => {
    try {
      await api.post(`/entry-requests/${selectedRequest.id}/assign_to_store/`, {
        store_id: selectedStore
      });

      enqueueSnackbar('Request assigned to store successfully', { variant: 'success' });
      setAssignDialogOpen(false);
      setSelectedStore('');
      setSelectedRequest(null);
      loadRequests();
    } catch (error) {
      console.error('Error assigning request to store:', error);
      enqueueSnackbar('Failed to assign request to store', { variant: 'error' });
    }
  };

  // Permission checks
  const canApprove = (request) => {
    return !request.workflow_status || request.workflow_status === 'pending';
  };

  const canAssign = (request) => {
    return request.workflow_status === 'approved';
  };

  const canEdit = (request) => {
    return ['draft', 'pending'].includes(request.workflow_status) || !request.workflow_status;
  };

  const canDelete = (request) => {
    return request.workflow_status === 'draft' || !request.workflow_status;
  };

  // Handle attachment download/view
  const handleDownloadAttachment = async (attachment) => {
    try {
      console.log('Attachment object:', attachment);

      // Try different possible file path sources
      let filePath = null;

      if (attachment.file) {
        // If there's a file field (Django FileField)
        filePath = attachment.file;
      } else if (attachment.file_path) {
        // If there's a file_path field
        filePath = attachment.file_path;
      }

      if (filePath) {
        // Create download URL - media files are served at /media/ (not /api/media/)
        // Use the Django server base URL without the /api/v1 prefix
        const baseUrl = 'http://127.0.0.1:8000'; // Match the Django server

        // Remove any leading slash and ensure proper path
        const cleanPath = filePath.startsWith('/') ? filePath.substring(1) : filePath;
        const downloadUrl = `${baseUrl}/media/${cleanPath}`;

        console.log('File path:', filePath);
        console.log('Clean path:', cleanPath);
        console.log('Download URL:', downloadUrl);

        // Try to fetch the file first to check if it exists
        try {
          const response = await fetch(downloadUrl, { method: 'HEAD' });
          if (response.ok) {
            // File exists, open it
            window.open(downloadUrl, '_blank');
          } else {
            console.error('File not found at:', downloadUrl);
            enqueueSnackbar('File not found on server. This may be an older attachment that was not properly uploaded.', {
              variant: 'warning',
              autoHideDuration: 6000
            });
          }
        } catch (fetchError) {
          console.error('Error checking file existence:', fetchError);
          enqueueSnackbar('Unable to access file. Please check your connection or contact support.', {
            variant: 'error',
            autoHideDuration: 6000
          });
        }
      } else {
        console.error('No file path found in attachment:', attachment);
        enqueueSnackbar('File path not available', { variant: 'error' });
      }
    } catch (error) {
      console.error('Error downloading attachment:', error);
      enqueueSnackbar('Failed to download file', { variant: 'error' });
    }
  };

  // Handle print functionality
  const handlePrintRequest = (request) => {
    const printWindow = window.open('', '_blank');
    const printContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Entry Request - ${request.request_code}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .section { margin-bottom: 20px; }
            .section-title { font-weight: bold; font-size: 16px; margin-bottom: 10px; border-bottom: 1px solid #ccc; }
            .field { margin-bottom: 8px; }
            .field-label { font-weight: bold; display: inline-block; width: 150px; }
            table { width: 100%; border-collapse: collapse; margin-top: 10px; }
            th, td { border: 1px solid #ccc; padding: 8px; text-align: left; }
            th { background-color: #f5f5f5; }
            .urgent { color: red; font-weight: bold; }
            @media print { body { margin: 0; } }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>Item Entry Request</h1>
            <h2>${request.request_code}</h2>
            ${request.is_urgent ? '<p class="urgent">*** URGENT REQUEST ***</p>' : ''}
          </div>

          <div class="section">
            <div class="section-title">Basic Information</div>
            <div class="field"><span class="field-label">Title:</span> ${request.title}</div>
            <div class="field"><span class="field-label">PO Number:</span> ${request.po_number}</div>
            <div class="field"><span class="field-label">PO Date:</span> ${request.po_date ? new Date(request.po_date).toLocaleDateString() : 'N/A'}</div>
            <div class="field"><span class="field-label">Supplier:</span> ${request.supplier?.company_name || request.supplier?.name || request.supplier_name || 'N/A'}</div>
            <div class="field"><span class="field-label">Target Store:</span> ${request.target_store?.name || request.target_store_name || 'N/A'}</div>
            <div class="field"><span class="field-label">Expected Delivery:</span> ${request.expected_delivery_date ? new Date(request.expected_delivery_date).toLocaleDateString() : 'N/A'}</div>
            <div class="field"><span class="field-label">Status:</span> ${getWorkflowStatusLabel(request.workflow_status)}</div>
            <div class="field"><span class="field-label">Description:</span> ${request.description || 'N/A'}</div>
          </div>

          ${request.items && request.items.length > 0 ? `
          <div class="section">
            <div class="section-title">Items List</div>
            <table>
              <thead>
                <tr>
                  <th>Item Code</th>
                  <th>Description</th>
                  <th>Quantity</th>
                  <th>Unit Price</th>
                  <th>Total</th>
                </tr>
              </thead>
              <tbody>
                ${request.items.map((item, index) => `
                  <tr>
                    <td>PRE-${String(index + 1).padStart(3, '0')}</td>
                    <td>${item.item_description}</td>
                    <td>${item.quantity}</td>
                    <td>${item.unit_price ? '$' + parseFloat(item.unit_price).toFixed(2) : 'N/A'}</td>
                    <td>${item.unit_price ? '$' + (parseFloat(item.unit_price) * item.quantity).toFixed(2) : 'N/A'}</td>
                  </tr>
                `).join('')}
                <tr style="font-weight: bold;">
                  <td colspan="2">Total</td>
                  <td>${request.items.reduce((sum, item) => sum + item.quantity, 0)}</td>
                  <td></td>
                  <td>$${request.items.reduce((sum, item) => sum + (parseFloat(item.unit_price || 0) * item.quantity), 0).toFixed(2)}</td>
                </tr>
              </tbody>
            </table>
          </div>
          ` : ''}

          <div class="section">
            <div class="section-title">Workflow Information</div>
            <div class="field"><span class="field-label">Requested By:</span> ${request.requested_by_name || 'N/A'}</div>
            <div class="field"><span class="field-label">Created Date:</span> ${new Date(request.created_at).toLocaleString()}</div>
            ${request.approved_by_name ? `<div class="field"><span class="field-label">Approved By:</span> ${request.approved_by_name}</div>` : ''}
            ${request.approval_date ? `<div class="field"><span class="field-label">Approval Date:</span> ${new Date(request.approval_date).toLocaleString()}</div>` : ''}
            ${request.approval_comments ? `<div class="field"><span class="field-label">Comments:</span> ${request.approval_comments}</div>` : ''}
          </div>

          <div style="margin-top: 50px; text-align: center; font-size: 12px; color: #666;">
            Printed on ${new Date().toLocaleString()}
          </div>
        </body>
      </html>
    `;

    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
  };

  const getStatusColor = (status) => {
    const colors = {
      pending: 'warning',
      approved: 'info',
      assigned: 'primary',
      inspecting: 'secondary',
      completed: 'success',
      rejected: 'error',
      draft: 'default'
    };
    return colors[status] || 'default';
  };

  const getStatusLabel = (status) => {
    const labels = {
      pending: 'Pending Approval',
      approved: 'Approved',
      assigned: 'Assigned to Store',
      inspecting: 'Under Inspection',
      completed: 'Completed',
      rejected: 'Rejected',
      draft: 'Draft'
    };
    return labels[status] || status;
  };

  const getWorkflowStatusColor = (status) => {
    return getStatusColor(status);
  };

  const getWorkflowStatusLabel = (status) => {
    return getStatusLabel(status);
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Item Receive Dashboard
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => navigate('/procurement/entry-request/new')}
        >
          New Pre-Registration
        </Button>
      </Box>

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Pending
              </Typography>
              <Typography variant="h4" color="warning.main">
                {stats.pending}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Approved
              </Typography>
              <Typography variant="h4" color="info.main">
                {stats.approved}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Assigned
              </Typography>
              <Typography variant="h4" color="primary.main">
                {stats.assigned}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Inspecting
              </Typography>
              <Typography variant="h4" color="secondary.main">
                {stats.inspecting}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Completed
              </Typography>
              <Typography variant="h4" color="success.main">
                {stats.completed}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Rejected
              </Typography>
              <Typography variant="h4" color="error.main">
                {stats.rejected}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters and Search */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="Search by code, title, or PO number..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Status Filter</InputLabel>
                <Select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  label="Status Filter"
                >
                  <MenuItem value="all">All Statuses</MenuItem>
                  <MenuItem value="pending">Pending</MenuItem>
                  <MenuItem value="approved">Approved</MenuItem>
                  <MenuItem value="assigned">Assigned</MenuItem>
                  <MenuItem value="inspecting">Inspecting</MenuItem>
                  <MenuItem value="completed">Completed</MenuItem>
                  <MenuItem value="rejected">Rejected</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={loadRequests}
                disabled={loading}
              >
                Refresh
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Tabs */}
      <Card>
        <Tabs
          value={currentTab}
          onChange={(e, newValue) => setCurrentTab(newValue)}
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab label="All" />
          <Tab
            label={
              <Badge badgeContent={stats.pending} color="warning">
                Pending
              </Badge>
            }
          />
          <Tab
            label={
              <Badge badgeContent={stats.approved} color="info">
                Approved
              </Badge>
            }
          />
          <Tab
            label={
              <Badge badgeContent={stats.assigned} color="primary">
                Assigned
              </Badge>
            }
          />
          <Tab
            label={
              <Badge badgeContent={stats.inspecting} color="secondary">
                Inspecting
              </Badge>
            }
          />
          <Tab
            label={
              <Badge badgeContent={stats.completed} color="success">
                Completed
              </Badge>
            }
          />
        </Tabs>

        {/* Requests Table */}
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Request Code</TableCell>
                <TableCell>Title</TableCell>
                <TableCell>PO Number</TableCell>
                <TableCell>Supplier</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Created Date</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredRequests.map((request) => (
                <TableRow key={request.id}>
                  <TableCell>
                    <Typography variant="body2" fontWeight="bold">
                      {request.request_code}
                    </Typography>
                  </TableCell>
                  <TableCell>{request.title}</TableCell>
                  <TableCell>{request.po_number}</TableCell>
                  <TableCell>{request.supplier_name || 'N/A'}</TableCell>
                  <TableCell>
                    <Chip
                      label={getStatusLabel(request.workflow_status || 'pending')}
                      color={getStatusColor(request.workflow_status || 'pending')}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    {new Date(request.created_at).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Tooltip title="View Details">
                        <IconButton
                          size="small"
                          onClick={() => handleViewRequest(request)}
                        >
                          <ViewIcon />
                        </IconButton>
                      </Tooltip>

                      {canEdit(request) && (
                        <Tooltip title="Edit">
                          <IconButton
                            size="small"
                            onClick={() => handleEditRequest(request)}
                          >
                            <EditIcon />
                          </IconButton>
                        </Tooltip>
                      )}

                      <Tooltip title="More Actions">
                        <IconButton
                          size="small"
                          onClick={(e) => handleActionMenuOpen(e, request)}
                        >
                          <MoreVertIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Card>

      {/* Action Menu */}
      <Menu
        anchorEl={actionMenuAnchor}
        open={Boolean(actionMenuAnchor)}
        onClose={handleActionMenuClose}
      >
        {actionMenuRequest && canApprove(actionMenuRequest) && [
          <MenuItem key="approve" onClick={() => handleApprovalAction('approve')}>
            <ListItemIcon>
              <ApproveIcon color="success" />
            </ListItemIcon>
            <ListItemText>Approve Request</ListItemText>
          </MenuItem>,
          <MenuItem key="reject" onClick={() => handleApprovalAction('reject')}>
            <ListItemIcon>
              <RejectIcon color="error" />
            </ListItemIcon>
            <ListItemText>Reject Request</ListItemText>
          </MenuItem>
        ]}

        {actionMenuRequest && canAssign(actionMenuRequest) && (
          <MenuItem onClick={handleAssignAction}>
            <ListItemIcon>
              <AssignIcon color="info" />
            </ListItemIcon>
            <ListItemText>Assign to Store</ListItemText>
          </MenuItem>
        )}

        {actionMenuRequest && canDelete(actionMenuRequest) && (
          <MenuItem onClick={() => {
            setSelectedRequest(actionMenuRequest);
            setDeleteDialogOpen(true);
            handleActionMenuClose();
          }}>
            <ListItemIcon>
              <DeleteIcon color="error" />
            </ListItemIcon>
            <ListItemText>Delete Request</ListItemText>
          </MenuItem>
        )}

        <MenuItem onClick={() => {
          handlePrintRequest(actionMenuRequest);
          handleActionMenuClose();
        }}>
          <ListItemIcon>
            <PrintIcon />
          </ListItemIcon>
          <ListItemText>Print Request</ListItemText>
        </MenuItem>
      </Menu>

      {/* Enhanced View Dialog */}
      <Dialog
        open={viewDialogOpen}
        onClose={() => setViewDialogOpen(false)}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: { height: '90vh' }
        }}
      >
        <DialogTitle sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          backgroundColor: 'primary.main',
          color: 'primary.contrastText'
        }}>
          <Box>
            <Typography variant="h6">
              Entry Request Details - {selectedRequest?.request_code}
            </Typography>
            <Typography variant="body2" sx={{ opacity: 0.8 }}>
              Complete request information and management
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            {selectedRequest && canEdit(selectedRequest) && (
              <Button
                variant="outlined"
                size="small"
                startIcon={<EditIcon />}
                onClick={() => {
                  setViewDialogOpen(false);
                  handleEditRequest(selectedRequest);
                }}
                sx={{ color: 'white', borderColor: 'white' }}
              >
                Edit
              </Button>
            )}
            <Button
              variant="outlined"
              size="small"
              startIcon={<PrintIcon />}
              onClick={() => handlePrintRequest(selectedRequest)}
              sx={{ color: 'white', borderColor: 'white' }}
            >
              Print
            </Button>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ p: 0 }}>
          {selectedRequest && (
            <Box sx={{ height: '100%', overflow: 'auto' }}>
              {/* Basic Information Section */}
              <Card sx={{ m: 2, mb: 1 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom color="primary" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <ViewIcon />
                    Basic Information
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Request Code</Typography>
                      <Typography variant="body1" fontWeight={600} gutterBottom>{selectedRequest.request_code}</Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Status</Typography>
                      <Box sx={{ mt: 0.5 }}>
                        <Chip
                          label={getWorkflowStatusLabel(selectedRequest.workflow_status)}
                          color={getWorkflowStatusColor(selectedRequest.workflow_status)}
                          size="small"
                        />
                        {selectedRequest.status_name && (
                          <Chip
                            label={`Approval: ${selectedRequest.status_name}`}
                            color={getStatusColor(selectedRequest.status_name.toLowerCase())}
                            size="small"
                            variant="outlined"
                            sx={{ ml: 1 }}
                          />
                        )}
                      </Box>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Title</Typography>
                      <Typography variant="body1" gutterBottom>{selectedRequest.title}</Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">PO Number</Typography>
                      <Typography variant="body1" gutterBottom>{selectedRequest.po_number}</Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">PO Date</Typography>
                      <Typography variant="body1" gutterBottom>
                        {selectedRequest.po_date ? new Date(selectedRequest.po_date).toLocaleDateString() : 'N/A'}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Supplier</Typography>
                      <Typography variant="body1" gutterBottom>
                        {selectedRequest.supplier?.company_name || selectedRequest.supplier?.name || selectedRequest.supplier_name || 'N/A'}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Target Store</Typography>
                      <Typography variant="body1" gutterBottom>
                        {selectedRequest.target_store?.name || selectedRequest.target_store_name || 'N/A'}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Expected Delivery Date</Typography>
                      <Typography variant="body1" gutterBottom>
                        {selectedRequest.expected_delivery_date ? new Date(selectedRequest.expected_delivery_date).toLocaleDateString() : 'N/A'}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Is Urgent</Typography>
                      <Chip
                        label={selectedRequest.is_urgent ? 'Yes' : 'No'}
                        color={selectedRequest.is_urgent ? 'error' : 'default'}
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" color="text.secondary">Description</Typography>
                      <Typography variant="body1" gutterBottom>
                        {selectedRequest.description || 'No description provided'}
                      </Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" color="text.secondary">Additional Notes</Typography>
                      <Typography variant="body1" gutterBottom>
                        {selectedRequest.additional_notes || 'No additional notes'}
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>

              {/* Items Section */}
              <Card sx={{ m: 2, mb: 1 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom color="primary" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <ListIcon />
                    Items List ({selectedRequest.items?.length || 0} items)
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                  {selectedRequest.items && selectedRequest.items.length > 0 ? (
                    <TableContainer component={Paper} variant="outlined">
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Item Code</TableCell>
                            <TableCell>Description</TableCell>
                            <TableCell align="right">Quantity</TableCell>
                            <TableCell align="right">Unit Price</TableCell>
                            <TableCell align="right">Total</TableCell>
                            <TableCell>Classification</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {selectedRequest.items.map((item, index) => (
                            <TableRow key={item.id || index}>
                              <TableCell>
                                <Chip
                                  label={item.item_code || `PRE-${String(index + 1).padStart(3, '0')}`}
                                  size="small"
                                  color="primary"
                                  variant="outlined"
                                />
                              </TableCell>
                              <TableCell>{item.item_description}</TableCell>
                              <TableCell align="right">{item.quantity}</TableCell>
                              <TableCell align="right">
                                {item.unit_price ? `$${parseFloat(item.unit_price).toFixed(2)}` : 'N/A'}
                              </TableCell>
                              <TableCell align="right">
                                {item.unit_price ? `$${(parseFloat(item.unit_price) * item.quantity).toFixed(2)}` : 'N/A'}
                              </TableCell>
                              <TableCell>{item.main_classification_name || 'N/A'}</TableCell>
                            </TableRow>
                          ))}
                          <TableRow>
                            <TableCell colSpan={2} align="right">
                              <Typography variant="subtitle2" fontWeight={600}>Total Items:</Typography>
                            </TableCell>
                            <TableCell align="right">
                              <Typography variant="subtitle2" fontWeight={600}>
                                {selectedRequest.items.reduce((sum, item) => sum + item.quantity, 0)}
                              </Typography>
                            </TableCell>
                            <TableCell></TableCell>
                            <TableCell align="right">
                              <Typography variant="subtitle2" fontWeight={600}>
                                ${selectedRequest.items.reduce((sum, item) =>
                                  sum + (parseFloat(item.unit_price || 0) * item.quantity), 0
                                ).toFixed(2)}
                              </Typography>
                            </TableCell>
                            <TableCell></TableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                    </TableContainer>
                  ) : (
                    <Alert severity="info">No items added to this request yet.</Alert>
                  )}
                </CardContent>
              </Card>

              {/* Attachments Section */}
              <Card sx={{ m: 2, mb: 1 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom color="primary" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <AttachFileIcon />
                    Attachments ({selectedRequest.attachments?.length || 0} files)
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                  {selectedRequest.attachments && selectedRequest.attachments.length > 0 ? (
                    <Grid container spacing={2}>
                      {selectedRequest.attachments.map((attachment, index) => (
                        <Grid item xs={12} sm={6} md={4} key={attachment.id || index}>
                          <Paper
                            variant="outlined"
                            sx={{
                              p: 2,
                              display: 'flex',
                              alignItems: 'center',
                              gap: 1,
                              '&:hover': { backgroundColor: 'action.hover' }
                            }}
                          >
                            <AttachFileIcon color="primary" />
                            <Box sx={{ flexGrow: 1, minWidth: 0 }}>
                              <Typography variant="body2" noWrap>
                                {attachment.file_name || attachment.name || `Attachment ${index + 1}`}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {attachment.file_type || 'Unknown type'} • {attachment.file_size || 'Unknown size'}
                              </Typography>
                            </Box>
                            <IconButton
                              size="small"
                              onClick={() => handleDownloadAttachment(attachment)}
                              title="Download/View File"
                            >
                              <ViewIcon />
                            </IconButton>
                          </Paper>
                        </Grid>
                      ))}
                    </Grid>
                  ) : (
                    <Alert severity="info">No attachments uploaded for this request.</Alert>
                  )}
                </CardContent>
              </Card>

              {/* Workflow History Section */}
              <Card sx={{ m: 2, mb: 2 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom color="primary" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <AssignIcon />
                    Workflow History & Tracking
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Requested By</Typography>
                      <Typography variant="body1" gutterBottom>{selectedRequest.requested_by_name}</Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Created Date</Typography>
                      <Typography variant="body1" gutterBottom>
                        {new Date(selectedRequest.created_at).toLocaleString()}
                      </Typography>
                    </Grid>
                    {selectedRequest.approved_by_name && (
                      <>
                        <Grid item xs={12} md={6}>
                          <Typography variant="subtitle2" color="text.secondary">Approved By</Typography>
                          <Typography variant="body1" gutterBottom>{selectedRequest.approved_by_name}</Typography>
                        </Grid>
                        <Grid item xs={12} md={6}>
                          <Typography variant="subtitle2" color="text.secondary">Approval Date</Typography>
                          <Typography variant="body1" gutterBottom>
                            {selectedRequest.approval_date ? new Date(selectedRequest.approval_date).toLocaleString() : 'N/A'}
                          </Typography>
                        </Grid>
                      </>
                    )}
                    {selectedRequest.approval_comments && (
                      <Grid item xs={12}>
                        <Typography variant="subtitle2" color="text.secondary">Approval Comments</Typography>
                        <Paper variant="outlined" sx={{ p: 2, backgroundColor: 'action.hover' }}>
                          <Typography variant="body1">{selectedRequest.approval_comments}</Typography>
                        </Paper>
                      </Grid>
                    )}
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Last Updated</Typography>
                      <Typography variant="body1" gutterBottom>
                        {new Date(selectedRequest.updated_at).toLocaleString()}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Total Items Count</Typography>
                      <Typography variant="body1" gutterBottom>
                        {selectedRequest.total_items_count || selectedRequest.items?.length || 0}
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button onClick={() => setViewDialogOpen(false)} variant="outlined">
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {/* Approval Dialog */}
      <Dialog
        open={approvalDialogOpen}
        onClose={() => setApprovalDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          {approvalAction === 'approve' ? 'Approve' : 'Reject'} Entry Request
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" gutterBottom>
            Are you sure you want to {approvalAction} the entry request "{selectedRequest?.request_code}"?
          </Typography>
          <TextField
            fullWidth
            multiline
            rows={3}
            label="Comments"
            value={approvalComments}
            onChange={(e) => setApprovalComments(e.target.value)}
            placeholder={`Enter ${approvalAction} comments...`}
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setApprovalDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={submitApproval}
            variant="contained"
            color={approvalAction === 'approve' ? 'success' : 'error'}
          >
            {approvalAction === 'approve' ? 'Approve' : 'Reject'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Store Assignment Dialog */}
      <Dialog
        open={assignDialogOpen}
        onClose={() => setAssignDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Assign Entry Request to Store</DialogTitle>
        <DialogContent>
          <Typography variant="body1" gutterBottom>
            Assign entry request "{selectedRequest?.request_code}" to a store for processing.
          </Typography>
          <FormControl fullWidth sx={{ mt: 2 }}>
            <InputLabel>Select Store</InputLabel>
            <Select
              value={selectedStore}
              onChange={(e) => setSelectedStore(e.target.value)}
              label="Select Store"
            >
              {stores.map((store) => (
                <MenuItem key={store.id} value={store.id}>
                  {store.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAssignDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={submitStoreAssignment}
            variant="contained"
            disabled={!selectedStore}
          >
            Assign to Store
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        maxWidth="sm"
      >
        <DialogTitle>Delete Entry Request</DialogTitle>
        <DialogContent>
          <Typography variant="body1">
            Are you sure you want to delete the entry request "{selectedRequest?.request_code}"?
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleDeleteRequest}
            variant="contained"
            color="error"
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ItemReceiveDashboard;
