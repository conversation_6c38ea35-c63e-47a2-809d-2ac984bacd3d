import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Tabs,
  Tab,
  Badge
} from '@mui/material';
import {
  Add as AddIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  Assignment as AssignIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { useNavigate } from 'react-router-dom';
import api from '../../utils/axios';

const ItemReceiveDashboard = () => {
  const { enqueueSnackbar } = useSnackbar();
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState(false);
  const [requests, setRequests] = useState([]);
  const [filteredRequests, setFilteredRequests] = useState([]);
  const [currentTab, setCurrentTab] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  
  // Dialog states
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [approvalDialogOpen, setApprovalDialogOpen] = useState(false);
  const [assignDialogOpen, setAssignDialogOpen] = useState(false);
  
  // Form states
  const [approvalComments, setApprovalComments] = useState('');
  const [stores, setStores] = useState([]);
  const [selectedStore, setSelectedStore] = useState('');

  // Statistics
  const [stats, setStats] = useState({
    pending: 0,
    approved: 0,
    assigned: 0,
    inspecting: 0,
    completed: 0,
    rejected: 0
  });

  // Load data
  useEffect(() => {
    loadRequests();
    loadStores();
  }, []);

  // Filter requests based on tab and search
  useEffect(() => {
    let filtered = requests;

    // Filter by tab
    if (currentTab === 1) filtered = filtered.filter(r => r.workflow_status === 'pending');
    else if (currentTab === 2) filtered = filtered.filter(r => r.workflow_status === 'approved');
    else if (currentTab === 3) filtered = filtered.filter(r => r.workflow_status === 'assigned');
    else if (currentTab === 4) filtered = filtered.filter(r => r.workflow_status === 'inspecting');
    else if (currentTab === 5) filtered = filtered.filter(r => r.workflow_status === 'completed');

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(r => 
        r.request_code.toLowerCase().includes(searchTerm.toLowerCase()) ||
        r.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        r.po_number.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(r => r.workflow_status === statusFilter);
    }

    setFilteredRequests(filtered);
  }, [requests, currentTab, searchTerm, statusFilter]);

  const loadRequests = async () => {
    setLoading(true);
    try {
      const response = await api.get('/entry-requests/');
      const requestsData = response.data.results || response.data || [];
      setRequests(requestsData);

      // Calculate statistics
      const newStats = {
        pending: requestsData.filter(r => r.workflow_status === 'pending').length,
        approved: requestsData.filter(r => r.workflow_status === 'approved').length,
        assigned: requestsData.filter(r => r.workflow_status === 'assigned').length,
        inspecting: requestsData.filter(r => r.workflow_status === 'inspecting').length,
        completed: requestsData.filter(r => r.workflow_status === 'completed').length,
        rejected: requestsData.filter(r => r.workflow_status === 'rejected').length,
      };
      setStats(newStats);

    } catch (error) {
      console.error('Error loading requests:', error);
      enqueueSnackbar('Failed to load requests', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const loadStores = async () => {
    try {
      const response = await api.get('/stores/');
      setStores(response.data.results || response.data || []);
    } catch (error) {
      console.error('Error loading stores:', error);
    }
  };

  const handleApprove = async () => {
    try {
      await api.post(`/entry-requests/${selectedRequest.id}/approve/`, {
        comments: approvalComments
      });
      enqueueSnackbar('Request approved successfully', { variant: 'success' });
      setApprovalDialogOpen(false);
      setApprovalComments('');
      loadRequests();
    } catch (error) {
      console.error('Error approving request:', error);
      enqueueSnackbar('Failed to approve request', { variant: 'error' });
    }
  };

  const handleReject = async () => {
    try {
      await api.post(`/entry-requests/${selectedRequest.id}/reject/`, {
        comments: approvalComments
      });
      enqueueSnackbar('Request rejected', { variant: 'success' });
      setApprovalDialogOpen(false);
      setApprovalComments('');
      loadRequests();
    } catch (error) {
      console.error('Error rejecting request:', error);
      enqueueSnackbar('Failed to reject request', { variant: 'error' });
    }
  };

  const handleAssignToStore = async () => {
    try {
      await api.post(`/entry-requests/${selectedRequest.id}/assign/`, {
        store_id: selectedStore
      });
      enqueueSnackbar('Request assigned to store successfully', { variant: 'success' });
      setAssignDialogOpen(false);
      setSelectedStore('');
      loadRequests();
    } catch (error) {
      console.error('Error assigning request:', error);
      enqueueSnackbar('Failed to assign request', { variant: 'error' });
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      pending: 'warning',
      approved: 'info',
      assigned: 'primary',
      inspecting: 'secondary',
      completed: 'success',
      rejected: 'error'
    };
    return colors[status] || 'default';
  };

  const getStatusLabel = (status) => {
    const labels = {
      pending: 'Pending Approval',
      approved: 'Approved',
      assigned: 'Assigned to Store',
      inspecting: 'Under Inspection',
      completed: 'Completed',
      rejected: 'Rejected'
    };
    return labels[status] || status;
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Item Receive Dashboard
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => navigate('/procurement/entry-request/new')}
        >
          New Pre-Registration
        </Button>
      </Box>

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Pending
              </Typography>
              <Typography variant="h4" color="warning.main">
                {stats.pending}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Approved
              </Typography>
              <Typography variant="h4" color="info.main">
                {stats.approved}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Assigned
              </Typography>
              <Typography variant="h4" color="primary.main">
                {stats.assigned}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Inspecting
              </Typography>
              <Typography variant="h4" color="secondary.main">
                {stats.inspecting}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Completed
              </Typography>
              <Typography variant="h4" color="success.main">
                {stats.completed}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Rejected
              </Typography>
              <Typography variant="h4" color="error.main">
                {stats.rejected}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters and Search */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="Search by code, title, or PO number..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Status Filter</InputLabel>
                <Select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  label="Status Filter"
                >
                  <MenuItem value="all">All Statuses</MenuItem>
                  <MenuItem value="pending">Pending</MenuItem>
                  <MenuItem value="approved">Approved</MenuItem>
                  <MenuItem value="assigned">Assigned</MenuItem>
                  <MenuItem value="inspecting">Inspecting</MenuItem>
                  <MenuItem value="completed">Completed</MenuItem>
                  <MenuItem value="rejected">Rejected</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={loadRequests}
                disabled={loading}
              >
                Refresh
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Tabs */}
      <Card>
        <Tabs
          value={currentTab}
          onChange={(e, newValue) => setCurrentTab(newValue)}
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab label="All" />
          <Tab 
            label={
              <Badge badgeContent={stats.pending} color="warning">
                Pending
              </Badge>
            } 
          />
          <Tab 
            label={
              <Badge badgeContent={stats.approved} color="info">
                Approved
              </Badge>
            } 
          />
          <Tab 
            label={
              <Badge badgeContent={stats.assigned} color="primary">
                Assigned
              </Badge>
            } 
          />
          <Tab 
            label={
              <Badge badgeContent={stats.inspecting} color="secondary">
                Inspecting
              </Badge>
            } 
          />
          <Tab 
            label={
              <Badge badgeContent={stats.completed} color="success">
                Completed
              </Badge>
            } 
          />
        </Tabs>

        {/* Requests Table */}
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Request Code</TableCell>
                <TableCell>Title</TableCell>
                <TableCell>PO Number</TableCell>
                <TableCell>Supplier</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Created Date</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredRequests.map((request) => (
                <TableRow key={request.id}>
                  <TableCell>
                    <Typography variant="body2" fontWeight="bold">
                      {request.request_code}
                    </Typography>
                  </TableCell>
                  <TableCell>{request.title}</TableCell>
                  <TableCell>{request.po_number}</TableCell>
                  <TableCell>{request.supplier_name || 'N/A'}</TableCell>
                  <TableCell>
                    <Chip
                      label={getStatusLabel(request.workflow_status)}
                      color={getStatusColor(request.workflow_status)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    {new Date(request.created_at).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <IconButton
                      size="small"
                      onClick={() => {
                        setSelectedRequest(request);
                        setViewDialogOpen(true);
                      }}
                    >
                      <ViewIcon />
                    </IconButton>
                    
                    {request.workflow_status === 'pending' && (
                      <IconButton
                        size="small"
                        color="primary"
                        onClick={() => {
                          setSelectedRequest(request);
                          setApprovalDialogOpen(true);
                        }}
                      >
                        <ApproveIcon />
                      </IconButton>
                    )}
                    
                    {request.workflow_status === 'approved' && (
                      <IconButton
                        size="small"
                        color="secondary"
                        onClick={() => {
                          setSelectedRequest(request);
                          setAssignDialogOpen(true);
                        }}
                      >
                        <AssignIcon />
                      </IconButton>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Card>

      {/* View Dialog */}
      <Dialog open={viewDialogOpen} onClose={() => setViewDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Request Details</DialogTitle>
        <DialogContent>
          {selectedRequest && (
            <Box>
              <Typography variant="h6" gutterBottom>
                {selectedRequest.title}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Request Code: {selectedRequest.request_code}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                PO Number: {selectedRequest.po_number}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Status: {getStatusLabel(selectedRequest.workflow_status)}
              </Typography>
              {selectedRequest.description && (
                <Typography variant="body1" sx={{ mt: 2 }}>
                  {selectedRequest.description}
                </Typography>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Approval Dialog */}
      <Dialog open={approvalDialogOpen} onClose={() => setApprovalDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Approve/Reject Request</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            multiline
            rows={4}
            label="Comments"
            value={approvalComments}
            onChange={(e) => setApprovalComments(e.target.value)}
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setApprovalDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleReject} color="error">
            Reject
          </Button>
          <Button onClick={handleApprove} variant="contained">
            Approve
          </Button>
        </DialogActions>
      </Dialog>

      {/* Assign Dialog */}
      <Dialog open={assignDialogOpen} onClose={() => setAssignDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Assign to Store</DialogTitle>
        <DialogContent>
          <FormControl fullWidth sx={{ mt: 2 }}>
            <InputLabel>Select Store</InputLabel>
            <Select
              value={selectedStore}
              onChange={(e) => setSelectedStore(e.target.value)}
              label="Select Store"
            >
              {stores.map((store) => (
                <MenuItem key={store.id} value={store.id}>
                  {store.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAssignDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={handleAssignToStore} 
            variant="contained"
            disabled={!selectedStore}
          >
            Assign
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ItemReceiveDashboard;
