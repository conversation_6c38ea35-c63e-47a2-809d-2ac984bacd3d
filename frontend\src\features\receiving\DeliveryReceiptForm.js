import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Paper,
  Typography,
  Grid,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Divider,
  FormHelperText,
} from '@mui/material';
import {
  Save as SaveIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  ArrowForward as ArrowForwardIcon,
  LocalShipping as ShippingIcon,
  Search as SearchIcon,
  Receipt as ReceiptIcon,
  Inventory as InventoryIcon,
} from '@mui/icons-material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useSnackbar } from 'notistack';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { getSuppliers } from '../../services/suppliers';
import { getPurchaseOrders } from '../../services/procurement';
import { createDeliveryReceipt } from '../../services/receiving';
import api from '../../utils/axios';

// Validation schema
const validationSchema = Yup.object({
  source_type: Yup.string().required('Source type is required'),
  // Conditionally require fields based on source type
  supplier: Yup.string().when('source_type', {
    is: 'purchase_order',
    then: (schema) => schema.required('Supplier is required for purchase orders'),
    otherwise: (schema) => schema
  }),
  donor: Yup.string().when('source_type', {
    is: 'donation',
    then: (schema) => schema.required('Donor is required for donations'),
    otherwise: (schema) => schema
  }),
  sending_department: Yup.string().when('source_type', {
    is: 'internal_transfer',
    then: (schema) => schema.required('Sending department is required for internal transfers'),
    otherwise: (schema) => schema
  }),
  delivery_date: Yup.date().required('Delivery date is required'),
  delivery_note_number: Yup.string().required('Delivery note number is required'),
  // Reference numbers based on source type
  purchase_order: Yup.string().when('source_type', {
    is: 'purchase_order',
    then: (schema) => schema.required('Purchase order number is required'),
    otherwise: (schema) => schema
  }),
  donation_letter_ref: Yup.string().when('source_type', {
    is: 'donation',
    then: (schema) => schema.required('Donation letter reference is required'),
    otherwise: (schema) => schema
  }),
  internal_requisition_ref: Yup.string().when('source_type', {
    is: 'internal_transfer',
    then: (schema) => schema.required('Internal requisition reference is required'),
    otherwise: (schema) => schema
  }),
  received_by: Yup.string().required('Received by is required'),
  external_packaging_condition: Yup.string().required('External packaging condition is required'),
});

const DeliveryReceiptForm = () => {
  const [suppliers, setSuppliers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [showMockSupplier, setShowMockSupplier] = useState(false);
  const { enqueueSnackbar } = useSnackbar();
  const navigate = useNavigate();

  // No mock suppliers - we want to use real API data only

  // Fetch initial data
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // First, check if the backend is running
        try {
          console.log('Checking if backend is running...');
          const healthCheck = await api.get('/api/v1/test/');
          console.log('Backend health check response:', healthCheck.data);
        } catch (healthError) {
          console.warn('Backend health check failed:', healthError);
          // Continue anyway, as we'll try multiple endpoints
        }

        // Get suppliers with includeInactive=false to only get active suppliers
        let suppliersData = [];
        try {
          // Try to get suppliers with detailed logging
          console.log('Attempting to fetch suppliers...');
          suppliersData = await getSuppliers({}, false);
          console.log('Suppliers data received:', suppliersData);

          // Check if we got an array or an object with results
          if (Array.isArray(suppliersData)) {
            console.log('Setting suppliers from array:', suppliersData);
            setSuppliers(suppliersData);
          } else if (suppliersData && suppliersData.results) {
            console.log('Setting suppliers from results:', suppliersData.results);
            setSuppliers(suppliersData.results);
          } else if (suppliersData && Array.isArray(suppliersData.data)) {
            console.log('Setting suppliers from data array:', suppliersData.data);
            setSuppliers(suppliersData.data);
          } else {
            console.warn('Unexpected suppliers data format:', suppliersData);
            setSuppliers([]);
          }
        } catch (error) {
          console.error('Error fetching suppliers:', error);
          enqueueSnackbar('Error loading suppliers. Please check your network connection.', { variant: 'error' });

          // Add a button to retry
          enqueueSnackbar(
            'Click here to retry loading suppliers',
            {
              variant: 'info',
              action: (key) => (
                <Button color="inherit" size="small" onClick={() => {
                  fetchData();
                }}>
                  Retry
                </Button>
              )
            }
          );

          // Try a direct API call as a last resort
          try {
            console.log('Attempting direct API call to fetch suppliers...');
            const directResponse = await api.get('/api/v1/suppliers/');
            console.log('Direct API response:', directResponse.data);

            if (directResponse.data && Array.isArray(directResponse.data)) {
              console.log('Setting suppliers from direct API array:', directResponse.data);
              setSuppliers(directResponse.data);
            } else if (directResponse.data && Array.isArray(directResponse.data.results)) {
              console.log('Setting suppliers from direct API results:', directResponse.data.results);
              setSuppliers(directResponse.data.results);
            }
          } catch (directError) {
            console.error('Direct API call failed:', directError);

            // Try one more endpoint as a last resort
            try {
              const lastResortResponse = await api.get('/api/suppliers/');
              console.log('Last resort API response:', lastResortResponse.data);

              if (lastResortResponse.data && Array.isArray(lastResortResponse.data)) {
                console.log('Setting suppliers from last resort array:', lastResortResponse.data);
                setSuppliers(lastResortResponse.data);
              } else if (lastResortResponse.data && Array.isArray(lastResortResponse.data.results)) {
                console.log('Setting suppliers from last resort results:', lastResortResponse.data.results);
                setSuppliers(lastResortResponse.data.results);
              }
            } catch (lastError) {
              console.error('Last resort API call failed:', lastError);

              // No mock data - we want to use real API responses only
              console.error('All supplier API endpoints failed. Please check your backend connection.');
            }
          }
        }
      } catch (error) {
        console.error('Error in fetchData:', error);
        enqueueSnackbar('Error loading data. Please try again.', { variant: 'error' });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [enqueueSnackbar, navigate]);

  // Check if we have suppliers after the state has been updated
  useEffect(() => {
    if (!loading) {
      if (suppliers.length === 0) {
        setShowMockSupplier(true);

        // Show a warning about no suppliers
        enqueueSnackbar(
          'No suppliers found. You may need to create a supplier first.',
          {
            variant: 'warning',
            preventDuplicate: true,
            autoHideDuration: 5000,
            action: (key) => (
              <Button color="inherit" size="small" onClick={() => {
                navigate('/suppliers/new');
              }}>
                Create Supplier
              </Button>
            )
          }
        );
      } else {
        console.log(`Found ${suppliers.length} suppliers:`, suppliers);
      }
    }
  }, [suppliers, loading, enqueueSnackbar, navigate]);

  // Initialize form with formik
  const formik = useFormik({
    initialValues: {
      source_type: 'purchase_order', // Default to purchase order
      supplier: '',
      donor: '',
      sending_department: '',
      delivery_date: new Date(),
      delivery_note_number: '',
      purchase_order: '',
      donation_letter_ref: '',
      internal_requisition_ref: '',
      invoice_number: '',
      received_by: '', // This would typically be the current user
      external_packaging_condition: 'good', // Default to good
      remarks: '',
      attachments: [],
    },
    validationSchema,
    onSubmit: async (values) => {
      setSubmitting(true);
      try {
        console.log('Submitting delivery receipt with values:', values);

        let response;
        try {
          // Try to create the delivery receipt using the service
          response = await createDeliveryReceipt(values);
          console.log('Delivery receipt created successfully:', response);
          enqueueSnackbar('Delivery receipt created successfully', {
            variant: 'success',
            autoHideDuration: 3000
          });
        } catch (error) {
          console.error('Error creating delivery receipt:', error);
          enqueueSnackbar('Error creating delivery receipt. Please try again.', {
            variant: 'error',
            autoHideDuration: 5000
          });
          setSubmitting(false);
          return;
        }

        // Ask user what they want to do next
        const nextStep = window.confirm('Delivery receipt created. Would you like to proceed to inspection?');

        if (nextStep) {
          // Navigate to the inspection form
          console.log('Navigating to inspection form with delivery receipt ID:', response.id);
          navigate(`/inspection-form/${response.id}`);
        } else {
          // Navigate back to the dashboard
          enqueueSnackbar('Returning to dashboard. You can continue the process later.', {
            variant: 'info',
            autoHideDuration: 3000
          });
          navigate('/receiving-dashboard');
        }
      } catch (error) {
        console.error('Unexpected error in form submission:', error);
        enqueueSnackbar('An unexpected error occurred. Please try again.', {
          variant: 'error',
          autoHideDuration: 5000
        });
      } finally {
        setSubmitting(false);
      }
    },
  });

  // No need to filter purchase orders anymore as we're using a text field

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box sx={{ p: 3 }}>
        <Paper sx={{ p: 3, mb: 3, bgcolor: '#f5f5f5' }}>
          <Typography variant="h4" gutterBottom>
            Create Delivery Receipt
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            This is the first step in the item receiving process according to Ethiopian Federal Government standards.
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', my: 2 }}>
            <Box
              sx={{
                p: 2,
                bgcolor: '#e3f2fd',
                borderRadius: 1,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                boxShadow: '0 0 10px rgba(0,0,0,0.2)'
              }}
            >
              <ShippingIcon color="primary" />
              <Typography variant="body2" sx={{ mt: 1, fontWeight: 'bold' }}>
                1. Delivery Receipt
              </Typography>
            </Box>
            <Box sx={{ width: 50, height: 2, bgcolor: 'primary.main' }} />
            <Box
              sx={{
                p: 2,
                bgcolor: '#e8f5e9',
                borderRadius: 1,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                opacity: 0.7
              }}
            >
              <SearchIcon color="primary" />
              <Typography variant="body2" sx={{ mt: 1, fontWeight: 'bold' }}>
                2. Inspection
              </Typography>
            </Box>
            <Box sx={{ width: 50, height: 2, bgcolor: 'primary.main' }} />
            <Box
              sx={{
                p: 2,
                bgcolor: '#fff3e0',
                borderRadius: 1,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                opacity: 0.7
              }}
            >
              <ReceiptIcon color="primary" />
              <Typography variant="body2" sx={{ mt: 1, fontWeight: 'bold' }}>
                3. Model 19 Receipt
              </Typography>
            </Box>
            <Box sx={{ width: 50, height: 2, bgcolor: 'primary.main' }} />
            <Box
              sx={{
                p: 2,
                bgcolor: '#f3e5f5',
                borderRadius: 1,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                opacity: 0.7
              }}
            >
              <InventoryIcon color="primary" />
              <Typography variant="body2" sx={{ mt: 1, fontWeight: 'bold' }}>
                4. Inventory Update
              </Typography>
            </Box>
          </Box>
          <Typography variant="body2" color="text.secondary" align="center">
            You are currently at step 1: Creating a Delivery Receipt to record the initial delivery of items from a supplier.
          </Typography>
        </Paper>

        <Alert severity="info" sx={{ mb: 3 }}>
          Record the delivery information including supplier, delivery date, and reference numbers. This information will be used in the subsequent inspection and Model 19 receipt steps.
        </Alert>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <form onSubmit={formik.handleSubmit}>
            <Paper sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Delivery Information
              </Typography>
              <Grid container spacing={2}>
                {/* Source Type Selection */}
                <Grid item xs={12}>
                  <FormControl fullWidth margin="normal" error={formik.touched.source_type && Boolean(formik.errors.source_type)}>
                    <InputLabel id="source-type-label">Source Type *</InputLabel>
                    <Select
                      labelId="source-type-label"
                      id="source_type"
                      name="source_type"
                      value={formik.values.source_type}
                      onChange={formik.handleChange}
                      label="Source Type *"
                    >
                      <MenuItem value="purchase_order">Purchase Order</MenuItem>
                      <MenuItem value="donation">Donation</MenuItem>
                      <MenuItem value="internal_transfer">Internal Transfer</MenuItem>
                    </Select>
                    <FormHelperText>
                      {formik.touched.source_type && formik.errors.source_type ?
                        formik.errors.source_type :
                        "Select the source of this delivery"}
                    </FormHelperText>
                  </FormControl>
                </Grid>

                {/* External Packaging Condition */}
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth margin="normal" error={formik.touched.external_packaging_condition && Boolean(formik.errors.external_packaging_condition)}>
                    <InputLabel id="packaging-condition-label">External Packaging Condition *</InputLabel>
                    <Select
                      labelId="packaging-condition-label"
                      id="external_packaging_condition"
                      name="external_packaging_condition"
                      value={formik.values.external_packaging_condition}
                      onChange={formik.handleChange}
                      label="External Packaging Condition *"
                    >
                      <MenuItem value="good">Good</MenuItem>
                      <MenuItem value="damaged">Damaged</MenuItem>
                      <MenuItem value="opened">Opened</MenuItem>
                      <MenuItem value="wet">Wet/Water Damaged</MenuItem>
                      <MenuItem value="other">Other (Specify in Remarks)</MenuItem>
                    </Select>
                    <FormHelperText>
                      {formik.touched.external_packaging_condition && formik.errors.external_packaging_condition ?
                        formik.errors.external_packaging_condition :
                        "Condition of the external packaging upon receipt"}
                    </FormHelperText>
                  </FormControl>
                </Grid>

                {/* Conditional Fields Based on Source Type */}
                {formik.values.source_type === 'purchase_order' && (
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth margin="normal" error={formik.touched.supplier && Boolean(formik.errors.supplier)}>
                      <InputLabel id="supplier-label">Supplier *</InputLabel>
                      <Select
                        labelId="supplier-label"
                        id="supplier"
                        name="supplier"
                        value={formik.values.supplier}
                        onChange={formik.handleChange}
                        label="Supplier *"
                      >
                        <MenuItem value="">
                          <em>Select a supplier</em>
                        </MenuItem>
                        {suppliers.map((supplier) => {
                          // Handle different supplier data formats
                          const supplierId = supplier.id || supplier.supplier_id;
                          const supplierName = supplier.name || supplier.supplier_name;
                          const contactPerson = supplier.contact_person || supplier.contact;

                          return (
                            <MenuItem key={supplierId} value={supplierId}>
                              <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                                <Typography variant="body1">{supplierName}</Typography>
                                {contactPerson && (
                                  <Typography variant="caption" color="text.secondary">
                                    Contact: {contactPerson}
                                  </Typography>
                                )}
                                {supplier.email && (
                                  <Typography variant="caption" color="text.secondary">
                                    Email: {supplier.email}
                                  </Typography>
                                )}
                                {supplier.phone && (
                                  <Typography variant="caption" color="text.secondary">
                                    Phone: {supplier.phone}
                                  </Typography>
                                )}
                              </Box>
                            </MenuItem>
                          );
                        })}
                      </Select>
                      {suppliers.length === 0 ? (
                        <Box>
                          <FormHelperText error>
                            No suppliers available. Please add suppliers first.
                          </FormHelperText>
                          <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
                            <Button
                              size="small"
                              variant="outlined"
                              color="primary"
                              onClick={() => navigate('/suppliers/new')}
                              startIcon={<AddIcon />}
                            >
                              Create New Supplier
                            </Button>
                          </Box>

                          {/* Show a more detailed error message */}
                          <Alert severity="info" sx={{ mt: 2, fontSize: '0.8rem' }}>
                            If you're seeing this message, it means the application couldn't connect to the supplier database.
                            This could be because:
                            <ul>
                              <li>The backend API is not running</li>
                              <li>There are no suppliers in the database</li>
                              <li>The API endpoint has changed</li>
                            </ul>
                            Please contact your system administrator if this problem persists.
                          </Alert>
                        </Box>
                      ) : (
                        <FormHelperText>
                          {suppliers.length} supplier(s) available
                        </FormHelperText>
                      )}
                      {formik.touched.supplier && formik.errors.supplier && (
                        <FormHelperText>{formik.errors.supplier}</FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                )}

                {formik.values.source_type === 'donation' && (
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      id="donor"
                      name="donor"
                      label="Donor Name *"
                      value={formik.values.donor}
                      onChange={formik.handleChange}
                      error={formik.touched.donor && Boolean(formik.errors.donor)}
                      helperText={formik.touched.donor && formik.errors.donor}
                      margin="normal"
                    />
                  </Grid>
                )}

                {formik.values.source_type === 'internal_transfer' && (
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      id="sending_department"
                      name="sending_department"
                      label="Sending Department *"
                      value={formik.values.sending_department}
                      onChange={formik.handleChange}
                      error={formik.touched.sending_department && Boolean(formik.errors.sending_department)}
                      helperText={formik.touched.sending_department && formik.errors.sending_department}
                      margin="normal"
                    />
                  </Grid>
                )}
                <Grid item xs={12} md={6}>
                  <DatePicker
                    label="Delivery Date *"
                    value={formik.values.delivery_date}
                    onChange={(date) => formik.setFieldValue('delivery_date', date)}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        fullWidth
                        margin="normal"
                        error={formik.touched.delivery_date && Boolean(formik.errors.delivery_date)}
                        helperText={formik.touched.delivery_date && formik.errors.delivery_date}
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    id="delivery_note_number"
                    name="delivery_note_number"
                    label="Delivery Note Number *"
                    value={formik.values.delivery_note_number}
                    onChange={formik.handleChange}
                    error={formik.touched.delivery_note_number && Boolean(formik.errors.delivery_note_number)}
                    helperText={formik.touched.delivery_note_number && formik.errors.delivery_note_number}
                    margin="normal"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    id="invoice_number"
                    name="invoice_number"
                    label="Invoice Number"
                    value={formik.values.invoice_number}
                    onChange={formik.handleChange}
                    margin="normal"
                  />
                </Grid>
                {/* Conditional Reference Number Fields */}
                {formik.values.source_type === 'purchase_order' && (
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      id="purchase_order"
                      name="purchase_order"
                      label="Purchase Order Number *"
                      value={formik.values.purchase_order}
                      onChange={formik.handleChange}
                      error={formik.touched.purchase_order && Boolean(formik.errors.purchase_order)}
                      helperText={formik.touched.purchase_order && formik.errors.purchase_order}
                      margin="normal"
                      placeholder="Enter purchase order number"
                    />
                  </Grid>
                )}

                {formik.values.source_type === 'donation' && (
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      id="donation_letter_ref"
                      name="donation_letter_ref"
                      label="Donation Letter Reference *"
                      value={formik.values.donation_letter_ref}
                      onChange={formik.handleChange}
                      error={formik.touched.donation_letter_ref && Boolean(formik.errors.donation_letter_ref)}
                      helperText={formik.touched.donation_letter_ref && formik.errors.donation_letter_ref}
                      margin="normal"
                      placeholder="Enter donation letter reference"
                    />
                  </Grid>
                )}

                {formik.values.source_type === 'internal_transfer' && (
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      id="internal_requisition_ref"
                      name="internal_requisition_ref"
                      label="Internal Requisition Reference *"
                      value={formik.values.internal_requisition_ref}
                      onChange={formik.handleChange}
                      error={formik.touched.internal_requisition_ref && Boolean(formik.errors.internal_requisition_ref)}
                      helperText={formik.touched.internal_requisition_ref && formik.errors.internal_requisition_ref}
                      margin="normal"
                      placeholder="Enter internal requisition reference"
                    />
                  </Grid>
                )}
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    id="received_by"
                    name="received_by"
                    label="Received By *"
                    value={formik.values.received_by}
                    onChange={formik.handleChange}
                    error={formik.touched.received_by && Boolean(formik.errors.received_by)}
                    helperText={formik.touched.received_by && formik.errors.received_by}
                    margin="normal"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="remarks"
                    name="remarks"
                    label="Remarks"
                    multiline
                    rows={4}
                    value={formik.values.remarks}
                    onChange={formik.handleChange}
                    margin="normal"
                  />
                </Grid>
              </Grid>
            </Paper>

            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
              <Button
                variant="outlined"
                onClick={() => navigate('/receiving-dashboard')}
                sx={{ mr: 2 }}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                disabled={submitting}
                startIcon={submitting ? <CircularProgress size={20} /> : <ArrowForwardIcon />}
              >
                {submitting ? 'Submitting...' : 'Continue to Inspection'}
              </Button>
            </Box>
          </form>
        )}
      </Box>
    </LocalizationProvider>
  );
};

export default DeliveryReceiptForm;
