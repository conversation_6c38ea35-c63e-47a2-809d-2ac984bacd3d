import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Box,
  Paper,
  Typography,
  Grid,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Divider,
  FormHelperText,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tooltip,
  Checkbox,
  FormControlLabel,
} from '@mui/material';
import {
  Save as SaveIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  ArrowForward as ArrowForwardIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Warning as WarningIcon,
  ArrowBack as ArrowBackIcon,
} from '@mui/icons-material';
import { useFormik, FieldArray, Form, Formik } from 'formik';
import * as Yup from 'yup';
import { useSnackbar } from 'notistack';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { getDeliveryReceipt, submitInspection } from '../../services/receiving';
import { getInspectionCommittees } from '../../services/committees';
import { getDiscrepancyTypes } from '../../services/specifications';

// Validation schema
const validationSchema = Yup.object({
  inspection_committee: Yup.string(), // Made optional for now until backend is ready
  inspection_date: Yup.date().required('Inspection date is required'),
  inspected_by: Yup.string().required('Inspector name is required'),
  technical_inspection_required: Yup.boolean(),
  technical_inspector: Yup.string().when('technical_inspection_required', {
    is: true,
    then: (schema) => schema.required('Technical inspector name is required when technical inspection is required'),
    otherwise: (schema) => schema
  }),
  external_packaging_verified: Yup.boolean().required('External packaging verification is required'),
  packaging_condition_matches: Yup.boolean().required('Packaging condition verification is required'),
  items: Yup.array().of(
    Yup.object().shape({
      item_id: Yup.string().required('Item is required'),
      inspection_status: Yup.string().required('Inspection status is required'),
      quantity_received: Yup.number().required('Quantity received is required').min(0, 'Quantity must be non-negative'),
      quantity_accepted: Yup.number().required('Quantity accepted is required').min(0, 'Quantity must be non-negative'),
      discrepancy_type: Yup.string().when('inspection_status', {
        is: (val) => val === 'partial' || val === 'failed',
        then: (schema) => schema.required('Discrepancy type is required when status is partial or failed'),
        otherwise: (schema) => schema
      }),
      quarantine_required: Yup.boolean(),
      lab_test_required: Yup.boolean(),
    })
  ).min(1, 'At least one item is required'),
});

const InspectionForm = () => {
  const [deliveryReceipt, setDeliveryReceipt] = useState(null);
  const [inspectionCommittees, setInspectionCommittees] = useState([]);
  const [discrepancyTypes, setDiscrepancyTypes] = useState([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const { enqueueSnackbar } = useSnackbar();
  const navigate = useNavigate();
  const { deliveryReceiptId } = useParams();

  // Fetch initial data
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // First try to get delivery receipt
        let receiptData = null;
        try {
          receiptData = await getDeliveryReceipt(deliveryReceiptId);
          setDeliveryReceipt(receiptData);
        } catch (error) {
          console.error('Error fetching delivery receipt:', error);
          enqueueSnackbar('Error loading delivery receipt. Please try again.', { variant: 'error' });

          // No mock data - we want to use real API responses only

          // In production, navigate away
          try {
            navigate('/receiving-dashboard');
          } catch (navError) {
            console.error('Navigation error:', navError);
            // Try alternative routes
            try {
              navigate('/receiving');
            } catch (navError2) {
              console.error('Second navigation error:', navError2);
              // Last resort, go to home
              navigate('/');
            }
          }
          return;
        }

        // Then try to get committees and discrepancy types
        let committeesData = [];
        try {
          committeesData = await getInspectionCommittees();
          setInspectionCommittees(Array.isArray(committeesData) ? committeesData : []);
        } catch (error) {
          console.error('Error fetching inspection committees:', error);
          enqueueSnackbar('Error loading inspection committees. Some features may be limited.', { variant: 'warning' });
        }

        let discrepanciesData = [];
        try {
          discrepanciesData = await getDiscrepancyTypes();
          setDiscrepancyTypes(Array.isArray(discrepanciesData) ? discrepanciesData : []);
        } catch (error) {
          console.error('Error fetching discrepancy types:', error);
          enqueueSnackbar('Error loading discrepancy types. Some features may be limited.', { variant: 'warning' });
        }

        // Initialize form with items from delivery receipt
        if (receiptData && receiptData.items) {
          const initialItems = receiptData.items.map(item => ({
            item_id: item.id,
            item_name: item.name || 'Unknown Item',
            description: item.description || '',
            quantity_ordered: item.quantity || 0,
            quantity_received: item.quantity || 0,
            quantity_accepted: item.quantity || 0,
            inspection_status: 'passed',
            discrepancy_type: '',
            discrepancy_notes: '',
            quarantine_required: false,
            lab_test_required: false,
            technical_inspection_notes: '',
            physical_condition: 'good',
          }));

          formik.setFieldValue('items', initialItems);
        }
      } catch (error) {
        console.error('Error in fetchData:', error);
        enqueueSnackbar('Error loading data. Please try again.', { variant: 'error' });
      } finally {
        setLoading(false);
      }
    };

    if (deliveryReceiptId) {
      fetchData();
    }
  }, [deliveryReceiptId, enqueueSnackbar, navigate]);

  // Initialize form with formik
  const formik = useFormik({
    initialValues: {
      delivery_receipt: deliveryReceiptId,
      inspection_committee: '',
      inspection_date: new Date(),
      inspected_by: '',
      technical_inspection_required: false,
      technical_inspector: '',
      external_packaging_verified: false,
      packaging_condition_matches: false,
      inspection_notes: '',
      items: [],
    },
    validationSchema,
    onSubmit: async (values) => {
      setSubmitting(true);
      try {
        const response = await submitInspection(values);
        enqueueSnackbar('Inspection submitted successfully', { variant: 'success' });

        // Ask user if they want to proceed to Model 19 form
        const nextStep = window.confirm('Inspection submitted successfully. Would you like to proceed to creating a Model 19 receipt?');

        if (nextStep) {
          // Navigate to Model 19 form with the inspection ID
          console.log('Navigating to Model 19 form with inspection ID:', response.id);
          navigate(`/model19-form/${response.id}`);
        } else {
          // Navigate back to the dashboard
          enqueueSnackbar('Returning to dashboard. You can continue the process later.', {
            variant: 'info',
            autoHideDuration: 3000
          });
          navigate('/receiving-dashboard');
        }
      } catch (error) {
        console.error('Error submitting inspection:', error);
        enqueueSnackbar('Error submitting inspection. Please try again.', { variant: 'error' });
      } finally {
        setSubmitting(false);
      }
    },
  });

  // Handle inspection status change
  const handleStatusChange = (index, status) => {
    formik.setFieldValue(`items[${index}].inspection_status`, status);

    // If status is 'passed', set quantity_accepted to quantity_received
    if (status === 'passed') {
      formik.setFieldValue(
        `items[${index}].quantity_accepted`,
        formik.values.items[index].quantity_received
      );
    }
    // If status is 'failed', set quantity_accepted to 0
    else if (status === 'failed') {
      formik.setFieldValue(`items[${index}].quantity_accepted`, 0);
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          Inspection Form
        </Typography>

        <Alert severity="info" sx={{ mb: 3 }}>
          This is the second step in the item receiving process. Inspect the delivered items and record any discrepancies.
        </Alert>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <form onSubmit={formik.handleSubmit}>
            {/* Delivery Receipt Information */}
            {deliveryReceipt && (
              <Paper sx={{ p: 3, mb: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Delivery Receipt Information
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={4}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Supplier
                    </Typography>
                    <Typography variant="body1">
                      {deliveryReceipt.supplier_name}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Delivery Date
                    </Typography>
                    <Typography variant="body1">
                      {new Date(deliveryReceipt.delivery_date).toLocaleDateString()}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Delivery Note Number
                    </Typography>
                    <Typography variant="body1">
                      {deliveryReceipt.delivery_note_number}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Purchase Order
                    </Typography>
                    <Typography variant="body1">
                      {deliveryReceipt.purchase_order_number}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Invoice Number
                    </Typography>
                    <Typography variant="body1">
                      {deliveryReceipt.invoice_number || 'N/A'}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Received By
                    </Typography>
                    <Typography variant="body1">
                      {deliveryReceipt.received_by_name}
                    </Typography>
                  </Grid>
                </Grid>
              </Paper>
            )}

            {/* Inspection Information */}
            <Paper sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Inspection Information
              </Typography>
              <Grid container spacing={2}>
                {/* External Packaging Verification */}
                <Grid item xs={12}>
                  <Paper variant="outlined" sx={{ p: 2, mb: 2, bgcolor: '#f8f9fa' }}>
                    <Typography variant="subtitle1" gutterBottom>
                      External Packaging Verification
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={formik.values.external_packaging_verified}
                              onChange={(e) => formik.setFieldValue('external_packaging_verified', e.target.checked)}
                              name="external_packaging_verified"
                            />
                          }
                          label="External packaging has been verified"
                        />
                        {formik.touched.external_packaging_verified && formik.errors.external_packaging_verified && (
                          <FormHelperText error>{formik.errors.external_packaging_verified}</FormHelperText>
                        )}
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={formik.values.packaging_condition_matches}
                              onChange={(e) => formik.setFieldValue('packaging_condition_matches', e.target.checked)}
                              name="packaging_condition_matches"
                            />
                          }
                          label="Packaging condition matches delivery receipt"
                        />
                        {formik.touched.packaging_condition_matches && formik.errors.packaging_condition_matches && (
                          <FormHelperText error>{formik.errors.packaging_condition_matches}</FormHelperText>
                        )}
                      </Grid>
                    </Grid>
                  </Paper>
                </Grid>

                {/* Inspection Committee */}
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth margin="normal" error={formik.touched.inspection_committee && Boolean(formik.errors.inspection_committee)}>
                    <InputLabel id="committee-label">Inspection Committee</InputLabel>
                    <Select
                      labelId="committee-label"
                      id="inspection_committee"
                      name="inspection_committee"
                      value={formik.values.inspection_committee}
                      onChange={formik.handleChange}
                      label="Inspection Committee"
                    >
                      <MenuItem value="">
                        <em>Select a committee</em>
                      </MenuItem>
                      {inspectionCommittees.map((committee) => (
                        <MenuItem key={committee.id} value={committee.id}>
                          {committee.name}
                        </MenuItem>
                      ))}
                    </Select>
                    {inspectionCommittees.length === 0 && (
                      <FormHelperText>
                        No inspection committees available. You can proceed without selecting one.
                      </FormHelperText>
                    )}
                    {formik.touched.inspection_committee && formik.errors.inspection_committee && (
                      <FormHelperText>{formik.errors.inspection_committee}</FormHelperText>
                    )}
                  </FormControl>
                </Grid>

                {/* Inspection Date */}
                <Grid item xs={12} md={6}>
                  <DatePicker
                    label="Inspection Date *"
                    value={formik.values.inspection_date}
                    onChange={(date) => formik.setFieldValue('inspection_date', date)}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        fullWidth
                        margin="normal"
                        error={formik.touched.inspection_date && Boolean(formik.errors.inspection_date)}
                        helperText={formik.touched.inspection_date && formik.errors.inspection_date}
                      />
                    )}
                  />
                </Grid>

                {/* Inspected By */}
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    id="inspected_by"
                    name="inspected_by"
                    label="Inspected By *"
                    value={formik.values.inspected_by}
                    onChange={formik.handleChange}
                    error={formik.touched.inspected_by && Boolean(formik.errors.inspected_by)}
                    helperText={formik.touched.inspected_by && formik.errors.inspected_by}
                    margin="normal"
                  />
                </Grid>

                {/* Technical Inspection */}
                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={formik.values.technical_inspection_required}
                        onChange={(e) => formik.setFieldValue('technical_inspection_required', e.target.checked)}
                        name="technical_inspection_required"
                      />
                    }
                    label="Technical inspection required"
                  />
                  {formik.values.technical_inspection_required && (
                    <TextField
                      fullWidth
                      id="technical_inspector"
                      name="technical_inspector"
                      label="Technical Inspector *"
                      value={formik.values.technical_inspector}
                      onChange={formik.handleChange}
                      error={formik.touched.technical_inspector && Boolean(formik.errors.technical_inspector)}
                      helperText={formik.touched.technical_inspector && formik.errors.technical_inspector}
                      margin="normal"
                    />
                  )}
                </Grid>

                {/* Inspection Notes */}
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="inspection_notes"
                    name="inspection_notes"
                    label="Inspection Notes"
                    multiline
                    rows={4}
                    value={formik.values.inspection_notes}
                    onChange={formik.handleChange}
                    margin="normal"
                  />
                </Grid>
              </Grid>
            </Paper>

            {/* Item Inspection */}
            <Paper sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Item Inspection
              </Typography>
              <Alert severity="info" sx={{ mb: 2 }}>
                Inspect each item and record the quantity received, quantity accepted, and any discrepancies.
              </Alert>

              {formik.values.items.length === 0 ? (
                <Alert severity="warning">
                  No items found in the delivery receipt. Please go back and check the delivery receipt.
                </Alert>
              ) : (
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Item</TableCell>
                        <TableCell>Qty Ordered</TableCell>
                        <TableCell>Qty Received</TableCell>
                        <TableCell>Qty Accepted</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Discrepancy</TableCell>
                        <TableCell>Special Handling</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {formik.values.items.map((item, index) => (
                        <React.Fragment key={index}>
                          <TableRow>
                            <TableCell>
                              <Typography variant="subtitle2">{item.item_name}</Typography>
                              <Typography variant="caption" color="text.secondary">
                                {item.description}
                              </Typography>
                            </TableCell>
                            <TableCell>{item.quantity_ordered}</TableCell>
                            <TableCell>
                              <TextField
                                type="number"
                                size="small"
                                value={item.quantity_received}
                                onChange={(e) => {
                                  formik.setFieldValue(`items[${index}].quantity_received`, e.target.value);
                                  // If status is passed, update accepted quantity too
                                  if (item.inspection_status === 'passed') {
                                    formik.setFieldValue(`items[${index}].quantity_accepted`, e.target.value);
                                  }
                                }}
                                error={
                                  formik.touched.items?.[index]?.quantity_received &&
                                  Boolean(formik.errors.items?.[index]?.quantity_received)
                                }
                              />
                            </TableCell>
                            <TableCell>
                              <TextField
                                type="number"
                                size="small"
                                value={item.quantity_accepted}
                                onChange={(e) => formik.setFieldValue(`items[${index}].quantity_accepted`, e.target.value)}
                                error={
                                  formik.touched.items?.[index]?.quantity_accepted &&
                                  Boolean(formik.errors.items?.[index]?.quantity_accepted)
                                }
                              />
                            </TableCell>
                            <TableCell>
                              <Box sx={{ display: 'flex', gap: 1 }}>
                                <Tooltip title="Passed">
                                  <IconButton
                                    color={item.inspection_status === 'passed' ? 'success' : 'default'}
                                    onClick={() => handleStatusChange(index, 'passed')}
                                  >
                                    <CheckCircleIcon />
                                  </IconButton>
                                </Tooltip>
                                <Tooltip title="Partial">
                                  <IconButton
                                    color={item.inspection_status === 'partial' ? 'warning' : 'default'}
                                    onClick={() => handleStatusChange(index, 'partial')}
                                  >
                                    <WarningIcon />
                                  </IconButton>
                                </Tooltip>
                                <Tooltip title="Failed">
                                  <IconButton
                                    color={item.inspection_status === 'failed' ? 'error' : 'default'}
                                    onClick={() => handleStatusChange(index, 'failed')}
                                  >
                                    <CancelIcon />
                                  </IconButton>
                                </Tooltip>
                              </Box>
                            </TableCell>
                            <TableCell>
                              {(item.inspection_status === 'partial' || item.inspection_status === 'failed') && (
                                <FormControl fullWidth size="small">
                                  <InputLabel id={`discrepancy-type-label-${index}`}>Discrepancy Type *</InputLabel>
                                  <Select
                                    labelId={`discrepancy-type-label-${index}`}
                                    value={item.discrepancy_type}
                                    onChange={(e) => formik.setFieldValue(`items[${index}].discrepancy_type`, e.target.value)}
                                    label="Discrepancy Type *"
                                    error={
                                      formik.touched.items?.[index]?.discrepancy_type &&
                                      Boolean(formik.errors.items?.[index]?.discrepancy_type)
                                    }
                                  >
                                    <MenuItem value="">
                                      <em>Select type</em>
                                    </MenuItem>
                                    {discrepancyTypes.map((type) => (
                                      <MenuItem key={type.id} value={type.id}>
                                        {type.name}
                                      </MenuItem>
                                    ))}
                                  </Select>
                                  <TextField
                                    fullWidth
                                    placeholder="Notes"
                                    size="small"
                                    value={item.discrepancy_notes}
                                    onChange={(e) => formik.setFieldValue(`items[${index}].discrepancy_notes`, e.target.value)}
                                    sx={{ mt: 1 }}
                                  />
                                </FormControl>
                              )}
                            </TableCell>
                            <TableCell>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    checked={item.quarantine_required}
                                    onChange={(e) => formik.setFieldValue(`items[${index}].quarantine_required`, e.target.checked)}
                                    size="small"
                                  />
                                }
                                label="Quarantine"
                              />
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    checked={item.lab_test_required}
                                    onChange={(e) => formik.setFieldValue(`items[${index}].lab_test_required`, e.target.checked)}
                                    size="small"
                                  />
                                }
                                label="Lab Test"
                              />
                            </TableCell>
                          </TableRow>
                          {/* Additional row for physical condition and technical inspection notes */}
                          <TableRow>
                            <TableCell colSpan={7} sx={{ borderTop: 'none', pt: 0 }}>
                              <Grid container spacing={2}>
                                <Grid item xs={12} md={6}>
                                  <FormControl fullWidth size="small" margin="dense">
                                    <InputLabel id={`physical-condition-label-${index}`}>Physical Condition</InputLabel>
                                    <Select
                                      labelId={`physical-condition-label-${index}`}
                                      value={item.physical_condition}
                                      onChange={(e) => formik.setFieldValue(`items[${index}].physical_condition`, e.target.value)}
                                      label="Physical Condition"
                                    >
                                      <MenuItem value="good">Good</MenuItem>
                                      <MenuItem value="damaged">Damaged</MenuItem>
                                      <MenuItem value="opened">Opened</MenuItem>
                                      <MenuItem value="wet">Wet/Water Damaged</MenuItem>
                                      <MenuItem value="other">Other (Specify in Notes)</MenuItem>
                                    </Select>
                                  </FormControl>
                                </Grid>
                                <Grid item xs={12} md={6}>
                                  <TextField
                                    fullWidth
                                    size="small"
                                    label="Technical Inspection Notes"
                                    value={item.technical_inspection_notes}
                                    onChange={(e) => formik.setFieldValue(`items[${index}].technical_inspection_notes`, e.target.value)}
                                    margin="dense"
                                  />
                                </Grid>
                              </Grid>
                            </TableCell>
                          </TableRow>
                        </React.Fragment>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </Paper>

            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
              <Button
                variant="outlined"
                startIcon={<ArrowBackIcon />}
                onClick={() => navigate(`/delivery-receipt/${deliveryReceiptId}`)}
              >
                Back to Delivery Receipt
              </Button>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                disabled={submitting || formik.values.items.length === 0}
                startIcon={submitting ? <CircularProgress size={20} /> : <ArrowForwardIcon />}
              >
                {submitting ? 'Submitting...' : 'Continue to Model 19'}
              </Button>
            </Box>
          </form>
        )}
      </Box>
    </LocalizationProvider>
  );
};

export default InspectionForm;
