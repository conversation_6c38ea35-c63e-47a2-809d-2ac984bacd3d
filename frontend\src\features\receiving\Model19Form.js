import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Box,
  Button,
  Card,
  CardContent,
  Grid,
  Typography,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Paper,
  Divider,
  Alert,
  CircularProgress,
  Stepper,
  Step,
  StepLabel,
  InputAdornment,
  FormHelperText,
  FormControlLabel,
  Checkbox,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Print as PrintIcon,
  ArrowBack as ArrowBackIcon,
  Assignment as AssignmentIcon,
  CheckCircle as CheckCircleIcon,
  LocalShipping as ShippingIcon,
  Search as SearchIcon,
  Receipt as ReceiptIcon,
  Inventory as InventoryIcon,
} from '@mui/icons-material';
import { useFormik, FieldArray, Formik, Form } from 'formik';
import * as Yup from 'yup';
import { useSnackbar } from 'notistack';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { createModel19Receipt, finalizeModel19Receipt, getInspection } from '../../services/receiving';
import { getSuppliers } from '../../services/suppliers';
import { getStores, getStoreShelves } from '../../services/storage';
import { getItemMasters, getItemMaster, getBatch } from '../../services/items';
import { getItemTypes, getItemCategories, getItemBrands, getItemQualities, getUnitsOfMeasure } from '../../services/specifications';
import { getSerialVouchers, getSerialVoucherCategories, getItemReceivedVouchers } from '../../services/serials';
import { getItemStatuses, getPropertyStatuses } from '../../services/status';
import Model19Print from './Model19Print';

// Validation schema
const validationSchema = Yup.object({
  source_type: Yup.string().required('Source type is required'),
  receipt_date: Yup.date().required('Receipt date is required'),
  store: Yup.string().required('Store is required'),
  received_by: Yup.string().required('Received by is required'),
  inspected_by: Yup.string().required('Inspected by is required'),
  approved_by: Yup.string().required('Approved by is required'),
  voucher: Yup.string().required('Item Received Voucher is required'),
  voucher_number: Yup.string().required('Voucher number is required'),
  // Conditional validation based on source type
  supplier: Yup.string().when('source_type', {
    is: 'supplier',
    then: (schema) => schema.required('Supplier is required for external suppliers'),
    otherwise: (schema) => schema
  }),
  department: Yup.string().when('source_type', {
    is: 'department',
    then: (schema) => schema.required('Department is required for internal transfers'),
    otherwise: (schema) => schema
  }),
  items: Yup.array().of(
    Yup.object().shape({
      item_master: Yup.string().required('Item is required'),
      quantity: Yup.number().required('Quantity is required').min(1, 'Quantity must be at least 1'),
      unit_price: Yup.number().required('Unit price is required').min(0, 'Unit price must be non-negative'),
      status: Yup.string().required('Status is required'),
      property_status: Yup.string().required('Property status is required'),
      approval_status: Yup.string().required('Approval status is required'),
    })
  ).min(1, 'At least one item is required'),
});

const Model19Form = () => {
  const [suppliers, setSuppliers] = useState([]);
  const [stores, setStores] = useState([]);
  const [shelves, setShelves] = useState([]);
  const [itemMasters, setItemMasters] = useState([]);
  const [vouchers, setVouchers] = useState([]);
  const [itemTypes, setItemTypes] = useState([]);
  const [itemCategories, setItemCategories] = useState([]);
  const [itemBrands, setItemBrands] = useState([]);
  const [itemQualities, setItemQualities] = useState([]);
  const [unitsOfMeasure, setUnitsOfMeasure] = useState([]);
  const [itemStatuses, setItemStatuses] = useState([]);
  const [propertyStatuses, setPropertyStatuses] = useState([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [activeStep, setActiveStep] = useState(0);
  const [model19Data, setModel19Data] = useState(null);
  const [voucherError, setVoucherError] = useState('');
  const [selectedItemMaster, setSelectedItemMaster] = useState(null);
  const [selectedBatch, setSelectedBatch] = useState(null);
  const [inspection, setInspection] = useState(null);
  const { enqueueSnackbar } = useSnackbar();
  const navigate = useNavigate();
  const { itemMasterId, batchId, inspectionId } = useParams();



  // Fetch initial data
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Log that we're starting to fetch data
        console.log('Fetching data for Model 19 form...');

        // Check if we have an inspection ID from params
        let inspectionData = null;
        if (inspectionId) {
          inspectionData = await getInspection(inspectionId);
          setInspection(inspectionData);
        }

        // Now fetch all data in parallel
        let vouchersData = [];
        try {
          // First try to get Item Received Vouchers
          vouchersData = await getItemReceivedVouchers();
          console.log('Successfully fetched Item Received Vouchers:', vouchersData);
        } catch (voucherError) {
          console.error('Error fetching Item Received Vouchers:', voucherError);
          enqueueSnackbar('Error loading Item Received Vouchers. Please create them first.', {
            variant: 'error',
            autoHideDuration: 5000
          });
          setVoucherError('No Item Received Vouchers available. Please create one first from the Serial Vouchers page.');
        }

        // Fetch all other data
        const [
          suppliersData,
          storesData,
          itemMastersData,
          itemTypesData,
          itemCategoriesData,
          itemBrandsData,
          itemQualitiesData,
          unitsOfMeasureData,
          itemStatusesData,
          propertyStatusesData
        ] = await Promise.all([
          getSuppliers(),
          getStores(),
          getItemMasters(),
          getItemTypes(),
          getItemCategories(),
          getItemBrands(),
          getItemQualities(),
          getUnitsOfMeasure(),
          getItemStatuses(),
          getPropertyStatuses()
        ]);

        setSuppliers(Array.isArray(suppliersData) ? suppliersData : []);
        setStores(Array.isArray(storesData) ? storesData : []);
        setItemMasters(Array.isArray(itemMastersData) ? itemMastersData : []);
        setItemTypes(Array.isArray(itemTypesData) ? itemTypesData : []);
        setItemCategories(Array.isArray(itemCategoriesData) ? itemCategoriesData : []);
        setItemBrands(Array.isArray(itemBrandsData) ? itemBrandsData : []);
        setItemQualities(Array.isArray(itemQualitiesData) ? itemQualitiesData : []);
        setUnitsOfMeasure(Array.isArray(unitsOfMeasureData) ? unitsOfMeasureData : []);
        setItemStatuses(Array.isArray(itemStatusesData) ? itemStatusesData : []);
        setPropertyStatuses(Array.isArray(propertyStatusesData) ? propertyStatusesData : []);

        // Set vouchers directly from the API response
        const filteredVouchers = Array.isArray(vouchersData) ? vouchersData : [];
        setVouchers(filteredVouchers);

        if (filteredVouchers.length === 0) {
          setVoucherError('No Item Received Vouchers available. Please create one first from the Serial Vouchers page.');
          enqueueSnackbar('No Item Received Vouchers found. Please create them first.', {
            variant: 'warning',
            autoHideDuration: 5000
          });
        } else {
          console.log('Found Item Received Vouchers:', filteredVouchers);
        }

        // Set default status values for the first item
        const defaultItemStatus = findDefaultStatusId(Array.isArray(itemStatusesData) ? itemStatusesData : [], 'Pending');
        const defaultPropertyStatus = findDefaultStatusId(Array.isArray(propertyStatusesData) ? propertyStatusesData : [], 'New');
        const defaultApprovalStatus = findDefaultStatusId(Array.isArray(itemStatusesData) ? itemStatusesData : [], 'Pending Approval');

        // If we have inspection data, pre-populate the form
        if (inspectionData) {
          // Set supplier and other fields from the inspection's delivery receipt
          const deliveryReceipt = inspectionData.delivery_receipt;
          if (deliveryReceipt) {
            formik.setFieldValue('supplier', deliveryReceipt.supplier);
            formik.setFieldValue('purchase_order', deliveryReceipt.purchase_order);
            formik.setFieldValue('delivery_note', deliveryReceipt.delivery_note_number);
            formik.setFieldValue('receipt_date', new Date(deliveryReceipt.delivery_date));
            formik.setFieldValue('received_by', deliveryReceipt.received_by);
            formik.setFieldValue('inspected_by', inspectionData.inspected_by);
          }

          // Set items from inspection
          if (inspectionData.items && inspectionData.items.length > 0) {
            const inspectedItems = inspectionData.items.map(item => ({
              item_master: item.item_master,
              description: item.description || '',
              quantity: item.quantity_accepted,
              unit_price: item.unit_price || '',
              purchase_price: item.unit_price * item.quantity_accepted,
              invoice_no: deliveryReceipt?.invoice_number || '',
              warranty_months: '',
              expiry_date: null,
              serial_number: '',
              barcode: '',
              serie: '',
              page_from: '',
              page_to: '',
              status: defaultItemStatus,
              property_status: defaultPropertyStatus,
              approval_status: defaultApprovalStatus,
              item_type: item.item_type || '',
              item_category: item.item_category || '',
              brand: item.brand || '',
              quality: item.quality || '',
              unit_of_measure: item.unit_of_measure || '',
              inspection_status: item.inspection_status || 'passed',
              inspection_notes: item.inspection_notes || '',
              remarks: '',
              is_active: true,
            }));

            formik.setFieldValue('items', inspectedItems);
          }
        } else {
          // Just set default values for the first item if no inspection data
          formik.setFieldValue('items[0].status', defaultItemStatus);
          formik.setFieldValue('items[0].property_status', defaultPropertyStatus);
          formik.setFieldValue('items[0].approval_status', defaultApprovalStatus);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        enqueueSnackbar('Error loading data. Please try again.', { variant: 'error' });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [enqueueSnackbar]);

  // Initialize form with formik
  const formik = useFormik({
    initialValues: {
      source_type: 'supplier',
      supplier: '',
      department: '',
      purchase_order: '',
      delivery_note: '',
      receipt_date: new Date(),
      notes: '',
      store: '',
      shelf: '',
      received_by: '', // This would typically be the current user
      inspected_by: '',
      approved_by: '',
      voucher: '', // This will be set from Item Received Vouchers
      voucher_number: '', // This will be generated from the selected voucher
      copies: 4, // Default to 4 copies as per Ethiopian Federal Government standards
      distribution: {
        accounts: true,
        stock_clerk: true,
        supplier: true,
        store_keeper: true
      },
      items: [
        {
          // Item Master Information
          item_master: '',
          description: '',

          // Batch Information
          quantity: '1',
          unit_price: '',
          purchase_price: '',
          invoice_no: '',
          warranty_months: '',
          expiry_date: null,

          // Item Information
          serial_number: '',
          barcode: '',
          serie: '',
          page_from: '',
          page_to: '',

          // Status Information - these will be set after data is loaded
          status: '',
          property_status: '',
          approval_status: '',

          // Specifications
          item_type: '',
          item_category: '',
          brand: '',
          quality: '',
          unit_of_measure: '',

          // Inspection Information
          inspection_status: 'passed',
          inspection_notes: '',

          // Additional Information
          remarks: '',
          is_active: true,
        },
      ],
    },
    validationSchema,
    onSubmit: async (values) => {
      await handleSubmit(values);
    },
  });

  // Fetch shelves when store changes
  useEffect(() => {
    const fetchShelves = async () => {
      if (formik.values.store) {
        try {
          const response = await getStoreShelves(formik.values.store);
          setShelves(response || []);
        } catch (error) {
          console.error('Error fetching shelves:', error);
          setShelves([]);
        }
      } else {
        setShelves([]);
      }
    };

    fetchShelves();
  }, [formik.values.store]);

  // Generate voucher number when voucher is selected
  useEffect(() => {
    const generateVoucherNumber = () => {
      if (formik.values.voucher) {
        const selectedVoucher = vouchers.find(v => v.id === formik.values.voucher);
        if (selectedVoucher) {
          // Format: PREFIX-CURRENT_NUMBER (e.g., IRV-001)
          const prefix = selectedVoucher.prefix || 'IRV';
          const currentNumber = selectedVoucher.current_number || 1;
          const formattedNumber = String(currentNumber).padStart(3, '0');
          const voucherNumber = `${prefix}-${formattedNumber}`;

          formik.setFieldValue('voucher_number', voucherNumber);
        }
      }
    };

    generateVoucherNumber();
  }, [formik.values.voucher, vouchers]);

  // Fetch item master or batch data if provided in URL
  useEffect(() => {
    const fetchItemMasterOrBatch = async () => {
      if (itemMasterId) {
        try {
          const itemMasterData = await getItemMaster(itemMasterId);
          setSelectedItemMaster(itemMasterData);

          // Pre-populate form with item master data
          formik.setFieldValue('items[0].item_master', itemMasterData.id);
          formik.setFieldValue('items[0].description', itemMasterData.description || '');

          if (itemMasterData.unit_of_measure) {
            formik.setFieldValue('items[0].unit_of_measure', itemMasterData.unit_of_measure.id);
          }

          if (itemMasterData.item_type) {
            formik.setFieldValue('items[0].item_type', itemMasterData.item_type.id);
          }

          if (itemMasterData.item_category) {
            formik.setFieldValue('items[0].item_category', itemMasterData.item_category.id);
          }

          if (itemMasterData.brand) {
            formik.setFieldValue('items[0].brand', itemMasterData.brand.id);
          }

          if (itemMasterData.quality) {
            formik.setFieldValue('items[0].quality', itemMasterData.quality.id);
          }

          formik.setFieldValue('items[0].unit_price', itemMasterData.unit_price || '');
        } catch (error) {
          console.error('Error fetching item master:', error);
          enqueueSnackbar('Error loading item master data', { variant: 'error' });
        }
      } else if (batchId) {
        try {
          const batchData = await getBatch(batchId);
          setSelectedBatch(batchData);

          // Pre-populate form with batch data
          if (batchData.master_item) {
            formik.setFieldValue('items[0].item_master', batchData.master_item.id);
            formik.setFieldValue('items[0].description', batchData.master_item.description || '');
          }

          formik.setFieldValue('items[0].quantity', batchData.quantity || '');
          formik.setFieldValue('items[0].unit_price', batchData.purchase_price || '');
          formik.setFieldValue('items[0].invoice_no', batchData.invoice_no || '');
          formik.setFieldValue('items[0].warranty_months', batchData.warranty_months || '');
          formik.setFieldValue('items[0].expiry_date', batchData.expiry_date || null);
          formik.setFieldValue('items[0].serie', batchData.series || '');
          formik.setFieldValue('items[0].page_from', batchData.page_from || '');
          formik.setFieldValue('items[0].page_to', batchData.page_to || '');

          if (batchData.supplier) {
            formik.setFieldValue('supplier', batchData.supplier.id);
          }

          if (batchData.store) {
            formik.setFieldValue('store', batchData.store.id);
          }

          if (batchData.received_date) {
            formik.setFieldValue('receipt_date', new Date(batchData.received_date));
          }
        } catch (error) {
          console.error('Error fetching batch:', error);
          enqueueSnackbar('Error loading batch data', { variant: 'error' });
        }
      }
    };

    if ((itemMasterId || batchId) && formik) {
      fetchItemMasterOrBatch();
    }
  }, [itemMasterId, batchId, enqueueSnackbar, formik]);

  // Find default status IDs
  const findDefaultStatusId = (statuses, statusName) => {
    const defaultStatus = statuses.find(s =>
      s.name.toLowerCase() === statusName.toLowerCase() ||
      s.code?.toLowerCase() === statusName.toLowerCase()
    );
    return defaultStatus?.id || '';
  };

  // Add a new item row
  const handleAddItem = () => {
    // Find default status values
    const defaultItemStatus = findDefaultStatusId(Array.isArray(itemStatuses) ? itemStatuses : [], 'Pending');
    const defaultPropertyStatus = findDefaultStatusId(Array.isArray(propertyStatuses) ? propertyStatuses : [], 'New');
    const defaultApprovalStatus = findDefaultStatusId(Array.isArray(itemStatuses) ? itemStatuses : [], 'Pending Approval');

    formik.setFieldValue('items', [
      ...formik.values.items,
      {
        // Item Master Information
        item_master: '',
        description: '',

        // Batch Information
        quantity: '',
        unit_price: '',
        purchase_price: '',
        invoice_no: '',
        warranty_months: '',
        expiry_date: null,

        // Item Information
        serial_number: '',
        barcode: '',
        serie: '',
        page_from: '',
        page_to: '',

        // Status Information
        status: defaultItemStatus,
        property_status: defaultPropertyStatus,
        approval_status: defaultApprovalStatus,

        // Specifications
        item_type: '',
        item_category: '',
        brand: '',
        quality: '',
        unit_of_measure: '',

        // Inspection Information
        inspection_status: 'passed',
        inspection_notes: '',

        // Additional Information
        remarks: '',
        is_active: true,
      },
    ]);
  };

  // Remove an item row
  const handleRemoveItem = (index) => {
    const newItems = [...formik.values.items];
    newItems.splice(index, 1);
    formik.setFieldValue('items', newItems);
  };

  // Calculate total for an item
  const calculateItemTotal = (item) => {
    const quantity = Number(item.quantity) || 0;
    const unitPrice = Number(item.unit_price) || 0;
    return (quantity * unitPrice).toFixed(2);
  };

  // Handle form submission
  const handleSubmit = async (values) => {
    setSubmitting(true);
    try {
      // Prepare data for API
      const formData = {
        ...values,
        // Convert items to the format expected by the API
        items: values.items.map(item => ({
          // Item Master Information
          item_master_id: item.item_master,
          description: item.description,

          // Batch Information
          quantity: item.quantity,
          unit_price: item.unit_price,
          purchase_price: item.purchase_price,
          invoice_no: item.invoice_no,
          warranty_months: item.warranty_months,
          expiry_date: item.expiry_date,

          // Item Information
          serial_number: item.serial_number,
          barcode: item.barcode,
          serie: item.serie,
          page_from: item.page_from,
          page_to: item.page_to,

          // Status Information
          status: item.status,
          property_status: item.property_status,
          approval_status: item.approval_status,

          // Specifications
          item_type: item.item_type,
          item_category: item.item_category,
          brand: item.brand,
          quality: item.quality,
          unit_of_measure: item.unit_of_measure,

          // Inspection Information
          inspection_status: item.inspection_status,
          inspection_notes: item.inspection_notes,

          // Additional Information
          remarks: item.remarks,
          is_active: item.is_active,
        })),
      };

      // Submit the Model 19 receipt
      const response = await createModel19Receipt(formData);

      enqueueSnackbar('Model 19 receipt created successfully', { variant: 'success' });
      setModel19Data(response);
      setActiveStep(1);
    } catch (error) {
      console.error('Error submitting Model 19 receipt:', error);
      enqueueSnackbar('Error creating receipt. Please try again.', { variant: 'error' });
    } finally {
      setSubmitting(false);
    }
  };

  // Handle finalizing the receipt
  const handleFinalize = async () => {
    if (!model19Data) return;

    setSubmitting(true);
    try {
      await finalizeModel19Receipt(model19Data.id);
      enqueueSnackbar('Model 19 receipt finalized successfully', { variant: 'success' });
      setActiveStep(2);
    } catch (error) {
      console.error('Error finalizing Model 19 receipt:', error);
      enqueueSnackbar('Error finalizing receipt. Please try again.', { variant: 'error' });
    } finally {
      setSubmitting(false);
    }
  };

  // Steps for the Model 19 process
  const steps = ['Create Receipt', 'Review & Print', 'Finalize'];

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          {selectedItemMaster
            ? `Create Model 19 Receipt for ${selectedItemMaster.name}`
            : selectedBatch
              ? `Create Model 19 Receipt for Batch ${selectedBatch.invoice_no || ''}`
              : inspection
                ? `Create Model 19 Receipt for Inspected Items`
                : 'Create Model 19 Receipt'
          }
        </Typography>

        <Paper sx={{ p: 3, mb: 3, bgcolor: '#f5f5f5' }}>
          <Typography variant="h6" gutterBottom>
            Ethiopian Federal Government Item Receiving Workflow
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', my: 2 }}>
            <Box
              sx={{
                p: 2,
                bgcolor: '#e3f2fd',
                borderRadius: 1,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                opacity: inspection ? 0.7 : 1
              }}
            >
              <ShippingIcon color="primary" />
              <Typography variant="body2" sx={{ mt: 1, fontWeight: 'bold' }}>
                1. Delivery Receipt
              </Typography>
            </Box>
            <Box sx={{ width: 50, height: 2, bgcolor: 'primary.main' }} />
            <Box
              sx={{
                p: 2,
                bgcolor: '#e8f5e9',
                borderRadius: 1,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                opacity: inspection ? 0.7 : 1
              }}
            >
              <SearchIcon color="primary" />
              <Typography variant="body2" sx={{ mt: 1, fontWeight: 'bold' }}>
                2. Inspection
              </Typography>
            </Box>
            <Box sx={{ width: 50, height: 2, bgcolor: 'primary.main' }} />
            <Box
              sx={{
                p: 2,
                bgcolor: '#fff3e0',
                borderRadius: 1,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                boxShadow: '0 0 10px rgba(0,0,0,0.2)'
              }}
            >
              <ReceiptIcon color="primary" />
              <Typography variant="body2" sx={{ mt: 1, fontWeight: 'bold' }}>
                3. Model 19 Receipt
              </Typography>
            </Box>
            <Box sx={{ width: 50, height: 2, bgcolor: 'primary.main' }} />
            <Box
              sx={{
                p: 2,
                bgcolor: '#f3e5f5',
                borderRadius: 1,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                opacity: 0.7
              }}
            >
              <InventoryIcon color="primary" />
              <Typography variant="body2" sx={{ mt: 1, fontWeight: 'bold' }}>
                4. Inventory Update
              </Typography>
            </Box>
          </Box>
          <Typography variant="body2" color="text.secondary" align="center">
            You are currently at step 3: Creating a Model 19 Receipt according to Ethiopian Federal Government standards.
          </Typography>
        </Paper>

        {(selectedItemMaster || selectedBatch) && (
          <Alert severity="info" sx={{ mb: 3 }}>
            {selectedItemMaster
              ? 'This form is pre-populated with data from the selected Item Master. You can modify the details as needed.'
              : 'This form is pre-populated with data from the selected Batch. You can modify the details as needed.'
            }
          </Alert>
        )}

        {inspection && (
          <Alert severity="info" sx={{ mb: 3 }}>
            This form is pre-populated with data from the inspection. The items shown have passed inspection and are ready to be received into inventory.
          </Alert>
        )}

        {(selectedItemMaster || selectedBatch) && (
          <Box sx={{ mb: 3 }}>
            <Button
              variant="outlined"
              startIcon={<ArrowBackIcon />}
              onClick={() => {
                if (selectedItemMaster) {
                  navigate(`/item-masters/${itemMasterId}`);
                } else if (selectedBatch) {
                  navigate(`/batches/${batchId}`);
                }
              }}
            >
              {selectedItemMaster ? 'Back to Item Master' : 'Back to Batch'}
            </Button>
          </Box>
        )}

        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            {activeStep === 0 && (
              <Formik
                initialValues={formik.initialValues}
                validationSchema={validationSchema}
                onSubmit={async (values) => {
                  await handleSubmit(values);
                }}
              >
                {(formikProps) => (
                <Form>
                <Paper sx={{ p: 3, mb: 3 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h6">
                      Receipt Information
                    </Typography>
                    <Button
                      variant="outlined"
                      size="small"
                      startIcon={<AssignmentIcon />}
                      onClick={() => navigate('/serial-vouchers')}
                    >
                      Manage Vouchers
                    </Button>
                  </Box>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={4}>
                      <FormControl fullWidth margin="normal">
                        <InputLabel id="source-type-label">Source Type</InputLabel>
                        <Select
                          labelId="source-type-label"
                          id="source_type"
                          name="source_type"
                          value={formikProps.values.source_type}
                          onChange={formikProps.handleChange}
                          label="Source Type"
                          error={formikProps.touched.source_type && Boolean(formikProps.errors.source_type)}
                        >
                          <MenuItem value="supplier">External Supplier</MenuItem>
                          <MenuItem value="department">Internal Department</MenuItem>
                          <MenuItem value="return">Return to Store</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <DatePicker
                        label="Receipt Date"
                        value={formikProps.values.receipt_date}
                        onChange={(date) => formikProps.setFieldValue('receipt_date', date)}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            fullWidth
                            margin="normal"
                            error={formikProps.touched.receipt_date && Boolean(formikProps.errors.receipt_date)}
                            helperText={formikProps.touched.receipt_date && formikProps.errors.receipt_date}
                          />
                        )}
                      />
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <FormControl
                        fullWidth
                        margin="normal"
                        error={formikProps.touched.voucher && Boolean(formikProps.errors.voucher) || Boolean(voucherError)}
                      >
                        <InputLabel id="voucher-label">Item Received Voucher</InputLabel>
                        <Select
                          labelId="voucher-label"
                          id="voucher"
                          name="voucher"
                          value={formikProps.values.voucher}
                          onChange={formikProps.handleChange}
                          label="Item Received Voucher"
                          disabled={vouchers.length === 0}
                        >
                          {vouchers.length === 0 ? (
                            <MenuItem value="">
                              <em>No vouchers available</em>
                            </MenuItem>
                          ) : (
                            vouchers.map((voucher) => (
                              <MenuItem key={voucher.id} value={voucher.id}>
                                {voucher.name || 'Unnamed Voucher'}
                                {voucher.prefix && ` (${voucher.prefix})`}
                                {voucher.current_number && voucher.end_number &&
                                  ` - ${voucher.current_number} to ${voucher.end_number}`}
                              </MenuItem>
                            ))
                          )}
                        </Select>
                        <FormHelperText>
                          Select a voucher to generate registry numbers for items. Vouchers can be created from the Serial Vouchers page.
                        </FormHelperText>
                        {(formikProps.touched.voucher && formikProps.errors.voucher) && (
                          <Typography color="error" variant="caption">
                            {formikProps.errors.voucher}
                          </Typography>
                        )}
                        {voucherError && (
                          <Box>
                            <Typography color="error" variant="caption" sx={{ display: 'block', mb: 1 }}>
                              {voucherError}
                            </Typography>
                            <Button
                              size="small"
                              variant="outlined"
                              color="primary"
                              onClick={() => navigate('/serial-vouchers')}
                            >
                              Go to Serial Vouchers
                            </Button>
                          </Box>
                        )}
                      </FormControl>
                    </Grid>

                    {/* Voucher Number Field */}
                    {formikProps.values.voucher && (
                      <Grid item xs={12} md={4}>
                        <TextField
                          fullWidth
                          id="voucher_number"
                          name="voucher_number"
                          label="Voucher Number *"
                          value={formikProps.values.voucher_number}
                          onChange={formikProps.handleChange}
                          error={formikProps.touched.voucher_number && Boolean(formikProps.errors.voucher_number)}
                          helperText={
                            (formikProps.touched.voucher_number && formikProps.errors.voucher_number) ||
                            "Auto-generated from selected voucher"
                          }
                          margin="normal"
                          InputProps={{
                            readOnly: true,
                          }}
                        />
                      </Grid>
                    )}
                  </Grid>

                  {/* Distribution Copies Section */}
                  <Box sx={{ mt: 3 }}>
                    <Typography variant="subtitle1" gutterBottom>
                      Model 19 Distribution Copies
                    </Typography>
                    <Typography variant="body2" color="text.secondary" paragraph>
                      According to Ethiopian Federal Government standards, Model 19 receipts require 4 copies for distribution.
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6} md={3}>
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={formikProps.values.distribution.accounts}
                              onChange={(e) => formikProps.setFieldValue('distribution.accounts', e.target.checked)}
                              name="distribution.accounts"
                            />
                          }
                          label="1. Accounts (Original)"
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={3}>
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={formikProps.values.distribution.stock_clerk}
                              onChange={(e) => formikProps.setFieldValue('distribution.stock_clerk', e.target.checked)}
                              name="distribution.stock_clerk"
                            />
                          }
                          label="2. Stock Clerk (Duplicate)"
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={3}>
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={formikProps.values.distribution.supplier}
                              onChange={(e) => formikProps.setFieldValue('distribution.supplier', e.target.checked)}
                              name="distribution.supplier"
                            />
                          }
                          label="3. Supplier (Triplicate)"
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={3}>
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={formikProps.values.distribution.store_keeper}
                              onChange={(e) => formikProps.setFieldValue('distribution.store_keeper', e.target.checked)}
                              name="distribution.store_keeper"
                            />
                          }
                          label="4. Store Keeper (Fourth Copy)"
                        />
                      </Grid>
                    </Grid>
                  </Box>
                </Paper>

                <Paper sx={{ p: 3, mb: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Item Details
                  </Typography>
                  <Alert severity="info" sx={{ mb: 2 }}>
                    Items will be registered with the Ethiopian Federal Government standard format. Each item will receive a unique registry number from the selected voucher.
                  </Alert>
                  <FieldArray
                    name="items"
                    render={arrayHelpers => (
                      <>
                        {formikProps.values.items && formikProps.values.items.map((item, index) => (
                          <Box key={index} sx={{ mb: 3, p: 2, border: '1px solid #e0e0e0', borderRadius: 2 }}>
                            <Grid container spacing={2}>
                              <Grid item xs={12}>
                                <Typography variant="subtitle1" fontWeight="bold">
                                  Item #{index + 1}
                                </Typography>
                                <Divider sx={{ my: 1 }} />
                              </Grid>

                              <Grid item xs={12} md={6}>
                                <FormControl
                                  fullWidth
                                  margin="normal"
                                  error={
                                    formikProps.touched.items?.[index]?.item_master &&
                                    Boolean(formikProps.errors.items?.[index]?.item_master)
                                  }
                                >
                                  <InputLabel id={`item-master-label-${index}`}>Item Classification</InputLabel>
                                  <Select
                                    labelId={`item-master-label-${index}`}
                                    id={`items[${index}].item_master`}
                                    name={`items[${index}].item_master`}
                                    value={item.item_master}
                                    onChange={(e) => {
                                      formikProps.handleChange(e);
                                      if (Array.isArray(itemMasters)) {
                                        const selectedItemMaster = itemMasters.find(
                                          (im) => im.id === e.target.value
                                        );
                                        if (selectedItemMaster) {
                                          // Set description
                                          formikProps.setFieldValue(
                                            `items[${index}].description`,
                                            selectedItemMaster.description || ''
                                          );

                                          // Set related fields from the item master
                                          if (selectedItemMaster.item_type) {
                                            formikProps.setFieldValue(
                                              `items[${index}].item_type`,
                                              selectedItemMaster.item_type
                                            );
                                          }

                                          if (selectedItemMaster.item_category) {
                                            formikProps.setFieldValue(
                                              `items[${index}].item_category`,
                                              selectedItemMaster.item_category
                                            );
                                          }

                                          if (selectedItemMaster.brand) {
                                            formikProps.setFieldValue(
                                              `items[${index}].brand`,
                                              selectedItemMaster.brand
                                            );
                                          }

                                          if (selectedItemMaster.unit_of_measure) {
                                            formikProps.setFieldValue(
                                              `items[${index}].unit_of_measure`,
                                              selectedItemMaster.unit_of_measure
                                            );
                                          }
                                        }
                                      }
                                    }}
                                    label="Item Classification"
                                  >
                                    <MenuItem value="">
                                      <em>Select an item classification</em>
                                    </MenuItem>
                                    {Array.isArray(itemMasters) && itemMasters.map((itemMaster) => (
                                      <MenuItem key={itemMaster.id} value={itemMaster.id}>
                                        <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                                          <Typography variant="body1">
                                            {itemMaster.name} - {itemMaster.stock_classification_code || 'No code'}
                                          </Typography>
                                          {itemMaster.description && (
                                            <Typography variant="caption" color="text.secondary" noWrap>
                                              {itemMaster.description.substring(0, 50)}
                                              {itemMaster.description.length > 50 ? '...' : ''}
                                            </Typography>
                                          )}
                                        </Box>
                                      </MenuItem>
                                    ))}
                                  </Select>
                                  {formikProps.touched.items?.[index]?.item_master &&
                                    formikProps.errors.items?.[index]?.item_master && (
                                      <FormHelperText>
                                        {formikProps.errors.items[index].item_master}
                                      </FormHelperText>
                                    )}
                                </FormControl>
                              </Grid>

                              <Grid item xs={12} md={6}>
                                <TextField
                                  fullWidth
                                  margin="normal"
                                  id={`items[${index}].description`}
                                  name={`items[${index}].description`}
                                  label="Description"
                                  value={item.description}
                                  onChange={formikProps.handleChange}
                                  multiline
                                  rows={2}
                                />
                              </Grid>

                              <Grid item xs={12}>
                                <Typography variant="subtitle2" color="text.secondary" sx={{ mt: 2, mb: 1 }}>
                                  Quantity and Value
                                </Typography>
                              </Grid>

                              <Grid item xs={12} md={4}>
                                <TextField
                                  fullWidth
                                  margin="normal"
                                  id={`items[${index}].quantity`}
                                  name={`items[${index}].quantity`}
                                  label="Quantity"
                                  type="number"
                                  value={item.quantity || ''}
                                  onChange={formikProps.handleChange}
                                  error={
                                    formikProps.touched.items?.[index]?.quantity &&
                                    Boolean(formikProps.errors.items?.[index]?.quantity)
                                  }
                                  helperText={
                                    formikProps.touched.items?.[index]?.quantity &&
                                    formikProps.errors.items?.[index]?.quantity
                                  }
                                />
                              </Grid>

                              <Grid item xs={12} md={4}>
                                <TextField
                                  fullWidth
                                  margin="normal"
                                  id={`items[${index}].unit_price`}
                                  name={`items[${index}].unit_price`}
                                  label="Unit Price"
                                  type="number"
                                  value={item.unit_price || ''}
                                  onChange={formikProps.handleChange}
                                  error={
                                    formikProps.touched.items?.[index]?.unit_price &&
                                    Boolean(formikProps.errors.items?.[index]?.unit_price)
                                  }
                                  helperText={
                                    formikProps.touched.items?.[index]?.unit_price &&
                                    formikProps.errors.items?.[index]?.unit_price
                                  }
                                  InputProps={{
                                    startAdornment: <InputAdornment position="start">ETB</InputAdornment>,
                                  }}
                                />
                              </Grid>

                              <Grid item xs={12} md={4}>
                                <TextField
                                  fullWidth
                                  margin="normal"
                                  label="Total Value"
                                  type="number"
                                  value={item.quantity && item.unit_price ? (item.quantity * item.unit_price).toFixed(2) : ''}
                                  InputProps={{
                                    readOnly: true,
                                    startAdornment: <InputAdornment position="start">ETB</InputAdornment>,
                                  }}
                                />
                              </Grid>

                              <Grid item xs={12}>
                                <Typography variant="subtitle2" color="text.secondary" sx={{ mt: 2, mb: 1 }}>
                                  Batch Information
                                </Typography>
                              </Grid>

                              <Grid item xs={12} md={4}>
                                <TextField
                                  fullWidth
                                  margin="normal"
                                  id={`items[${index}].invoice_no`}
                                  name={`items[${index}].invoice_no`}
                                  label="Invoice Number"
                                  value={item.invoice_no || ''}
                                  onChange={formikProps.handleChange}
                                />
                              </Grid>

                              <Grid item xs={12} md={4}>
                                <TextField
                                  fullWidth
                                  margin="normal"
                                  id={`items[${index}].warranty_months`}
                                  name={`items[${index}].warranty_months`}
                                  label="Warranty (Months)"
                                  type="number"
                                  value={item.warranty_months || ''}
                                  onChange={formikProps.handleChange}
                                />
                              </Grid>

                              <Grid item xs={12} md={4}>
                                <DatePicker
                                  label="Expiry Date"
                                  value={item.expiry_date}
                                  onChange={(date) => formikProps.setFieldValue(`items[${index}].expiry_date`, date)}
                                  renderInput={(params) => (
                                    <TextField
                                      {...params}
                                      fullWidth
                                      margin="normal"
                                      id={`items[${index}].expiry_date`}
                                      name={`items[${index}].expiry_date`}
                                    />
                                  )}
                                />
                              </Grid>

                              <Grid item xs={12}>
                                <Typography variant="subtitle2" color="text.secondary" sx={{ mt: 2, mb: 1 }}>
                                  Item Information
                                </Typography>
                              </Grid>

                              <Grid item xs={12} md={4}>
                                <TextField
                                  fullWidth
                                  margin="normal"
                                  id={`items[${index}].serial_number`}
                                  name={`items[${index}].serial_number`}
                                  label="Serial Number"
                                  value={item.serial_number || ''}
                                  onChange={formikProps.handleChange}
                                />
                              </Grid>

                              <Grid item xs={12} md={4}>
                                <TextField
                                  fullWidth
                                  margin="normal"
                                  id={`items[${index}].serie`}
                                  name={`items[${index}].serie`}
                                  label="Serie"
                                  value={item.serie || ''}
                                  onChange={formikProps.handleChange}
                                />
                              </Grid>

                              <Grid item xs={12} md={4}>
                                <FormControl fullWidth margin="normal">
                                  <InputLabel id={`unit-of-measure-label-${index}`}>Unit of Measure</InputLabel>
                                  <Select
                                    labelId={`unit-of-measure-label-${index}`}
                                    id={`items[${index}].unit_of_measure`}
                                    name={`items[${index}].unit_of_measure`}
                                    value={item.unit_of_measure || ''}
                                    onChange={formikProps.handleChange}
                                    label="Unit of Measure"
                                  >
                                    <MenuItem value="">
                                      <em>Select a unit of measure</em>
                                    </MenuItem>
                                    {unitsOfMeasure.map((uom) => (
                                      <MenuItem key={uom.id} value={uom.id}>
                                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                          <Typography variant="body2" sx={{ fontWeight: 'bold', mr: 1 }}>
                                            {uom.symbol}
                                          </Typography>
                                          <Typography variant="body2">
                                            {uom.name}
                                          </Typography>
                                        </Box>
                                      </MenuItem>
                                    ))}
                                  </Select>
                                  {unitsOfMeasure.length === 0 && (
                                    <FormHelperText>
                                      No units of measure available
                                    </FormHelperText>
                                  )}
                                </FormControl>
                              </Grid>

                              <Grid item xs={12}>
                                <Typography variant="subtitle2" color="text.secondary" sx={{ mt: 2, mb: 1 }}>
                                  Registry Information
                                </Typography>
                              </Grid>

                              <Grid item xs={12} md={6}>
                                <TextField
                                  fullWidth
                                  margin="normal"
                                  id={`items[${index}].page_from`}
                                  name={`items[${index}].page_from`}
                                  label="Page From"
                                  type="number"
                                  value={item.page_from || ''}
                                  onChange={formikProps.handleChange}
                                />
                              </Grid>

                              <Grid item xs={12} md={6}>
                                <TextField
                                  fullWidth
                                  margin="normal"
                                  id={`items[${index}].page_to`}
                                  name={`items[${index}].page_to`}
                                  label="Page To"
                                  type="number"
                                  value={item.page_to || ''}
                                  onChange={formikProps.handleChange}
                                />
                              </Grid>

                              <Grid item xs={12}>
                                <Typography variant="subtitle2" color="text.secondary" sx={{ mt: 2, mb: 1 }}>
                                  Status Information
                                </Typography>
                              </Grid>

                              <Grid item xs={12} md={4}>
                                <FormControl fullWidth margin="normal">
                                  <InputLabel id={`status-label-${index}`}>Item Status</InputLabel>
                                  <Select
                                    labelId={`status-label-${index}`}
                                    id={`items[${index}].status`}
                                    name={`items[${index}].status`}
                                    value={item.status || ''}
                                    onChange={formikProps.handleChange}
                                    label="Item Status"
                                  >
                                    <MenuItem value="">
                                      <em>Select a status</em>
                                    </MenuItem>
                                    {itemStatuses.map((status) => (
                                      <MenuItem key={status.id} value={status.id}>
                                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                          <Box
                                            sx={{
                                              width: 12,
                                              height: 12,
                                              borderRadius: '50%',
                                              bgcolor:
                                                status.name.toLowerCase().includes('pending') ? 'warning.main' :
                                                status.name.toLowerCase().includes('in stock') ? 'success.main' :
                                                status.name.toLowerCase().includes('issued') ? 'info.main' :
                                                status.name.toLowerCase().includes('disposed') ? 'error.main' :
                                                'grey.500',
                                              mr: 1
                                            }}
                                          />
                                          <Typography variant="body2">{status.name}</Typography>
                                        </Box>
                                      </MenuItem>
                                    ))}
                                  </Select>
                                  {itemStatuses.length === 0 && (
                                    <FormHelperText>
                                      No item statuses available
                                    </FormHelperText>
                                  )}
                                </FormControl>
                              </Grid>

                              <Grid item xs={12} md={4}>
                                <FormControl fullWidth margin="normal">
                                  <InputLabel id={`property-status-label-${index}`}>Property Status</InputLabel>
                                  <Select
                                    labelId={`property-status-label-${index}`}
                                    id={`items[${index}].property_status`}
                                    name={`items[${index}].property_status`}
                                    value={item.property_status || ''}
                                    onChange={formikProps.handleChange}
                                    label="Property Status"
                                  >
                                    <MenuItem value="">
                                      <em>Select a property status</em>
                                    </MenuItem>
                                    {propertyStatuses.map((status) => (
                                      <MenuItem key={status.id} value={status.id}>
                                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                          <Box
                                            sx={{
                                              width: 12,
                                              height: 12,
                                              borderRadius: '50%',
                                              bgcolor:
                                                status.name.toLowerCase().includes('new') ? 'success.main' :
                                                status.name.toLowerCase().includes('good') ? 'info.main' :
                                                status.name.toLowerCase().includes('fair') ? 'warning.main' :
                                                status.name.toLowerCase().includes('poor') ? 'error.light' :
                                                status.name.toLowerCase().includes('damaged') ? 'error.main' :
                                                'grey.500',
                                              mr: 1
                                            }}
                                          />
                                          <Typography variant="body2">{status.name}</Typography>
                                        </Box>
                                      </MenuItem>
                                    ))}
                                  </Select>
                                  {propertyStatuses.length === 0 && (
                                    <FormHelperText>
                                      No property statuses available
                                    </FormHelperText>
                                  )}
                                </FormControl>
                              </Grid>

                              <Grid item xs={12} md={4}>
                                <FormControl fullWidth margin="normal">
                                  <InputLabel id={`item-type-label-${index}`}>Item Type</InputLabel>
                                  <Select
                                    labelId={`item-type-label-${index}`}
                                    id={`items[${index}].item_type`}
                                    name={`items[${index}].item_type`}
                                    value={item.item_type || ''}
                                    onChange={formikProps.handleChange}
                                    label="Item Type"
                                  >
                                    <MenuItem value="">
                                      <em>Select an item type</em>
                                    </MenuItem>
                                    {itemTypes.map((type) => (
                                      <MenuItem key={type.id} value={type.id}>
                                        <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                                          <Typography variant="body2">{type.name}</Typography>
                                          {type.description && (
                                            <Typography variant="caption" color="text.secondary">
                                              {type.description.substring(0, 40)}
                                              {type.description.length > 40 ? '...' : ''}
                                            </Typography>
                                          )}
                                        </Box>
                                      </MenuItem>
                                    ))}
                                  </Select>
                                  {itemTypes.length === 0 && (
                                    <FormHelperText>
                                      No item types available
                                    </FormHelperText>
                                  )}
                                </FormControl>
                              </Grid>

                              <Grid item xs={12}>
                                <Typography variant="subtitle2" color="text.secondary" sx={{ mt: 2, mb: 1 }}>
                                  Specifications
                                </Typography>
                              </Grid>

                              <Grid item xs={12} md={4}>
                                <FormControl fullWidth margin="normal">
                                  <InputLabel id={`item-category-label-${index}`}>Item Category</InputLabel>
                                  <Select
                                    labelId={`item-category-label-${index}`}
                                    id={`items[${index}].item_category`}
                                    name={`items[${index}].item_category`}
                                    value={item.item_category || ''}
                                    onChange={formikProps.handleChange}
                                    label="Item Category"
                                  >
                                    <MenuItem value="">
                                      <em>Select an item category</em>
                                    </MenuItem>
                                    {itemCategories.map((category) => (
                                      <MenuItem key={category.id} value={category.id}>
                                        <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                                          <Typography variant="body2">{category.name}</Typography>
                                          {category.description && (
                                            <Typography variant="caption" color="text.secondary">
                                              {category.description.substring(0, 40)}
                                              {category.description.length > 40 ? '...' : ''}
                                            </Typography>
                                          )}
                                        </Box>
                                      </MenuItem>
                                    ))}
                                  </Select>
                                  {itemCategories.length === 0 && (
                                    <FormHelperText>
                                      No item categories available
                                    </FormHelperText>
                                  )}
                                </FormControl>
                              </Grid>

                              <Grid item xs={12} md={4}>
                                <FormControl fullWidth margin="normal">
                                  <InputLabel id={`brand-label-${index}`}>Brand</InputLabel>
                                  <Select
                                    labelId={`brand-label-${index}`}
                                    id={`items[${index}].brand`}
                                    name={`items[${index}].brand`}
                                    value={item.brand || ''}
                                    onChange={formikProps.handleChange}
                                    label="Brand"
                                  >
                                    <MenuItem value="">
                                      <em>Select a brand</em>
                                    </MenuItem>
                                    {itemBrands.map((brand) => (
                                      <MenuItem key={brand.id} value={brand.id}>
                                        <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                                          <Typography variant="body2">{brand.name}</Typography>
                                          {brand.description && (
                                            <Typography variant="caption" color="text.secondary">
                                              {brand.description.substring(0, 40)}
                                              {brand.description.length > 40 ? '...' : ''}
                                            </Typography>
                                          )}
                                        </Box>
                                      </MenuItem>
                                    ))}
                                  </Select>
                                  {itemBrands.length === 0 && (
                                    <FormHelperText>
                                      No brands available
                                    </FormHelperText>
                                  )}
                                </FormControl>
                              </Grid>

                              <Grid item xs={12} md={4}>
                                <FormControl fullWidth margin="normal">
                                  <InputLabel id={`quality-label-${index}`}>Quality</InputLabel>
                                  <Select
                                    labelId={`quality-label-${index}`}
                                    id={`items[${index}].quality`}
                                    name={`items[${index}].quality`}
                                    value={item.quality || ''}
                                    onChange={formikProps.handleChange}
                                    label="Quality"
                                  >
                                    <MenuItem value="">
                                      <em>Select a quality</em>
                                    </MenuItem>
                                    {itemQualities.map((quality) => (
                                      <MenuItem key={quality.id} value={quality.id}>
                                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                          <Box
                                            sx={{
                                              width: 12,
                                              height: 12,
                                              borderRadius: '50%',
                                              bgcolor:
                                                quality.name.toLowerCase().includes('high') ? 'success.main' :
                                                quality.name.toLowerCase().includes('medium') ? 'warning.main' :
                                                quality.name.toLowerCase().includes('low') ? 'error.main' :
                                                'grey.500',
                                              mr: 1
                                            }}
                                          />
                                          <Typography variant="body2">{quality.name}</Typography>
                                        </Box>
                                      </MenuItem>
                                    ))}
                                  </Select>
                                  {itemQualities.length === 0 && (
                                    <FormHelperText>
                                      No qualities available
                                    </FormHelperText>
                                  )}
                                </FormControl>
                              </Grid>

                              <Grid item xs={12}>
                                <Typography variant="subtitle2" color="text.secondary" sx={{ mt: 2, mb: 1 }}>
                                  Inspection Details
                                </Typography>
                              </Grid>

                              <Grid item xs={12} md={6}>
                                <FormControl fullWidth margin="normal">
                                  <InputLabel id={`inspection-status-label-${index}`}>
                                    Inspection Status
                                  </InputLabel>
                                  <Select
                                    labelId={`inspection-status-label-${index}`}
                                    id={`items[${index}].inspection_status`}
                                    name={`items[${index}].inspection_status`}
                                    value={item.inspection_status || 'passed'}
                                    onChange={formikProps.handleChange}
                                    label="Inspection Status"
                                  >
                                    <MenuItem value="passed">Passed</MenuItem>
                                    <MenuItem value="failed">Failed</MenuItem>
                                    <MenuItem value="pending">Pending Inspection</MenuItem>
                                  </Select>
                                </FormControl>
                              </Grid>

                              <Grid item xs={12} md={6}>
                                <TextField
                                  fullWidth
                                  margin="normal"
                                  id={`items[${index}].inspection_notes`}
                                  name={`items[${index}].inspection_notes`}
                                  label="Inspection Notes"
                                  value={item.inspection_notes || ''}
                                  onChange={formikProps.handleChange}
                                  multiline
                                  rows={2}
                                />
                              </Grid>

                              <Grid item xs={12}>
                                <Typography variant="subtitle2" color="text.secondary" sx={{ mt: 2, mb: 1 }}>
                                  Additional Information
                                </Typography>
                              </Grid>

                              <Grid item xs={12}>
                                <TextField
                                  fullWidth
                                  margin="normal"
                                  id={`items[${index}].remarks`}
                                  name={`items[${index}].remarks`}
                                  label="Remarks"
                                  value={item.remarks || ''}
                                  onChange={formikProps.handleChange}
                                  multiline
                                  rows={2}
                                />
                              </Grid>

                              <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                                {index > 0 && (
                                  <Button
                                    variant="outlined"
                                    color="error"
                                    onClick={() => arrayHelpers.remove(index)}
                                    startIcon={<DeleteIcon />}
                                    sx={{ ml: 1 }}
                                  >
                                    Remove Item
                                  </Button>
                                )}
                              </Grid>
                            </Grid>
                          </Box>
                        ))}
                        <Button
                          variant="contained"
                          color="primary"
                          onClick={() =>
                            arrayHelpers.push({
                              item_master: '',
                              description: '',
                              quantity: '',
                              unit_price: '',
                              serie: '',
                              page_from: '',
                              page_to: '',
                              inspection_status: 'passed',
                              inspection_notes: '',
                            })
                          }
                          startIcon={<AddIcon />}
                        >
                          Add Item
                        </Button>
                      </>
                    )}
                  />
                </Paper>

                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
                  <Button
                    type="submit"
                    variant="contained"
                    color="primary"
                    disabled={submitting}
                    startIcon={submitting ? <CircularProgress size={20} /> : <SaveIcon />}
                    sx={{ minWidth: 150 }}
                  >
                    {submitting ? 'Submitting...' : 'Create Receipt'}
                  </Button>
                </Box>
              </Form>
                )}
              </Formik>
            )}

            {activeStep === 1 && model19Data && (
              <Box>
                <Alert severity="success" sx={{ mb: 3 }}>
                  Model 19 receipt has been created successfully. You can now print the receipt.
                </Alert>

                <Model19Print data={model19Data} />

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
                  <Button onClick={() => setActiveStep(0)} startIcon={<ArrowBackIcon />}>
                    Back
                  </Button>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleFinalize}
                    disabled={submitting}
                    startIcon={submitting ? <CircularProgress size={20} /> : <SaveIcon />}
                  >
                    {submitting ? 'Finalizing...' : 'Finalize Receipt'}
                  </Button>
                </Box>
              </Box>
            )}

            {activeStep === 2 && (
              <Box>
                <Alert severity="success" sx={{ mb: 3 }}>
                  Model 19 receipt has been finalized successfully. The items have been added to inventory.
                </Alert>

                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={() => navigate('/model19-receipts')}
                  >
                    Go to Receipts List
                  </Button>
                </Box>
              </Box>
            )}
          </>
        )}
      </Box>
    </LocalizationProvider>
  );
};

export default Model19Form;
