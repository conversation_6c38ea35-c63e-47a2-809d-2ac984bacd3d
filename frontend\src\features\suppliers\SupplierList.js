import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Typography,
  Chip,
  InputAdornment,
  CircularProgress,
  Grid,
  FormControlLabel,
  Switch,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { getSuppliers, createSupplier, updateSupplier, deleteSupplier } from '../../services/suppliers';

const validationSchema = Yup.object({
  company_name: Yup.string().required('Company name is required'),
  contact_person: Yup.string().required('Contact person is required'),
  email: Yup.string().email('Invalid email address').required('Email is required'),
  phone: Yup.string().required('Phone number is required'),
  country: Yup.string().required('Country is required'),
  tin_number: Yup.string().required('TIN number is required'),
  address: Yup.string().required('Address is required'),
});

const SupplierList = () => {
  const [suppliers, setSuppliers] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [openDialog, setOpenDialog] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [currentSupplier, setCurrentSupplier] = useState(null);
  const [loading, setLoading] = useState(false);
  const { enqueueSnackbar } = useSnackbar();

  const formik = useFormik({
    initialValues: {
      company_name: '',
      contact_person: '',
      email: '',
      phone: '',
      country: '',
      tin_number: '',
      address: '',
      is_active: true,
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        if (currentSupplier) {
          await updateSupplier(currentSupplier.id, values);
          enqueueSnackbar('Supplier updated successfully', { variant: 'success' });
        } else {
          await createSupplier(values);
          enqueueSnackbar('Supplier created successfully', { variant: 'success' });
        }
        handleCloseDialog();
        fetchSuppliers();
      } catch (error) {
        console.error('Error saving supplier:', error);
        console.error('Error response:', error.response?.data);

        let errorMessage = 'Error saving supplier';

        if (error.response?.data) {
          if (error.response.data.detail) {
            errorMessage = error.response.data.detail;
          } else if (error.response.data.message) {
            errorMessage = error.response.data.message;
          } else if (typeof error.response.data === 'object') {
            // Handle field-specific errors
            const fieldErrors = [];
            Object.keys(error.response.data).forEach(field => {
              const fieldError = error.response.data[field];
              if (Array.isArray(fieldError)) {
                fieldErrors.push(`${field}: ${fieldError.join(', ')}`);
              } else {
                fieldErrors.push(`${field}: ${fieldError}`);
              }
            });
            if (fieldErrors.length > 0) {
              errorMessage = fieldErrors.join('; ');
            }
          }
        }

        enqueueSnackbar(errorMessage, { variant: 'error' });
      }
    },
  });

  const fetchSuppliers = async () => {
    setLoading(true);
    try {
      const params = {
        page: page + 1,
        page_size: rowsPerPage,
      };

      if (searchTerm) {
        params.search = searchTerm;
      }

      const response = await getSuppliers(params);

      // Handle different response formats
      if (Array.isArray(response)) {
        setSuppliers(response);
        setTotalCount(response.length);
      } else if (response && response.results && Array.isArray(response.results)) {
        setSuppliers(response.results);
        setTotalCount(response.count || response.results.length);
      } else if (response && Array.isArray(response.data)) {
        setSuppliers(response.data);
        setTotalCount(response.data.length);
      } else {
        console.warn('Unexpected response format:', response);
        setSuppliers([]);
        setTotalCount(0);
      }
    } catch (error) {
      console.error('Error fetching suppliers:', error);
      setSuppliers([]);
      setTotalCount(0);
      enqueueSnackbar('Error fetching suppliers', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSuppliers();
  }, [page, rowsPerPage, searchTerm]);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleOpenDialog = (supplier = null) => {
    if (supplier) {
      setCurrentSupplier(supplier);
      formik.setValues({
        company_name: supplier.company_name,
        contact_person: supplier.contact_person,
        email: supplier.email,
        phone: supplier.phone,
        country: supplier.country,
        tin_number: supplier.tin_number || '',
        address: supplier.address || '',
        is_active: supplier.is_active,
      });
    } else {
      setCurrentSupplier(null);
      formik.resetForm();
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    formik.resetForm();
  };

  const handleOpenDeleteDialog = (supplier) => {
    setCurrentSupplier(supplier);
    setOpenDeleteDialog(true);
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
  };

  const handleDeleteSupplier = async () => {
    try {
      await deleteSupplier(currentSupplier.id);
      enqueueSnackbar('Supplier deleted successfully', { variant: 'success' });
      handleCloseDeleteDialog();
      fetchSuppliers();
    } catch (error) {
      console.error('Error deleting supplier:', error);
      enqueueSnackbar('Error deleting supplier', { variant: 'error' });
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Suppliers
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          Add Supplier
        </Button>
      </Box>

      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <TextField
              label="Search"
              variant="outlined"
              size="small"
              fullWidth
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              sx={{ mr: 2 }}
            />
            <IconButton onClick={fetchSuppliers} color="primary">
              <RefreshIcon />
            </IconButton>
          </Box>
        </CardContent>
      </Card>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Company Name</TableCell>
              <TableCell>Contact Person</TableCell>
              <TableCell>Email</TableCell>
              <TableCell>Phone</TableCell>
              <TableCell>Country</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={7} align="center">
                  <CircularProgress />
                </TableCell>
              </TableRow>
            ) : !suppliers || suppliers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} align="center">
                  No suppliers found
                </TableCell>
              </TableRow>
            ) : (
              (suppliers || []).map((supplier) => (
                <TableRow key={supplier.id}>
                  <TableCell>{supplier.company_name}</TableCell>
                  <TableCell>{supplier.contact_person}</TableCell>
                  <TableCell>{supplier.email}</TableCell>
                  <TableCell>{supplier.phone}</TableCell>
                  <TableCell>{supplier.country}</TableCell>
                  <TableCell>
                    <Chip
                      label={supplier.is_active ? 'Active' : 'Inactive'}
                      color={supplier.is_active ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton
                      color="primary"
                      onClick={() => handleOpenDialog(supplier)}
                      size="small"
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      color="error"
                      onClick={() => handleOpenDeleteDialog(supplier)}
                      size="small"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={totalCount}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </TableContainer>

      {/* Add/Edit Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <form onSubmit={formik.handleSubmit}>
          <DialogTitle>
            {currentSupplier ? 'Edit Supplier' : 'Add Supplier'}
          </DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  id="company_name"
                  name="company_name"
                  label="Company Name"
                  value={formik.values.company_name}
                  onChange={formik.handleChange}
                  error={formik.touched.company_name && Boolean(formik.errors.company_name)}
                  helperText={formik.touched.company_name && formik.errors.company_name}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  id="contact_person"
                  name="contact_person"
                  label="Contact Person"
                  value={formik.values.contact_person}
                  onChange={formik.handleChange}
                  error={formik.touched.contact_person && Boolean(formik.errors.contact_person)}
                  helperText={formik.touched.contact_person && formik.errors.contact_person}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  id="email"
                  name="email"
                  label="Email"
                  value={formik.values.email}
                  onChange={formik.handleChange}
                  error={formik.touched.email && Boolean(formik.errors.email)}
                  helperText={formik.touched.email && formik.errors.email}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  id="phone"
                  name="phone"
                  label="Phone"
                  value={formik.values.phone}
                  onChange={formik.handleChange}
                  error={formik.touched.phone && Boolean(formik.errors.phone)}
                  helperText={formik.touched.phone && formik.errors.phone}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  id="country"
                  name="country"
                  label="Country"
                  value={formik.values.country}
                  onChange={formik.handleChange}
                  error={formik.touched.country && Boolean(formik.errors.country)}
                  helperText={formik.touched.country && formik.errors.country}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  id="tin_number"
                  name="tin_number"
                  label="TIN Number *"
                  value={formik.values.tin_number}
                  onChange={formik.handleChange}
                  error={formik.touched.tin_number && Boolean(formik.errors.tin_number)}
                  helperText={formik.touched.tin_number && formik.errors.tin_number}
                  margin="normal"
                  required
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  id="address"
                  name="address"
                  label="Address *"
                  value={formik.values.address}
                  onChange={formik.handleChange}
                  error={formik.touched.address && Boolean(formik.errors.address)}
                  helperText={formik.touched.address && formik.errors.address}
                  margin="normal"
                  multiline
                  rows={3}
                  required
                />
              </Grid>
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formik.values.is_active}
                      onChange={(e) => formik.setFieldValue('is_active', e.target.checked)}
                      name="is_active"
                      color="primary"
                    />
                  }
                  label="Active"
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Cancel</Button>
            <Button type="submit" variant="contained" color="primary">
              Save
            </Button>
          </DialogActions>
        </form>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={openDeleteDialog} onClose={handleCloseDeleteDialog}>
        <DialogTitle>Delete Supplier</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the supplier "{currentSupplier?.company_name}"? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog}>Cancel</Button>
          <Button onClick={handleDeleteSupplier} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SupplierList;
