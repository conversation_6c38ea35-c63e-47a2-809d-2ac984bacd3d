import api from '../utils/axios';

// Item Entry Request Services
export const getEntryRequests = async (params = {}) => {
  try {
    const response = await api.get('/entry-requests/', { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching entry requests:', error);
    throw error;
  }
};

export const getEntryRequest = async (id) => {
  try {
    const response = await api.get(`/entry-requests/${id}/`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching entry request with id ${id}:`, error);
    throw error;
  }
};

export const createEntryRequest = async (data) => {
  try {
    const response = await api.post('/entry-requests/', data);
    return response.data;
  } catch (error) {
    console.error('Error creating entry request:', error);
    throw error;
  }
};

export const updateEntryRequest = async (id, data) => {
  try {
    const response = await api.put(`/entry-requests/${id}/`, data);
    return response.data;
  } catch (error) {
    console.error(`Error updating entry request with id ${id}:`, error);
    throw error;
  }
};

export const deleteEntryRequest = async (id) => {
  try {
    const response = await api.delete(`/entry-requests/${id}/`);
    return response.data;
  } catch (error) {
    console.error(`Error deleting entry request with id ${id}:`, error);
    throw error;
  }
};

export const approveEntryRequest = async (id, comments = '') => {
  try {
    const response = await api.post(`/entry-requests/${id}/approve/`, { comments });
    return response.data;
  } catch (error) {
    console.error(`Error approving entry request with id ${id}:`, error);
    throw error;
  }
};

export const rejectEntryRequest = async (id, comments = '') => {
  try {
    const response = await api.post(`/entry-requests/${id}/reject/`, { comments });
    return response.data;
  } catch (error) {
    console.error(`Error rejecting entry request with id ${id}:`, error);
    throw error;
  }
};

export const assignToStore = async (id, storeId) => {
  try {
    const response = await api.post(`/entry-requests/${id}/assign_to_store/`, { store_id: storeId });
    return response.data;
  } catch (error) {
    console.error(`Error assigning entry request with id ${id} to store:`, error);
    throw error;
  }
};

export const requestInspection = async (id) => {
  try {
    const response = await api.post(`/entry-requests/${id}/request_inspection/`);
    return response.data;
  } catch (error) {
    console.error(`Error requesting inspection for entry request with id ${id}:`, error);
    throw error;
  }
};

export const generateModel19 = async (id, reference = '') => {
  try {
    const response = await api.post(`/entry-requests/${id}/generate_model19/`, { reference });
    return response.data;
  } catch (error) {
    console.error(`Error generating Model 19 for entry request with id ${id}:`, error);
    throw error;
  }
};

// Item Entry Request Attachment Services
export const getEntryRequestAttachments = async (params = {}) => {
  try {
    const response = await api.get('/entry-request-attachments/', { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching entry request attachments:', error);
    throw error;
  }
};

export const getEntryRequestAttachment = async (id) => {
  try {
    const response = await api.get(`/entry-request-attachments/${id}/`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching entry request attachment with id ${id}:`, error);
    throw error;
  }
};

export const createEntryRequestAttachment = async (data) => {
  try {
    const response = await api.post('/entry-request-attachments/', data);
    return response.data;
  } catch (error) {
    console.error('Error creating entry request attachment:', error);
    throw error;
  }
};

export const updateEntryRequestAttachment = async (id, data) => {
  try {
    const response = await api.put(`/entry-request-attachments/${id}/`, data);
    return response.data;
  } catch (error) {
    console.error(`Error updating entry request attachment with id ${id}:`, error);
    throw error;
  }
};

export const deleteEntryRequestAttachment = async (id) => {
  try {
    const response = await api.delete(`/entry-request-attachments/${id}/`);
    return response.data;
  } catch (error) {
    console.error(`Error deleting entry request attachment with id ${id}:`, error);
    throw error;
  }
};

// Item Entry Request Items Services
export const getItemEntryRequestItems = async (params = {}) => {
  try {
    const response = await api.get('/entry-request-items/', { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching entry request items:', error);
    throw error;
  }
};

export const getItemEntryRequestItem = async (id) => {
  try {
    const response = await api.get(`/entry-request-items/${id}/`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching entry request item with id ${id}:`, error);
    throw error;
  }
};

export const updateInspectionStatus = async (id, data) => {
  try {
    const response = await api.post(`/entry-request-items/${id}/update_inspection_status/`, data);
    return response.data;
  } catch (error) {
    console.error(`Error updating inspection status for item with id ${id}:`, error);
    throw error;
  }
};

export const assignInspector = async (id, committeeId) => {
  try {
    const response = await api.post(`/entry-request-items/${id}/assign_inspector/`, {
      committee_id: committeeId
    });
    return response.data;
  } catch (error) {
    console.error(`Error assigning inspector to item with id ${id}:`, error);
    throw error;
  }
};
