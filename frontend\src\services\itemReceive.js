import api from '../utils/axios';

// Pre-Registration Services
export const getPreRegistrations = async (params = {}) => {
  try {
    const response = await api.get('/pre-registrations/', { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching pre-registrations:', error);
    throw error;
  }
};

export const getPreRegistration = async (id) => {
  try {
    const response = await api.get(`/pre-registrations/${id}/`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching pre-registration with id ${id}:`, error);
    throw error;
  }
};

export const createPreRegistration = async (data) => {
  try {
    const response = await api.post('/pre-registrations/', data);
    return response.data;
  } catch (error) {
    console.error('Error creating pre-registration:', error);
    throw error;
  }
};

export const updatePreRegistration = async (id, data) => {
  try {
    const response = await api.put(`/pre-registrations/${id}/`, data);
    return response.data;
  } catch (error) {
    console.error(`Error updating pre-registration with id ${id}:`, error);
    throw error;
  }
};

export const deletePreRegistration = async (id) => {
  try {
    const response = await api.delete(`/pre-registrations/${id}/`);
    return response.data;
  } catch (error) {
    console.error(`Error deleting pre-registration with id ${id}:`, error);
    throw error;
  }
};

export const approvePreRegistration = async (id, comments = '') => {
  try {
    const response = await api.post(`/pre-registrations/${id}/approve/`, { comments });
    return response.data;
  } catch (error) {
    console.error(`Error approving pre-registration with id ${id}:`, error);
    throw error;
  }
};

export const rejectPreRegistration = async (id, comments = '') => {
  try {
    const response = await api.post(`/pre-registrations/${id}/reject/`, { comments });
    return response.data;
  } catch (error) {
    console.error(`Error rejecting pre-registration with id ${id}:`, error);
    throw error;
  }
};

export const uploadPreRegistrationAttachment = async (id, file) => {
  try {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await api.post(`/pre-registrations/${id}/attachments/`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error) {
    console.error(`Error uploading attachment for pre-registration with id ${id}:`, error);
    throw error;
  }
};

export const getPreRegistrationAttachments = async (id) => {
  try {
    const response = await api.get(`/pre-registrations/${id}/attachments/`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching attachments for pre-registration with id ${id}:`, error);
    throw error;
  }
};

export const deletePreRegistrationAttachment = async (preRegistrationId, attachmentId) => {
  try {
    const response = await api.delete(`/pre-registrations/${preRegistrationId}/attachments/${attachmentId}/`);
    return response.data;
  } catch (error) {
    console.error(`Error deleting attachment ${attachmentId} for pre-registration with id ${preRegistrationId}:`, error);
    throw error;
  }
};

export default {
  getPreRegistrations,
  getPreRegistration,
  createPreRegistration,
  updatePreRegistration,
  deletePreRegistration,
  approvePreRegistration,
  rejectPreRegistration,
  uploadPreRegistrationAttachment,
  getPreRegistrationAttachments,
  deletePreRegistrationAttachment,
};
