import api from '../utils/axios';
import { prepareDropdownData } from '../utils/filters';

// Organization Types
export const getOrganizationTypes = async (params = {}, includeInactive = false) => {
  // If not explicitly requesting inactive items, only get active ones
  if (!includeInactive && !params.is_active) {
    params = { ...params, is_active: true };
  }

  const response = await api.get('/organization-types/', { params });
  return prepareDropdownData(response.data, includeInactive);
};

export const getOrganizationType = async (id) => {
  const response = await api.get(`/organization-types/${id}/`);
  return response.data;
};

export const createOrganizationType = async (data) => {
  const response = await api.post('/organization-types/', data);
  return response.data;
};

export const updateOrganizationType = async (id, data) => {
  const response = await api.put(`/organization-types/${id}/`, data);
  return response.data;
};

export const deleteOrganizationType = async (id) => {
  await api.delete(`/organization-types/${id}/`);
  return true;
};

// Organizations
export const getOrganizations = async (params = {}, includeInactive = false) => {
  // If not explicitly requesting inactive items, only get active ones
  if (!includeInactive && !params.is_active) {
    params = { ...params, is_active: true };
  }

  const response = await api.get('/organizations/', { params });
  return prepareDropdownData(response.data, includeInactive);
};

export const getOrganization = async (id) => {
  const response = await api.get(`/organizations/${id}/`);
  return response.data;
};

export const createOrganization = async (data) => {
  const response = await api.post('/organizations/', data);
  return response.data;
};

export const updateOrganization = async (id, data) => {
  const response = await api.put(`/organizations/${id}/`, data);
  return response.data;
};

export const deleteOrganization = async (id) => {
  await api.delete(`/organizations/${id}/`);
  return true;
};

// Get main organization (first active organization or fallback)
export const getMainOrganization = async () => {
  try {
    const response = await api.get('/organizations/', {
      params: {
        is_active: true,
        limit: 1
      }
    });

    const organizations = response.data.results || response.data;

    if (organizations && organizations.length > 0) {
      return organizations[0];
    }

    // If no organizations found, return fallback data
    return {
      id: null,
      name: 'University of Gondar',
      motto: 'Stock Management System',
      logo_url: '/assets/images/uog-logo.png',
      website: '',
      phone: '',
      email: '',
      is_active: true
    };
  } catch (error) {
    console.error('Error fetching main organization:', error);

    // Return fallback data on error
    return {
      id: null,
      name: 'University of Gondar',
      motto: 'Stock Management System',
      logo_url: '/assets/images/uog-logo.png',
      website: '',
      phone: '',
      email: '',
      is_active: true
    };
  }
};

// Offices
export const getOffices = async (params = {}, includeInactive = false) => {
  // If not explicitly requesting inactive items, only get active ones
  if (!includeInactive && !params.is_active) {
    params = { ...params, is_active: true };
  }

  const response = await api.get('/offices/', { params });
  return prepareDropdownData(response.data, includeInactive);
};

export const getOffice = async (id) => {
  const response = await api.get(`/offices/${id}/`);
  return response.data;
};

export const createOffice = async (data) => {
  const response = await api.post('/offices/', data);
  return response.data;
};

export const updateOffice = async (id, data) => {
  const response = await api.put(`/offices/${id}/`, data);
  return response.data;
};

export const deleteOffice = async (id) => {
  await api.delete(`/offices/${id}/`);
  return true;
};
