import api from '../utils/axios';
import { prepareDropdownData } from '../utils/filters';

// Suppliers
export const getSuppliers = async (params = {}, includeInactive = false) => {
  try {
    // If not explicitly requesting inactive items, only get active ones
    if (!includeInactive && !params.is_active) {
      params = { ...params, is_active: true };
    }

    console.log('Fetching suppliers with params:', params);
    const response = await api.get('/suppliers/', { params });
    console.log('Suppliers fetched successfully:', response.data);

    // Handle different response formats
    let suppliersData;
    if (Array.isArray(response.data)) {
      suppliersData = response.data;
    } else if (response.data.results && Array.isArray(response.data.results)) {
      suppliersData = response.data.results;
    } else if (response.data.data && Array.isArray(response.data.data)) {
      suppliersData = response.data.data;
    } else {
      console.warn('Unexpected data format:', response.data);
      suppliersData = [];
    }

    console.log('Processed suppliers data:', suppliersData);
    return prepareDropdownData(suppliersData, includeInactive);
  } catch (error) {
    console.error('Failed to fetch suppliers:', error);
    return []; // Return empty array instead of throwing to prevent UI errors
  }
};

export const getSupplier = async (id) => {
  try {
    const response = await api.get(`/suppliers/${id}/`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching supplier with ID ${id}:`, error);
    throw error;
  }
};

export const createSupplier = async (data) => {
  try {
    const response = await api.post('/suppliers/', data);
    return response.data;
  } catch (error) {
    console.error('Error creating supplier:', error);
    throw error;
  }
};

export const updateSupplier = async (id, data) => {
  try {
    const response = await api.put(`/suppliers/${id}/`, data);
    return response.data;
  } catch (error) {
    console.error(`Error updating supplier with ID ${id}:`, error);
    throw error;
  }
};

export const deleteSupplier = async (id) => {
  try {
    await api.delete(`/suppliers/${id}/`);
    return true;
  } catch (error) {
    console.error(`Error deleting supplier with ID ${id}:`, error);
    throw error;
  }
};
