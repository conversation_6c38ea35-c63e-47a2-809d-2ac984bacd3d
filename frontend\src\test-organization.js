// Test script to verify getMainOrganization function
import { getMainOrganization } from './services/organizations';

const testOrganizationFunction = async () => {
  console.log('🧪 Testing getMainOrganization function...');
  
  try {
    const organization = await getMainOrganization();
    console.log('✅ Function works! Organization data:', organization);
    
    // Verify required fields
    const requiredFields = ['name', 'motto', 'logo_url'];
    const missingFields = requiredFields.filter(field => !organization[field]);
    
    if (missingFields.length === 0) {
      console.log('✅ All required fields present');
    } else {
      console.log('⚠️ Missing fields:', missingFields);
    }
    
    return true;
  } catch (error) {
    console.error('❌ Function failed:', error);
    return false;
  }
};

// Export for testing
export default testOrganizationFunction;

// If running directly
if (typeof window !== 'undefined') {
  window.testOrganizationFunction = testOrganizationFunction;
  console.log('🔧 Test function available as window.testOrganizationFunction()');
}
