import api from './axios';
import { jwtDecode } from 'jwt-decode';

/**
 * Get the current user's token from localStorage
 *
 * @returns {string|null} - The JWT token or null if not found
 */
export const getToken = () => {
  return localStorage.getItem('token');
};

/**
 * Set the user's token in localStorage
 *
 * @param {string} token - The JWT token to store
 */
export const setToken = (token) => {
  localStorage.setItem('token', token);
};

/**
 * Remove the user's token from localStorage
 */
export const removeToken = () => {
  localStorage.removeItem('token');
};

/**
 * Check if the user is authenticated (has a valid token)
 *
 * @returns {boolean} - True if authenticated, false otherwise
 */
export const isAuthenticated = () => {
  const token = getToken();

  if (!token) {
    return false;
  }

  try {
    const decoded = jwtDecode(token);
    const currentTime = Date.now() / 1000;

    // Check if token is expired
    if (decoded.exp < currentTime) {
      removeToken();
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error decoding token:', error);
    removeToken();
    return false;
  }
};

/**
 * Get the current user's information from the token
 *
 * @returns {Object|null} - User information or null if not authenticated
 */
export const getUserInfo = () => {
  const token = getToken();

  if (!token) {
    return null;
  }

  try {
    const decoded = jwtDecode(token);
    return decoded;
  } catch (error) {
    console.error('Error decoding token:', error);
    return null;
  }
};

/**
 * Check if the current user has a specific role
 *
 * @param {string|string[]} roles - Role or array of roles to check
 * @returns {boolean} - True if user has the role, false otherwise
 */
export const hasRole = (roles) => {
  const userInfo = getUserInfo();

  if (!userInfo || !userInfo.roles) {
    return false;
  }

  if (Array.isArray(roles)) {
    return roles.some(role => userInfo.roles.includes(role));
  }

  return userInfo.roles.includes(roles);
};

/**
 * Check if the current user has a specific permission
 *
 * @param {string|string[]} permissions - Permission or array of permissions to check
 * @returns {boolean} - True if user has the permission, false otherwise
 */
export const hasPermission = (permissions) => {
  const userInfo = getUserInfo();

  if (!userInfo || !userInfo.permissions) {
    return false;
  }

  if (Array.isArray(permissions)) {
    return permissions.some(permission => userInfo.permissions.includes(permission));
  }

  return userInfo.permissions.includes(permissions);
};

/**
 * Authentication service for login, logout, and user management
 */
export const authService = {
  /**
   * Login with username and password
   *
   * @param {Object} credentials - Login credentials
   * @param {string} credentials.username - Username
   * @param {string} credentials.password - Password
   * @returns {Promise} - Promise resolving to the login response
   */
  login: async (credentials) => {
    try {
      const response = await api.post('/auth/token/', credentials);
      const { token } = response.data;

      if (token) {
        setToken(token);
      }

      return response.data;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  },

  /**
   * Logout the current user
   */
  logout: () => {
    removeToken();
    window.location.href = '/login';
  },

  /**
   * Get the current user's profile
   *
   * @returns {Promise} - Promise resolving to the user profile
   */
  getCurrentUser: async () => {
    try {
      const response = await api.get('/auth/user/');
      return response.data;
    } catch (error) {
      console.error('Error fetching current user:', error);
      throw error;
    }
  },

  /**
   * Update the current user's profile
   *
   * @param {Object} userData - Updated user data
   * @returns {Promise} - Promise resolving to the updated user profile
   */
  updateProfile: async (userData) => {
    try {
      const response = await api.put('/auth/user/', userData);
      return response.data;
    } catch (error) {
      console.error('Error updating profile:', error);
      throw error;
    }
  },

  /**
   * Change the current user's password
   *
   * @param {Object} passwordData - Password change data
   * @param {string} passwordData.old_password - Current password
   * @param {string} passwordData.new_password - New password
   * @returns {Promise} - Promise resolving to the password change response
   */
  changePassword: async (passwordData) => {
    try {
      const response = await api.post('/auth/change-password/', passwordData);
      return response.data;
    } catch (error) {
      console.error('Error changing password:', error);
      throw error;
    }
  },
};

// Create a named export object to satisfy ESLint
const authUtils = {
  getToken,
  setToken,
  removeToken,
  isAuthenticated,
  getUserInfo,
  hasRole,
  hasPermission,
  authService,
};

export default authUtils;
