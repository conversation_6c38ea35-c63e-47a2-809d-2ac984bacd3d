/**
 * Global error handler for browser extension and other errors
 */

// Filter out browser extension errors that we can't control
const isBrowserExtensionError = (error) => {
  if (!error) return false;
  
  const message = error.message || '';
  const stack = error.stack || '';
  
  return (
    message.includes('message channel closed') ||
    message.includes('Extension context invalidated') ||
    message.includes('chrome-extension://') ||
    message.includes('moz-extension://') ||
    stack.includes('chrome-extension://') ||
    stack.includes('moz-extension://') ||
    message.includes('listener indicated an asynchronous response')
  );
};

// Global error handler
const setupGlobalErrorHandler = () => {
  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    if (isBrowserExtensionError(event.reason)) {
      console.warn('Browser extension error ignored:', event.reason?.message || event.reason);
      event.preventDefault(); // Prevent the error from being logged to console
      return;
    }
    
    // Log other errors normally
    console.error('Unhandled promise rejection:', event.reason);
  });

  // Handle general errors
  window.addEventListener('error', (event) => {
    if (isBrowserExtensionError(event.error)) {
      console.warn('Browser extension error ignored:', event.error?.message || event.error);
      event.preventDefault(); // Prevent the error from being logged to console
      return;
    }
    
    // Log other errors normally
    console.error('Global error:', event.error);
  });

  // Override console.error to filter extension errors
  const originalConsoleError = console.error;
  console.error = (...args) => {
    const errorMessage = args.join(' ');
    
    if (
      errorMessage.includes('message channel closed') ||
      errorMessage.includes('Extension context invalidated') ||
      errorMessage.includes('chrome-extension://') ||
      errorMessage.includes('moz-extension://') ||
      errorMessage.includes('listener indicated an asynchronous response')
    ) {
      // Silently ignore browser extension errors
      return;
    }
    
    // Log other errors normally
    originalConsoleError.apply(console, args);
  };
};

// Initialize error handling
export const initializeErrorHandling = () => {
  setupGlobalErrorHandler();
  
  console.log('✅ Global error handling initialized');
  console.log('🔇 Browser extension errors will be silently ignored');
};

export default {
  initializeErrorHandling,
  isBrowserExtensionError,
};
