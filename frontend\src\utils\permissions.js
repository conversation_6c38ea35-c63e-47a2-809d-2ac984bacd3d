/**
 * Utility functions for checking user permissions based on roles
 */

// Role definitions
export const ROLE_PAO = 'PAO';  // Property Administration Officer
export const ROLE_CENTRAL_PAO = 'Central PAO';  // Central Property Administration Officer
export const ROLE_STOREKEEPER = 'Storekeeper';
export const ROLE_STOCK_CLERK = 'Stock Clerk';
export const ROLE_PROCUREMENT = 'Procurement';
export const ROLE_USER_DEPT = 'User Department';

// Group to role mapping
const GROUP_TO_ROLE = {
  'Admin': ROLE_CENTRAL_PAO,
  'Inventory Manager': ROLE_PAO,
  'Central PAO': ROL<PERSON>_CENTRAL_PAO,
  'Storekeeper': R<PERSON><PERSON>_STOREKEEPER,
  'Stock Clerk': ROLE_STOCK_CLERK,
  'Procurement': ROLE_PROCUREMENT,
  'User Department': ROLE_USER_DEPT,
};

/**
 * Get user roles based on their groups
 *
 * @param {Object} user - The user object from the authentication service
 * @returns {Array} - Array of role names
 */
export const getUserRoles = (user) => {
  if (!user) return [];

  const roles = [];

  // Superusers are considered Central PAO by default
  if (user.is_superuser) {
    roles.push(ROLE_CENTRAL_PAO);
  }
  // Staff users are considered PAO by default
  else if (user.is_staff) {
    roles.push(ROLE_PAO);
  }

  // Add roles based on user's groups
  if (user.groups && Array.isArray(user.groups)) {
    user.groups.forEach(group => {
      const role = GROUP_TO_ROLE[group];
      if (role && !roles.includes(role)) {
        roles.push(role);
      }
    });
  }

  return roles;
};

/**
 * Check if a user has a specific role
 *
 * @param {Object} user - The user object from the authentication service
 * @param {String} role - The role to check
 * @returns {Boolean} - True if the user has the role
 */
export const hasRole = (user, role) => {
  const roles = getUserRoles(user);
  return roles.includes(role);
};

/**
 * Check if a user can submit requisitions
 *
 * @param {Object} user - The user object from the authentication service
 * @returns {Boolean} - True if the user can submit requisitions
 */
export const canSubmitRequisition = (user) => {
  return hasRole(user, ROLE_USER_DEPT) || hasRole(user, ROLE_PAO);
};

/**
 * Check if a user can approve requisitions
 *
 * @param {Object} user - The user object from the authentication service
 * @param {Object} requisition - The requisition object
 * @returns {Boolean} - True if the user can approve requisitions
 */
export const canApproveRequisition = (user, requisition) => {
  // PAO can approve any requisition that doesn't require cross-store transfer
  if (hasRole(user, ROLE_PAO)) {
    if (requisition && requisition.status_details) {
      return requisition.status_details.name.toLowerCase() !== 'pending central pao approval';
    }
    return true;
  }

  // User Department can only approve requisitions that were adjusted
  if (hasRole(user, ROLE_USER_DEPT)) {
    if (requisition && requisition.status_details) {
      return requisition.status_details.name.toLowerCase() === 'adjusted - awaiting confirmation';
    }
  }

  return false;
};

/**
 * Check if a user can approve cross-store transfers
 *
 * @param {Object} user - The user object from the authentication service
 * @param {Object} requisition - The requisition object
 * @returns {Boolean} - True if the user can approve cross-store transfers
 */
export const canApproveCrossStore = (user, requisition) => {
  // Only Central PAO can approve cross-store transfers
  if (hasRole(user, ROLE_CENTRAL_PAO)) {
    if (requisition && requisition.status_details) {
      return requisition.status_details.name.toLowerCase() === 'pending central pao approval';
    }
  }

  return false;
};

/**
 * Check if a user can adjust requisitions
 *
 * @param {Object} user - The user object from the authentication service
 * @param {Object} requisition - The requisition object
 * @returns {Boolean} - True if the user can adjust requisitions
 */
export const canAdjustRequisition = (user, requisition) => {
  // Only PAO can adjust requisitions
  if (!hasRole(user, ROLE_PAO)) {
    return false;
  }

  // Can only adjust pending requisitions
  if (requisition && requisition.status_details) {
    return requisition.status_details.name.toLowerCase() === 'pending approval';
  }

  return false;
};

/**
 * Check if a user can receive stocks
 *
 * @param {Object} user - The user object from the authentication service
 * @returns {Boolean} - True if the user can receive stocks
 */
export const canReceiveStocks = (user) => {
  return hasRole(user, ROLE_STOREKEEPER) || hasRole(user, ROLE_PROCUREMENT);
};

/**
 * Check if a user can issue stocks
 *
 * @param {Object} user - The user object from the authentication service
 * @returns {Boolean} - True if the user can issue stocks
 */
export const canIssueStocks = (user) => {
  return hasRole(user, ROLE_STOREKEEPER);
};

/**
 * Check if a user can update bin cards
 *
 * @param {Object} user - The user object from the authentication service
 * @returns {Boolean} - True if the user can update bin cards
 */
export const canUpdateBinCards = (user) => {
  return hasRole(user, ROLE_STOREKEEPER);
};

/**
 * Check if a user can update stock records
 *
 * @param {Object} user - The user object from the authentication service
 * @returns {Boolean} - True if the user can update stock records
 */
export const canUpdateStockRecords = (user) => {
  return hasRole(user, ROLE_STOCK_CLERK);
};

/**
 * Check if a user can initiate disposal
 *
 * @param {Object} user - The user object from the authentication service
 * @returns {Boolean} - True if the user can initiate disposal
 */
export const canInitiateDisposal = (user) => {
  return hasRole(user, ROLE_PAO);
};

/**
 * Check if a user can conduct stock audit
 *
 * @param {Object} user - The user object from the authentication service
 * @returns {Boolean} - True if the user can conduct stock audit
 */
export const canConductStockAudit = (user) => {
  return hasRole(user, ROLE_PAO) || hasRole(user, ROLE_STOCK_CLERK);
};

/**
 * Check if a user can assist in stock audit
 *
 * @param {Object} user - The user object from the authentication service
 * @returns {Boolean} - True if the user can assist in stock audit
 */
export const canAssistStockAudit = (user) => {
  return hasRole(user, ROLE_STOREKEEPER);
};

/**
 * Get menu items visibility based on user roles
 *
 * @param {Object} user - The user object from the authentication service
 * @returns {Object} - Object with menu categories as keys and boolean values
 */
export const getMenuVisibility = (user) => {
  const roles = getUserRoles(user);
  const isPAO = roles.includes(ROLE_PAO);
  const isStorekeeper = roles.includes(ROLE_STOREKEEPER);
  const isStockClerk = roles.includes(ROLE_STOCK_CLERK);
  const isProcurement = roles.includes(ROLE_PROCUREMENT);
  const isUserDept = roles.includes(ROLE_USER_DEPT);

  return {
    // Dashboard is visible to all authenticated users
    dashboard: true,

    // Organization management is only for PAO
    organization: isPAO,

    // Classification management is only for PAO
    classification: isPAO,

    // Specification management is only for PAO
    specification: isPAO,

    // Storage management is for PAO and Storekeeper
    storage: isPAO || isStorekeeper,

    // Supplier management is for PAO and Procurement
    supplier: isPAO || isProcurement,

    // Status management is only for PAO
    status: isPAO,

    // Inventory management varies by role
    inventory: true, // All roles need some inventory access

    // Item masters management is for PAO, Stock Clerk, and Procurement
    itemMasters: isPAO || isStockClerk || isProcurement,

    // Batches management is for PAO, Storekeeper, Stock Clerk, and Procurement
    batches: isPAO || isStorekeeper || isStockClerk || isProcurement,

    // Items management is for PAO, Storekeeper, and Stock Clerk
    items: isPAO || isStorekeeper || isStockClerk,

    // Gate passes are for PAO and Storekeeper
    gatePasses: isPAO || isStorekeeper,

    // Requisition management is for all roles but with different permissions
    requisition: true,

    // Requisition statuses management is only for PAO
    requisitionStatuses: isPAO,

    // Reports are for PAO and Stock Clerk
    reports: isPAO || isStockClerk,

    // Receiving & Inspection is for PAO, Storekeeper, and Procurement
    receiving: isPAO || isStorekeeper || isProcurement,

    // Item Receive (Pre-Registration) is for PAO and Procurement
    itemReceive: isPAO || isProcurement,
  };
};
